# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

Word Learning App v5 是一个基于 Flask 的英语单词学习应用，采用模块化架构设计，支持个性化学习路径、智能复习计划和激励系统。

## 核心架构

### 技术栈
- **后端**: Flask 3.0.3 + Python 3.13
- **数据库**: SQLite (通过 SQLAlchemy ORM)
- **前端**: 原生 JavaScript + 响应式设计
- **生产服务器**: Gunicorn
- **容器化**: Docker + Docker Compose

### 项目结构
```
word_learning_app_v5_prod/
├── app.py                 # 主应用入口
├── src/                   # 核心源代码
│   ├── models/           # 数据模型
│   ├── services/         # 业务逻辑服务
│   ├── routes/           # 路由定义
│   ├── core/             # 核心功能（日志、性能、错误处理）
│   └── config/           # 配置管理
├── static/               # 静态资源
│   ├── js/               # JavaScript 文件
│   ├── css/              # 样式文件
│   └── cache/            # 缓存文件
└── instance/             # 数据库文件（.gitignore）
```

## 常用开发命令

### 本地开发
```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # macOS/Linux

# 安装依赖
pip install -r requirements.txt

# 运行开发服务器
python app.py
# 或
flask run --host=0.0.0.0 --port=5005

# 运行测试（如果有测试文件）
pytest
```

### Docker 部署

#### 本地 Docker 部署
```bash
# 构建镜像
docker build -t word-learning-app-v5 .

# 使用 Docker Compose 运行
docker-compose up -d

# 查看日志
docker-compose logs -f
```

#### QNAP NAS 自动部署 (AMD64)
```bash
# 🚀 一键自动部署（推荐）
./auto-deploy.sh

# ⚡ 快速部署（简化版）
./quick-deploy.sh

# 仅构建镜像
./auto-deploy.sh --build-only

# 仅部署到 QNAP
./auto-deploy.sh --deploy-only

# 跳过部署验证
./auto-deploy.sh --no-verify
```

#### 手动部署到 QNAP
```bash
# 1. 构建 AMD64 镜像
docker build --platform linux/amd64 -f Dockerfile.cn-mirror -t word_learning_app_v5_amd64:latest .

# 2. 保存并传输镜像
docker save word_learning_app_v5_amd64:latest > word_learning_app_v5_amd64_latest.tar
scp word_learning_app_v5_amd64_latest.tar lesong@*************:/tmp/

# 3. 在 QNAP 上部署
ssh lesong@*************
docker load < /tmp/word_learning_app_v5_amd64_latest.tar
docker stop word_learning_app_v5 2>/dev/null || true
docker rm word_learning_app_v5 2>/dev/null || true
docker run -d --name word_learning_app_v5 \
  -p 5003:5005 \
  -v /share/homes/lesong/word_learning_app_v5/instance:/app/instance \
  -v /share/homes/lesong/word_learning_app_v5/static:/app/static \
  --restart unless-stopped \
  word_learning_app_v5_amd64:latest
```

#### 部署要点
- **镜像架构**: 必须使用 `--platform linux/amd64` 构建 AMD64 镜像
- **Dockerfile**: 使用 `Dockerfile.cn-mirror` 解决网络问题
- **数据持久化**: 挂载 `instance/` 和 `static/` 目录
- **访问地址**: http://*************:5003
- **SSH 配置**: 确保已配置 SSH 密钥认证

### 数据库操作
```bash
# 数据库位置：instance/words.db
# 使用 SQLite 客户端查看
sqlite3 instance/words.db

# 常用查询
.tables  # 查看所有表
.schema user  # 查看表结构
```

## 关键业务逻辑

### 1. 学习计划生成 (src/services/learning/plan_service.py)  
- 每日生成30个单词：10个新词 + 20个复习词
- 基于熟练度(proficiency)选择复习词
- 支持新词不足时的智能补充机制
- 单词数量配置化管理，通过src/config/config.py统一配置

### 2. 熟练度计算 (src/services/proficiency/calculator.py)
- 优化权重分配：正确率(60%) + 学习间隔(25%) + 回答时间(15%)
- 正确率为最重要指标，回答时间权重较低避免过度惩罚
- 实时更新，每次答题后立即计算

### 3. 激励系统 (src/services/voucher_service.py)
- 积分系统：答对+10分，答错-20分
- 购物券系统：连击奖励、优秀表现奖励
- 每日限制：最多获得2张购物券

### 4. 状态管理 (src/services/learning/status_service.py)
- 三种状态：new(新词)、review(复习)、attention(生词本)
- 基于proficiency自动转换状态
- 实时更新机制确保数据一致性

## 重要配置

### 环境变量
```bash
FLASK_ENV=production/development
FLASK_DEBUG=true/false
FLASK_PORT=5005
DATABASE_URL=/path/to/words.db
LOG_LEVEL=INFO/DEBUG
```

### 数据库模型关系
- user ← user_word → word
- user ← word_record → word
- user ← learning_plan → word
- user ← voucher_log

## 开发注意事项

1. **数据一致性**: 使用事务确保 user_word 和 word_record 数据同步
2. **性能优化**: 利用 memory_cache 缓存频繁查询的数据
3. **错误处理**: 所有路由都有对应的错误处理器
4. **日志记录**: 使用 structlog 进行结构化日志记录
5. **安全性**: 
   - 密码使用 bcrypt 加密
   - 所有用户输入都需要验证
   - 使用 CSRF 保护（Flask 内置）

## 调试技巧

### 查看实时日志
```python
# 在关键位置添加日志
from src.core import get_logger
logger = get_logger(__name__)
logger.info("操作描述", user_id=user_id, word_id=word_id)
```

### 性能监控
- 访问 /api/system/performance (需要管理员权限)
- 查看缓存命中率、请求统计等

### 数据一致性检查
- 使用 src/services/monitoring/consistency_monitor.py
- 定期运行数据一致性检查

## 部署检查清单

- [ ] 确保 requirements.txt 包含所有依赖
- [ ] 设置正确的环境变量
- [ ] 创建必要的目录：instance/, static/exports/, static/cache/
- [ ] 配置正确的数据库路径
- [ ] 使用 Gunicorn 而非 Flask 开发服务器
- [ ] 配置健康检查端点：/health
- [ ] 设置适当的资源限制（CPU、内存）

## 相关文档

- data_model.md - 详细的数据模型设计
- DOCKER.md - Docker 部署指南
- QNAP_DEPLOYMENT.md - QNAP NAS 部署指南
- DEPENDENCIES.md - 依赖包说明