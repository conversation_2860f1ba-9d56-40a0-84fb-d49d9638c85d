<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试改进后的免复习功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .improvement-list {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .improvement-list h3 {
            color: #2e7d32;
            margin-top: 0;
        }
        .improvement-list ul {
            margin: 10px 0;
        }
        .improvement-list li {
            margin: 8px 0;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 改进后的免复习功能测试</h1>
        <p>点击下面的按钮测试全新设计的免复习功能：</p>
        
        <button class="test-button" onclick="testSkipReviewModal()">
            🚀 测试改进后的免复习选择
        </button>
        
        <div class="improvement-list">
            <h3>🎉 主要改进内容：</h3>
            <ul>
                <li><strong>🎛️ 操作控制栏优化</strong>：快速选择改为下拉菜单，与免复习天数并列显示</li>
                <li><strong>📱 网格布局</strong>：单词列表改为网格布局，每行显示多个单词，减少滚动</li>
                <li><strong>✅ 确认流程改进</strong>：设置成功后显示详细信息，用户必须手动确认才关闭</li>
                <li><strong>❌ 错误处理完善</strong>：失败时显示具体错误信息，按钮状态正确恢复</li>
                <li><strong>🎨 视觉优化</strong>：更美观的卡片设计，更清晰的信息层次</li>
            </ul>
        </div>
        
        <div id="test-results" style="margin-top: 20px; padding: 15px; background: #f9f9f9; border-radius: 6px;">
            <h3>🧪 测试要点：</h3>
            <ul>
                <li>✅ 快速选择下拉菜单是否工作正常</li>
                <li>✅ 单词网格布局是否美观易用</li>
                <li>✅ 设置成功后是否显示详细信息</li>
                <li>✅ 用户是否需要手动确认才能关闭</li>
                <li>✅ 错误情况下按钮状态是否正确恢复</li>
            </ul>
        </div>
    </div>

    <!-- 改进后的免复习选择模态框 -->
    <div id="skipReviewModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🎯 选择免复习词汇</h2>
                <span class="close" onclick="closeSkipReviewModal()">&times;</span>
            </div>
            
            <div class="modal-body">
                <!-- 消息显示区域 -->
                <div id="skipReviewMessage" class="skip-review-message" style="display: none;"></div>
                
                <!-- 学习日期显示 -->
                <div class="date-info">
                    <p>📅 学习日期：<span id="learningDate">2025-08-02</span></p>
                    <p>📊 今日学习：<span id="totalWordsCount">8</span>个单词，推荐免复习：<span id="recommendCount">5</span>个</p>
                </div>
                
                <!-- 操作控制栏 -->
                <div class="control-row">
                    <div class="quick-select-group">
                        <label for="quickSelect">快速选择：</label>
                        <select id="quickSelect" onchange="handleQuickSelect(this.value)">
                            <option value="">请选择...</option>
                            <option value="recommended">⭐ 推荐词汇</option>
                            <option value="proficiency90">🏆 熟练度≥90%</option>
                            <option value="proficiency80">✨ 熟练度≥80%</option>
                            <option value="all">📝 全选</option>
                            <option value="clear">🔄 清空选择</option>
                        </select>
                    </div>
                    
                    <div class="skip-duration-group">
                        <label for="skipDays">免复习天数：</label>
                        <select id="skipDays">
                            <option value="3">3天</option>
                            <option value="7" selected>7天（推荐）</option>
                            <option value="14">14天</option>
                            <option value="30">30天</option>
                        </select>
                    </div>
                </div>
                
                <!-- 模拟单词网格列表 -->
                <div class="words-container">
                    <div id="wordsList" class="words-list">
                        <div class="word-item selected">
                            <input type="checkbox" class="word-checkbox" id="word_1" checked onclick="toggleWordSelection(1)">
                            <div class="word-info">
                                <div class="word-main">
                                    <div class="word-english">apple</div>
                                    <div class="word-chinese">苹果</div>
                                </div>
                                <div class="word-stats">
                                    <div class="proficiency high">95.0%</div>
                                    <div class="today-performance">今日: 3/3</div>
                                </div>
                            </div>
                        </div>
                        <div class="word-item selected">
                            <input type="checkbox" class="word-checkbox" id="word_2" checked onclick="toggleWordSelection(2)">
                            <div class="word-info">
                                <div class="word-main">
                                    <div class="word-english">book</div>
                                    <div class="word-chinese">书</div>
                                </div>
                                <div class="word-stats">
                                    <div class="proficiency high">88.5%</div>
                                    <div class="today-performance">今日: 2/2</div>
                                </div>
                            </div>
                        </div>
                        <div class="word-item selected">
                            <input type="checkbox" class="word-checkbox" id="word_3" checked onclick="toggleWordSelection(3)">
                            <div class="word-info">
                                <div class="word-main">
                                    <div class="word-english">computer</div>
                                    <div class="word-chinese">电脑</div>
                                </div>
                                <div class="word-stats">
                                    <div class="proficiency high">92.3%</div>
                                    <div class="today-performance">今日: 4/4</div>
                                </div>
                            </div>
                        </div>
                        <div class="word-item">
                            <input type="checkbox" class="word-checkbox" id="word_4" onclick="toggleWordSelection(4)">
                            <div class="word-info">
                                <div class="word-main">
                                    <div class="word-english">house</div>
                                    <div class="word-chinese">房子</div>
                                </div>
                                <div class="word-stats">
                                    <div class="proficiency medium">75.2%</div>
                                    <div class="today-performance">今日: 2/3</div>
                                </div>
                            </div>
                        </div>
                        <div class="word-item">
                            <input type="checkbox" class="word-checkbox" id="word_5" onclick="toggleWordSelection(5)">
                            <div class="word-info">
                                <div class="word-main">
                                    <div class="word-english">water</div>
                                    <div class="word-chinese">水</div>
                                </div>
                                <div class="word-stats">
                                    <div class="proficiency high">89.1%</div>
                                    <div class="today-performance">今日: 3/3</div>
                                </div>
                            </div>
                        </div>
                        <div class="word-item">
                            <input type="checkbox" class="word-checkbox" id="word_6" onclick="toggleWordSelection(6)">
                            <div class="word-info">
                                <div class="word-main">
                                    <div class="word-english">school</div>
                                    <div class="word-chinese">学校</div>
                                </div>
                                <div class="word-stats">
                                    <div class="proficiency medium">68.7%</div>
                                    <div class="today-performance">今日: 1/2</div>
                                </div>
                            </div>
                        </div>
                        <div class="word-item">
                            <input type="checkbox" class="word-checkbox" id="word_7" onclick="toggleWordSelection(7)">
                            <div class="word-info">
                                <div class="word-main">
                                    <div class="word-english">friend</div>
                                    <div class="word-chinese">朋友</div>
                                </div>
                                <div class="word-stats">
                                    <div class="proficiency high">91.4%</div>
                                    <div class="today-performance">今日: 2/2</div>
                                </div>
                            </div>
                        </div>
                        <div class="word-item">
                            <input type="checkbox" class="word-checkbox" id="word_8" onclick="toggleWordSelection(8)">
                            <div class="word-info">
                                <div class="word-main">
                                    <div class="word-english">happy</div>
                                    <div class="word-chinese">快乐的</div>
                                </div>
                                <div class="word-stats">
                                    <div class="proficiency medium">72.8%</div>
                                    <div class="today-performance">今日: 1/3</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="modal-footer">
                <div class="selection-info">
                    已选择：<span id="selectedCount">3</span>个单词
                </div>
                
                <div class="action-buttons">
                    <button type="button" class="btn btn-secondary" onclick="closeSkipReviewModal()">
                        取消
                    </button>
                    <button type="button" class="btn btn-primary" onclick="testConfirmSkipReview()" id="confirmBtn">
                        确认设置免复习
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 3% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 85vh;
            overflow-y: auto;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .modal-header h2 {
            margin: 0;
            font-size: 1.5rem;
        }

        .modal-body {
            padding: 20px 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
            border-radius: 0 0 12px 12px;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            opacity: 0.8;
        }

        .close:hover {
            opacity: 1;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        /* 日期信息 */
        .date-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }

        .date-info p {
            margin: 5px 0;
            font-size: 14px;
        }

        /* 操作控制栏 */
        .control-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            gap: 20px;
        }

        .quick-select-group,
        .skip-duration-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .quick-select-group label,
        .skip-duration-group label {
            font-weight: 500;
            color: #495057;
            white-space: nowrap;
            font-size: 14px;
        }

        .quick-select-group select,
        .skip-duration-group select {
            padding: 8px 12px;
            border: 2px solid #dee2e6;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 150px;
        }

        .quick-select-group select:focus,
        .skip-duration-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .quick-select-group select:hover,
        .skip-duration-group select:hover {
            border-color: #667eea;
        }

        /* 单词列表容器 */
        .words-container {
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            background: #fafafa;
        }

        .words-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 12px;
        }

        /* 单词卡片 */
        .word-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 8px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: white;
            transition: all 0.2s;
            cursor: pointer;
            text-align: center;
            min-height: 100px;
            position: relative;
        }

        .word-item:hover {
            background: #f8f9fa;
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }

        .word-item.selected {
            background: #e7f3ff;
            border-color: #667eea;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.2);
        }

        /* 复选框 */
        .word-checkbox {
            position: absolute;
            top: 8px;
            left: 8px;
            transform: scale(1.3);
            cursor: pointer;
        }

        /* 单词信息 */
        .word-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            width: 100%;
            margin-top: 20px;
        }

        .word-main {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .word-english {
            font-weight: 700;
            color: #212529;
            font-size: 16px;
            line-height: 1.2;
        }

        .word-chinese {
            color: #6c757d;
            font-size: 13px;
            line-height: 1.2;
        }

        .word-stats {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            font-size: 11px;
            color: #6c757d;
        }

        .proficiency {
            font-weight: 600;
            padding: 3px 8px;
            border-radius: 12px;
            color: white;
            font-size: 10px;
            white-space: nowrap;
        }

        .proficiency.high {
            background: #28a745;
        }

        .proficiency.medium {
            background: #ffc107;
            color: #212529;
        }

        /* 消息显示样式 */
        .skip-review-message {
            margin-bottom: 15px;
            padding: 16px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            text-align: left;
            animation: messageSlideIn 0.3s ease-out;
            white-space: pre-line;
            line-height: 1.5;
            max-height: 300px;
            overflow-y: auto;
        }

        .skip-review-message.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #c8e6c8;
            box-shadow: 0 2px 8px rgba(46, 125, 50, 0.1);
        }

        .skip-review-message.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #ffcdd2;
            box-shadow: 0 2px 8px rgba(198, 40, 40, 0.1);
        }

        .skip-review-message.warning {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ffcc02;
            box-shadow: 0 2px 8px rgba(245, 124, 0, 0.1);
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -100%) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>

    <script>
        // 模拟数据
        let todayWords = [
            {word_id: 1, english_word: 'apple', chinese_meaning: '苹果', proficiency: 95.0, recommend_skip: true},
            {word_id: 2, english_word: 'book', chinese_meaning: '书', proficiency: 88.5, recommend_skip: true},
            {word_id: 3, english_word: 'computer', chinese_meaning: '电脑', proficiency: 92.3, recommend_skip: true},
            {word_id: 4, english_word: 'house', chinese_meaning: '房子', proficiency: 75.2, recommend_skip: false},
            {word_id: 5, english_word: 'water', chinese_meaning: '水', proficiency: 89.1, recommend_skip: true},
            {word_id: 6, english_word: 'school', chinese_meaning: '学校', proficiency: 68.7, recommend_skip: false},
            {word_id: 7, english_word: 'friend', chinese_meaning: '朋友', proficiency: 91.4, recommend_skip: true},
            {word_id: 8, english_word: 'happy', chinese_meaning: '快乐的', proficiency: 72.8, recommend_skip: false}
        ];
        let selectedWordIds = new Set([1, 2, 3]);

        function testSkipReviewModal() {
            document.getElementById('skipReviewModal').style.display = 'block';
        }

        function closeSkipReviewModal() {
            document.getElementById('skipReviewModal').style.display = 'none';

            // 重置消息显示
            const messageElement = document.getElementById('skipReviewMessage');
            if (messageElement) {
                messageElement.style.display = 'none';
                messageElement.className = 'skip-review-message';
                messageElement.textContent = '';
            }

            // 重置确认按钮
            const confirmBtn = document.getElementById('confirmBtn');
            if (confirmBtn) {
                confirmBtn.textContent = '确认设置免复习';
                confirmBtn.style.background = '';
                confirmBtn.style.color = '';
                confirmBtn.style.opacity = '1';
                confirmBtn.disabled = false;
                confirmBtn.onclick = function() { testConfirmSkipReview(); };
            }

            // 重置快速选择下拉菜单
            document.getElementById('quickSelect').value = '';
        }

        function toggleWordSelection(wordId) {
            const checkbox = document.getElementById(`word_${wordId}`);
            const wordItem = checkbox.closest('.word-item');

            if (checkbox.checked) {
                selectedWordIds.add(wordId);
                wordItem.classList.add('selected');
            } else {
                selectedWordIds.delete(wordId);
                wordItem.classList.remove('selected');
            }

            updateSelectionCount();
        }

        function updateSelectionCount() {
            document.getElementById('selectedCount').textContent = selectedWordIds.size;
        }

        // 处理快速选择下拉菜单
        function handleQuickSelect(value) {
            if (!value) return;

            switch(value) {
                case 'recommended':
                    selectRecommended();
                    showMessage('✅ 已选择推荐免复习的词汇', 'success');
                    break;
                case 'proficiency90':
                    selectByProficiency(90);
                    showMessage('✅ 已选择熟练度≥90%的词汇', 'success');
                    break;
                case 'proficiency80':
                    selectByProficiency(80);
                    showMessage('✅ 已选择熟练度≥80%的词汇', 'success');
                    break;
                case 'all':
                    selectAll();
                    showMessage('✅ 已选择所有词汇', 'success');
                    break;
                case 'clear':
                    clearSelection();
                    showMessage('🔄 已清空选择', 'info');
                    break;
            }

            // 重置下拉菜单
            document.getElementById('quickSelect').value = '';
        }

        function selectRecommended() {
            clearSelection();
            todayWords.forEach(word => {
                if (word.recommend_skip) {
                    selectedWordIds.add(word.word_id);
                    const checkbox = document.getElementById(`word_${word.word_id}`);
                    const wordItem = checkbox.closest('.word-item');
                    checkbox.checked = true;
                    wordItem.classList.add('selected');
                }
            });
            updateSelectionCount();
        }

        function selectByProficiency(threshold) {
            clearSelection();
            todayWords.forEach(word => {
                if (word.proficiency >= threshold) {
                    selectedWordIds.add(word.word_id);
                    const checkbox = document.getElementById(`word_${word.word_id}`);
                    const wordItem = checkbox.closest('.word-item');
                    checkbox.checked = true;
                    wordItem.classList.add('selected');
                }
            });
            updateSelectionCount();
        }

        function selectAll() {
            clearSelection();
            todayWords.forEach(word => {
                selectedWordIds.add(word.word_id);
                const checkbox = document.getElementById(`word_${word.word_id}`);
                const wordItem = checkbox.closest('.word-item');
                checkbox.checked = true;
                wordItem.classList.add('selected');
            });
            updateSelectionCount();
        }

        function clearSelection() {
            selectedWordIds.clear();
            todayWords.forEach(word => {
                const checkbox = document.getElementById(`word_${word.word_id}`);
                const wordItem = checkbox.closest('.word-item');
                checkbox.checked = false;
                wordItem.classList.remove('selected');
            });
            updateSelectionCount();
        }

        // 显示消息函数
        function showMessage(message, type = 'info') {
            const messageElement = document.getElementById('skipReviewMessage');
            if (!messageElement) return;

            messageElement.className = 'skip-review-message';
            if (type === 'success' || type === 'error' || type === 'warning') {
                messageElement.classList.add(type);
            }

            messageElement.textContent = message;
            messageElement.style.display = 'block';

            const hideDelay = type === 'success' ? 3000 : (type === 'error' ? 5000 : 4000);
            setTimeout(() => {
                if (messageElement.style.display !== 'none') {
                    messageElement.style.display = 'none';
                }
            }, hideDelay);
        }

        // 创建临时全局消息提示
        function createTemporaryMessage(message, type) {
            const tempMessage = document.createElement('div');
            tempMessage.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                background: #e8f5e8;
                color: #2e7d32;
                border: 1px solid #c8e6c8;
                animation: slideInRight 0.3s ease-out;
            `;

            tempMessage.textContent = message;
            document.body.appendChild(tempMessage);

            setTimeout(() => {
                if (tempMessage.parentNode) {
                    tempMessage.remove();
                }
            }, 3000);
        }

        // 显示详细的成功信息
        function showDetailedSuccessMessage(result, skipDays) {
            const selectedWords = todayWords.filter(word => selectedWordIds.has(word.word_id));

            const skipUntilDate = new Date();
            skipUntilDate.setDate(skipUntilDate.getDate() + skipDays);
            const skipUntilStr = skipUntilDate.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            let detailMessage = `🎉 免复习设置成功！\n\n`;
            detailMessage += `📊 设置详情：\n`;
            detailMessage += `   • 单词数量：${result.affected_count} 个\n`;
            detailMessage += `   • 免复习天数：${skipDays} 天\n`;
            detailMessage += `   • 截止日期：${skipUntilStr}\n\n`;
            detailMessage += `📝 免复习单词列表：\n`;

            selectedWords.forEach((word, index) => {
                const proficiency = word.proficiency ? word.proficiency.toFixed(1) : '0.0';
                detailMessage += `   ${index + 1}. ${word.english_word} - ${word.chinese_meaning} (熟练度: ${proficiency}%)\n`;
            });

            detailMessage += `\n💡 这些单词在 ${skipUntilStr} 之前不会出现在复习计划中。`;

            showMessage(detailMessage, 'success');

            const confirmBtn = document.getElementById('confirmBtn');
            confirmBtn.textContent = '✅ 确认完成';
            confirmBtn.style.background = '#4CAF50';
            confirmBtn.style.color = 'white';
            confirmBtn.style.fontWeight = 'bold';

            confirmBtn.onclick = function() {
                closeSkipReviewModal();
            };

            createTemporaryMessage(`🎯 成功设置 ${result.affected_count} 个单词免复习至 ${skipUntilStr}`, 'success');
        }

        // 测试确认设置免复习
        async function testConfirmSkipReview() {
            if (selectedWordIds.size === 0) {
                showMessage('请至少选择一个单词', 'warning');
                return;
            }

            const skipDays = parseInt(document.getElementById('skipDays').value);
            const confirmBtn = document.getElementById('confirmBtn');

            // 禁用按钮并显示加载状态
            confirmBtn.disabled = true;
            confirmBtn.textContent = '⏳ 设置中...';
            confirmBtn.style.opacity = '0.7';

            // 隐藏之前的消息
            const messageElement = document.getElementById('skipReviewMessage');
            if (messageElement) {
                messageElement.style.display = 'none';
            }

            // 模拟API调用延迟
            setTimeout(() => {
                // 模拟成功响应
                const result = {
                    success: true,
                    affected_count: selectedWordIds.size,
                    skip_until: '2025-08-09',
                    skip_days: skipDays
                };

                showDetailedSuccessMessage(result, skipDays);
            }, 1500);
        }
    </script>
</body>
</html>
