{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-02T13:33:28.800Z", "updatedAt": "2025-08-02T13:33:28.801Z", "resourceCount": 5}, "resources": [{"id": "english-phonetics-expert", "source": "project", "protocol": "role", "name": "English Phonetics Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/english-phonetics-expert/english-phonetics-expert.role.md", "metadata": {"createdAt": "2025-08-02T13:33:28.801Z", "updatedAt": "2025-08-02T13:33:28.801Z", "scannedAt": "2025-08-02T13:33:28.801Z", "path": "role/english-phonetics-expert/english-phonetics-expert.role.md"}}, {"id": "pattern-storage-workflow", "source": "project", "protocol": "execution", "name": "Pattern Storage Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/english-phonetics-expert/execution/pattern-storage-workflow.execution.md", "metadata": {"createdAt": "2025-08-02T13:33:28.801Z", "updatedAt": "2025-08-02T13:33:28.801Z", "scannedAt": "2025-08-02T13:33:28.801Z", "path": "role/english-phonetics-expert/execution/pattern-storage-workflow.execution.md"}}, {"id": "phonetic-rule-extraction", "source": "project", "protocol": "execution", "name": "Phonetic Rule Extraction 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/english-phonetics-expert/execution/phonetic-rule-extraction.execution.md", "metadata": {"createdAt": "2025-08-02T13:33:28.801Z", "updatedAt": "2025-08-02T13:33:28.801Z", "scannedAt": "2025-08-02T13:33:28.801Z", "path": "role/english-phonetics-expert/execution/phonetic-rule-extraction.execution.md"}}, {"id": "phonetic-pattern-recognition", "source": "project", "protocol": "thought", "name": "Phonetic Pattern Recognition 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/english-phonetics-expert/thought/phonetic-pattern-recognition.thought.md", "metadata": {"createdAt": "2025-08-02T13:33:28.801Z", "updatedAt": "2025-08-02T13:33:28.801Z", "scannedAt": "2025-08-02T13:33:28.801Z", "path": "role/english-phonetics-expert/thought/phonetic-pattern-recognition.thought.md"}}, {"id": "systematic-learning", "source": "project", "protocol": "thought", "name": "Systematic Learning 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/english-phonetics-expert/thought/systematic-learning.thought.md", "metadata": {"createdAt": "2025-08-02T13:33:28.801Z", "updatedAt": "2025-08-02T13:33:28.801Z", "scannedAt": "2025-08-02T13:33:28.801Z", "path": "role/english-phonetics-expert/thought/systematic-learning.thought.md"}}], "stats": {"totalResources": 5, "byProtocol": {"role": 1, "execution": 2, "thought": 2}, "bySource": {"project": 5}}}