<execution>
  <constraint>
    ## PromptX记忆系统约束
    - **记忆格式标准**：必须使用结构化格式存储规律
    - **分类标签要求**：每条记忆必须有明确的分类标签
    - **检索优化**：记忆内容需便于后续检索和应用
    - **更新机制**：支持规律的增量更新和修正
  </constraint>

  <rule>
    ## 模式存储强制规则
    - **结构化存储**：所有发音规律必须按统一格式存储
    - **标签完整性**：每个规律必须包含完整的分类标签
    - **关联建立**：相关规律间必须建立明确关联
    - **版本控制**：规律更新需保留版本历史
    - **验证存储**：存储前必须验证规律的准确性
  </rule>

  <guideline>
    ## 存储优化指导
    - **检索友好**：优化存储结构便于快速检索
    - **分层组织**：按重要性和复杂度分层存储
    - **关联丰富**：建立丰富的规律间关联关系
    - **更新便利**：设计便于更新和维护的存储结构
  </guideline>

  <process>
    ## 发音规律存储工作流
    
    ### Step 1: 规律结构化处理 (15分钟)
    ```mermaid
    flowchart TD
        A[原始规律] --> B[格式标准化]
        B --> C[添加分类标签]
        C --> D[建立关联关系]
        D --> E[验证完整性]
        E --> F[准备存储]
    ```
    
    **标准化格式模板**：
    ```json
    {
      "pattern_id": "ai_combination_001",
      "letter_combination": "ai",
      "main_pronunciation": "/eɪ/",
      "frequency": "75%",
      "rule_type": "primary",
      "examples": ["rain", "pain", "main"],
      "exceptions": ["said", "again"],
      "conditions": [],
      "memory_tips": "AI says /eɪ/ like 'hey'",
      "related_patterns": ["ay", "ei"],
      "difficulty_level": "basic",
      "tags": ["vowel_combination", "diphthong", "common"]
    }
    ```
    
    ### Step 2: 分类标签体系 (10分钟)
    ```mermaid
    mindmap
      root((标签体系))
        字母类型
          vowel_combination
          consonant_combination
          mixed_combination
        发音类型
          monophthong
          diphthong
          consonant_cluster
        难度等级
          basic
          intermediate
          advanced
        使用频率
          high_frequency
          medium_frequency
          low_frequency
    ```
    
    ### Step 3: 记忆存储执行 (5分钟)
    ```mermaid
    flowchart LR
        A[结构化规律] --> B[生成存储内容]
        B --> C[添加检索标签]
        C --> D[执行记忆存储]
        D --> E[验证存储结果]
        E --> F[建立索引关联]
    ```
    
    **存储内容生成模板**：
    ```
    ## [字母组合]发音规律 - [规律类型]
    
    **核心规律**: [字母组合] → /[音标]/ ([频率])
    
    **典型例词**: [例词列表]
    **例外情况**: [例外词汇] → /[例外发音]/
    **记忆技巧**: [助记方法]
    **相关规律**: [关联规律]
    
    **应用场景**: [使用环境]
    **学习建议**: [学习方法]
    
    #发音规律 #[字母组合] #[发音类型] #[难度等级]
    ```
    
    ### Step 4: 关联网络构建 (10分钟)
    ```mermaid
    graph TD
        A[当前规律] --> B[相似发音规律]
        A --> C[相同字母规律]
        A --> D[对比学习规律]
        A --> E[进阶学习规律]
        
        B --> F[关联存储]
        C --> F
        D --> F
        E --> F
    ```
    
    ## 具体存储操作
    
    ### 主规律存储格式
    ```
    ## AI字母组合发音规律 - 主要模式
    
    **核心规律**: ai → /eɪ/ (75%)
    
    **典型例词**: 
    - 基础词: rain, pain, main, gain
    - 进阶词: train, brain, chain, plain
    - 高级词: maintain, explain, contain, remain
    
    **发音环境**: 
    - 单词中间位置最常见
    - 重读音节中发音清晰
    - 词尾位置较少见
    
    **记忆技巧**: "AI says /eɪ/ - 就像说'hey'"
    
    **相关规律**: 
    - ay组合 (day, say, play)
    - ei组合 (eight, weight, neighbor)
    
    #发音规律 #ai组合 #双元音 #基础级别 #高频使用
    ```
    
    ### 例外情况存储格式
    ```
    ## AI字母组合发音例外 - 特殊情况
    
    **例外规律**: ai → /e/ 或 /ə/
    
    **例外词汇**:
    - said /sed/ - 常用词，需特别记忆
    - again /əˈɡen/ - 非重读音节弱化
    - captain /ˈkæptən/ - 非重读音节
    
    **例外原因**:
    - 历史语音变化
    - 非重读音节弱化
    - 特殊词汇演变
    
    **记忆策略**: 
    - 高频例外词单独记忆
    - 理解语音变化原因
    - 多次练习强化记忆
    
    #发音例外 #ai组合 #特殊情况 #重点记忆
    ```
  </process>

  <criteria>
    ## 存储质量标准
    
    ### 结构完整性
    - ✅ 格式标准化100%完成
    - ✅ 必要字段全部填写
    - ✅ 分类标签准确完整
    - ✅ 关联关系明确建立
    
    ### 内容准确性
    - ✅ 发音标注准确无误
    - ✅ 例词选择典型代表
    - ✅ 例外情况完整覆盖
    - ✅ 统计数据真实可靠
    
    ### 检索效率
    - ✅ 标签体系科学合理
    - ✅ 关键词覆盖全面
    - ✅ 检索路径清晰
    - ✅ 相关性排序准确
    
    ### 学习友好
    - ✅ 内容表述清晰易懂
    - ✅ 记忆技巧实用有效
    - ✅ 学习建议具体可行
    - ✅ 难度分级合理
  </criteria>
</execution>
