<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语字母组合发音规律交互式脑图</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        
        .controls {
            padding: 15px;
            background: #f5f5f5;
            border-bottom: 1px solid #ddd;
            text-align: center;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .mindmap-container {
            padding: 20px;
            height: 600px;
            overflow: auto;
        }
        
        .level-0 {
            text-align: center;
            background: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 50px;
            margin: 20px auto;
            font-size: 18px;
            font-weight: bold;
            max-width: 300px;
        }
        
        .level-1 {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            margin: 20px 0;
        }
        
        .level-1-item {
            background: #2196F3;
            color: white;
            padding: 15px;
            border-radius: 15px;
            margin: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 200px;
            text-align: center;
        }
        
        .level-1-item:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .level-1-item.collapsed {
            opacity: 0.7;
        }
        
        .level-2 {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
        }
        
        .level-2.expanded {
            display: block;
        }
        
        .level-2-item {
            background: #FF9800;
            color: white;
            padding: 10px;
            border-radius: 10px;
            margin: 8px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .level-2-item:hover {
            background: #F57C00;
        }
        
        .level-3 {
            display: none;
            margin-top: 10px;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        
        .level-3.expanded {
            display: block;
        }
        
        .level-3-item {
            background: #9C27B0;
            color: white;
            padding: 8px;
            border-radius: 8px;
            margin: 5px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .level-3-item:hover {
            background: #7B1FA2;
        }
        
        .level-4 {
            display: none;
            margin-top: 8px;
            padding: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        
        .level-4.expanded {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .level-4-item {
            background: #F44336;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            white-space: nowrap;
        }
        
        .legend {
            padding: 15px;
            background: #f9f9f9;
            border-top: 1px solid #ddd;
            text-align: center;
        }
        
        .legend-item {
            display: inline-block;
            margin: 5px 15px;
            font-size: 12px;
        }
        
        .legend-color {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 5px;
            vertical-align: middle;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            max-width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 英语字母组合发音规律交互式脑图</h1>
            <p>点击节点展开/收缩 | 悬停查看详情</p>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="expandAll()">🔍 全部展开</button>
            <button class="btn" onclick="collapseAll()">📁 全部收缩</button>
            <button class="btn" onclick="resetView()">🔄 重置视图</button>
        </div>
        
        <div class="mindmap-container" id="mindmap">
            <div class="level-0">
                英语字母组合发音规律体系
            </div>
            
            <div class="level-1">
                <!-- 超简单级 -->
                <div class="level-1-item" onclick="toggleLevel(this)" data-tooltip="规律性最强，几乎无例外">
                    <div>🟢 超简单级 (信心建立)</div>
                    <div class="level-2">
                        <div class="level-2-item" onclick="toggleLevel(this, event)" data-tooltip="单一/ɔː/音，95%规律性">
                            AW组合 /ɔː/ 95%
                            <div class="level-3">
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    基础词
                                    <div class="level-4">
                                        <div class="level-4-item">saw 看见</div>
                                        <div class="level-4-item">law 法律</div>
                                        <div class="level-4-item">raw 生的</div>
                                        <div class="level-4-item">paw 爪子</div>
                                        <div class="level-4-item">jaw 下巴</div>
                                        <div class="level-4-item">claw 爪子</div>
                                    </div>
                                </div>
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    日常词
                                    <div class="level-4">
                                        <div class="level-4-item">draw 画画</div>
                                        <div class="level-4-item">awful 糟糕的</div>
                                        <div class="level-4-item">crawl 爬行</div>
                                        <div class="level-4-item">dawn 黎明</div>
                                        <div class="level-4-item">lawn 草坪</div>
                                        <div class="level-4-item">yawn 打哈欠</div>
                                        <div class="level-4-item">hawk 鹰</div>
                                    </div>
                                </div>
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    进阶词
                                    <div class="level-4">
                                        <div class="level-4-item">strawberry 草莓</div>
                                        <div class="level-4-item">withdraw 撤回</div>
                                        <div class="level-4-item">awkward 尴尬的</div>
                                        <div class="level-4-item">awesome 棒极了</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 简单级 -->
                <div class="level-1-item" onclick="toggleLevel(this)" data-tooltip="主要规律+少量例外">
                    <div>🟢 简单级 (基础掌握)</div>
                    <div class="level-2">
                        <div class="level-2-item" onclick="toggleLevel(this, event)" data-tooltip="主要双元音，少量例外">
                            AI组合 /eɪ/ 75%
                            <div class="level-3">
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    主规律 /eɪ/
                                    <div class="level-4">
                                        <div class="level-4-item">rain 雨</div>
                                        <div class="level-4-item">pain 疼痛</div>
                                        <div class="level-4-item">train 火车</div>
                                        <div class="level-4-item">brain 大脑</div>
                                        <div class="level-4-item">maintain 维持</div>
                                        <div class="level-4-item">explain 解释</div>
                                    </div>
                                </div>
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    例外词
                                    <div class="level-4">
                                        <div class="level-4-item">said /sed/ 说</div>
                                        <div class="level-4-item">again /əˈɡen/ 再次</div>
                                        <div class="level-4-item">captain /ˈkæptən/ 船长</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="level-2-item" onclick="toggleLevel(this, event)" data-tooltip="主要长元音，规律性强">
                            EW组合 /uː/ 80%
                            <div class="level-3">
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    主规律 /uː/
                                    <div class="level-4">
                                        <div class="level-4-item">new 新的</div>
                                        <div class="level-4-item">few 少数</div>
                                        <div class="level-4-item">grew 成长</div>
                                        <div class="level-4-item">news 新闻</div>
                                        <div class="level-4-item">interview 面试</div>
                                        <div class="level-4-item">review 复习</div>
                                    </div>
                                </div>
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    例外词
                                    <div class="level-4">
                                        <div class="level-4-item">sew /soʊ/ 缝纫</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中级 -->
                <div class="level-1-item" onclick="toggleLevel(this)" data-tooltip="双模式规律，需要分组记忆">
                    <div>🟡 中级 (双模式规律)</div>
                    <div class="level-2">
                        <div class="level-2-item" onclick="toggleLevel(this, event)" data-tooltip="两个主要发音模式">
                            OO组合 双主模式
                            <div class="level-3">
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    长音 /uː/ 60%
                                    <div class="level-4">
                                        <div class="level-4-item">food 食物</div>
                                        <div class="level-4-item">moon 月亮</div>
                                        <div class="level-4-item">school 学校</div>
                                        <div class="level-4-item">cool 凉爽</div>
                                        <div class="level-4-item">smooth 光滑</div>
                                        <div class="level-4-item">choose 选择</div>
                                    </div>
                                </div>
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    短音 /ʊ/ 35%
                                    <div class="level-4">
                                        <div class="level-4-item">book 书</div>
                                        <div class="level-4-item">look 看</div>
                                        <div class="level-4-item">good 好的</div>
                                        <div class="level-4-item">took 拿</div>
                                        <div class="level-4-item">childhood 童年</div>
                                        <div class="level-4-item">understood 理解</div>
                                    </div>
                                </div>
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    例外 /ʌ/ 5%
                                    <div class="level-4">
                                        <div class="level-4-item">blood /blʌd/ 血</div>
                                        <div class="level-4-item">flood /flʌd/ 洪水</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="level-2-item" onclick="toggleLevel(this, event)" data-tooltip="两种截然不同的双元音">
                            OW组合 双元音对立
                            <div class="level-3">
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    开口音 /aʊ/ 70%
                                    <div class="level-4">
                                        <div class="level-4-item">cow 奶牛</div>
                                        <div class="level-4-item">how 如何</div>
                                        <div class="level-4-item">now 现在</div>
                                        <div class="level-4-item">down 向下</div>
                                        <div class="level-4-item">flower 花</div>
                                        <div class="level-4-item">power 力量</div>
                                    </div>
                                </div>
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    圆唇音 /oʊ/ 30%
                                    <div class="level-4">
                                        <div class="level-4-item">show 展示</div>
                                        <div class="level-4-item">know 知道</div>
                                        <div class="level-4-item">grow 成长</div>
                                        <div class="level-4-item">snow 雪</div>
                                        <div class="level-4-item">window 窗户</div>
                                        <div class="level-4-item">follow 跟随</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中高级 -->
                <div class="level-1-item" onclick="toggleLevel(this)" data-tooltip="4个发音模式，环境决定发音">
                    <div>🟠 中高级 (多模式环境依赖)</div>
                    <div class="level-2">
                        <div class="level-2-item" onclick="toggleLevel(this, event)" data-tooltip="4个发音模式，环境决定发音">
                            EA组合 4模式环境依赖
                            <div class="level-3">
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    长音 /iː/ 40%
                                    <div class="level-4">
                                        <div class="level-4-item">eat 吃</div>
                                        <div class="level-4-item">tea 茶</div>
                                        <div class="level-4-item">sea 海</div>
                                        <div class="level-4-item">read 读</div>
                                        <div class="level-4-item">meat 肉</div>
                                        <div class="level-4-item">team 团队</div>
                                        <div class="level-4-item">dream 梦想</div>
                                        <div class="level-4-item">steam 蒸汽</div>
                                    </div>
                                </div>
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    短音 /e/ 35%
                                    <div class="level-4">
                                        <div class="level-4-item">bread 面包</div>
                                        <div class="level-4-item">head 头</div>
                                        <div class="level-4-item">dead 死的</div>
                                        <div class="level-4-item">heavy 重的</div>
                                        <div class="level-4-item">ready 准备好</div>
                                        <div class="level-4-item">weather 天气</div>
                                        <div class="level-4-item">leather 皮革</div>
                                        <div class="level-4-item">treasure 宝藏</div>
                                    </div>
                                </div>
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    双元音 /eɪ/ 15%
                                    <div class="level-4">
                                        <div class="level-4-item">break 打破</div>
                                        <div class="level-4-item">great 伟大的</div>
                                        <div class="level-4-item">steak 牛排</div>
                                    </div>
                                </div>
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    卷舌音 /ɜː/ 10%
                                    <div class="level-4">
                                        <div class="level-4-item">learn 学习</div>
                                        <div class="level-4-item">earth 地球</div>
                                        <div class="level-4-item">search 搜索</div>
                                        <div class="level-4-item">heard 听见</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 高级 -->
                <div class="level-1-item" onclick="toggleLevel(this)" data-tooltip="4个发音模式，无明显环境规律">
                    <div>🔴 高级 (复杂多样规律)</div>
                    <div class="level-2">
                        <div class="level-2-item" onclick="toggleLevel(this, event)" data-tooltip="4个发音模式，无明显环境规律">
                            OU组合 4模式复杂
                            <div class="level-3">
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    双元音 /aʊ/ 50%
                                    <div class="level-4">
                                        <div class="level-4-item">house 房子</div>
                                        <div class="level-4-item">mouse 老鼠</div>
                                        <div class="level-4-item">about 关于</div>
                                        <div class="level-4-item">sound 声音</div>
                                        <div class="level-4-item">ground 地面</div>
                                        <div class="level-4-item">mountain 山</div>
                                        <div class="level-4-item">thousand 千</div>
                                        <div class="level-4-item">pronounce 发音</div>
                                    </div>
                                </div>
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    长音 /uː/ 25%
                                    <div class="level-4">
                                        <div class="level-4-item">soup 汤</div>
                                        <div class="level-4-item">group 组</div>
                                        <div class="level-4-item">through 通过</div>
                                        <div class="level-4-item">youth 青年</div>
                                        <div class="level-4-item">route 路线</div>
                                        <div class="level-4-item">wound 伤口</div>
                                        <div class="level-4-item">troupe 剧团</div>
                                        <div class="level-4-item">coupon 优惠券</div>
                                    </div>
                                </div>
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    短音 /ʌ/ 20%
                                    <div class="level-4">
                                        <div class="level-4-item">country 国家</div>
                                        <div class="level-4-item">young 年轻</div>
                                        <div class="level-4-item">trouble 麻烦</div>
                                        <div class="level-4-item">double 双倍</div>
                                        <div class="level-4-item">touch 触摸</div>
                                        <div class="level-4-item">southern 南方的</div>
                                        <div class="level-4-item">cousin 表兄弟</div>
                                        <div class="level-4-item">couple 夫妇</div>
                                    </div>
                                </div>
                                <div class="level-3-item" onclick="toggleLevel(this, event)">
                                    长音 /ɔː/ 5%
                                    <div class="level-4">
                                        <div class="level-4-item">four 四</div>
                                        <div class="level-4-item">pour 倒</div>
                                        <div class="level-4-item">court 法庭</div>
                                        <div class="level-4-item">source 来源</div>
                                        <div class="level-4-item">course 课程</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <span class="legend-color" style="background: #4CAF50;"></span>
                中心节点
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background: #2196F3;"></span>
                🟢 超简单级/简单级
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background: #FF9800;"></span>
                字母组合
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background: #9C27B0;"></span>
                词汇分类
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background: #F44336;"></span>
                具体单词
            </div>
        </div>
    </div>

    <script>
        function toggleLevel(element, event) {
            if (event) {
                event.stopPropagation();
            }
            
            const nextLevel = element.querySelector('.level-2, .level-3, .level-4');
            if (nextLevel) {
                nextLevel.classList.toggle('expanded');
                element.classList.toggle('collapsed');
            }
        }
        
        function expandAll() {
            document.querySelectorAll('.level-2, .level-3, .level-4').forEach(el => {
                el.classList.add('expanded');
            });
            document.querySelectorAll('.level-1-item, .level-2-item, .level-3-item').forEach(el => {
                el.classList.remove('collapsed');
            });
        }
        
        function collapseAll() {
            document.querySelectorAll('.level-2, .level-3, .level-4').forEach(el => {
                el.classList.remove('expanded');
            });
            document.querySelectorAll('.level-1-item, .level-2-item, .level-3-item').forEach(el => {
                el.classList.add('collapsed');
            });
        }
        
        function resetView() {
            collapseAll();
            document.querySelector('.mindmap-container').scrollTop = 0;
        }
        
        // 工具提示
        document.addEventListener('mouseover', function(e) {
            if (e.target.dataset.tooltip) {
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip';
                tooltip.textContent = e.target.dataset.tooltip;
                tooltip.style.left = e.pageX + 10 + 'px';
                tooltip.style.top = e.pageY + 10 + 'px';
                document.body.appendChild(tooltip);
                
                e.target.addEventListener('mouseleave', function() {
                    if (tooltip.parentNode) {
                        tooltip.parentNode.removeChild(tooltip);
                    }
                }, { once: true });
            }
        });
    </script>
</body>
</html>
