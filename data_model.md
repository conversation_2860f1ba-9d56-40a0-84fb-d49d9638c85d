# 📊 背单词应用数据模型设计文档

## 🎯 概述

本文档详细描述了背单词应用的完整数据模型，包括表结构设计、字段定义、关系图谱和业务逻辑。该数据模型基于Domain Driven Design (DDD)架构，支持个性化学习、熟练度评估和智能复习计划。

## 🏗️ 数据库架构图

```mermaid
erDiagram
    user ||--o{ user_word : "学习关系"
    user ||--o{ word_record : "学习记录"
    user ||--o{ learning_plan : "学习计划"
    user ||--o{ import_history : "导入记录"
    user ||--o{ voucher_log : "购物券记录"
    word ||--o{ user_word : "单词关系"
    word ||--o{ word_record : "学习记录"
    word ||--o{ learning_plan : "计划单词"
    word ||--o{ word_pattern_relations : "Pattern关联"
    word_patterns ||--o{ word_pattern_relations : "Pattern关系"
    word_patterns ||--o{ user_pattern_interactions : "用户交互"
    user ||--o{ user_pattern_interactions : "Pattern交互"

    user {
        int id PK "用户唯一标识"
        varchar username "用户名(唯一)"
        varchar password "加密密码"
        date registration_date "注册日期"
        int points "用户积分"
        int vouchers "购物券数量"
        timestamp created_at "创建时间"
        timestamp updated_at "更新时间"
    }

    word {
        int id PK "单词唯一标识"
        varchar english_word "英文单词"
        varchar chinese_meaning "中文释义"
        varchar section "章节分类"
        text learning_requirement "学习要求(spelling/recognition)"
    }

    user_word {
        int id PK "记录唯一标识"
        int user_id FK "用户ID"
        int word_id FK "单词ID"
        int learning_count "学习总次数"
        int correct_count "正确回答次数"
        datetime last_learning_date "最后学习时间"
        text memory_method "记忆方法描述"
        varchar status "单词状态(new/review/attention)"
        real proficiency "熟练度评分(0-100)"
    }

    word_record {
        int id PK "记录唯一标识"
        int user_id FK "用户ID"
        int word_id FK "单词ID"
        date date "学习日期"
        float duration_seconds "学习耗时(秒)"
        varchar user_input "用户输入答案"
        varchar answer "正确答案"
        boolean is_correct "是否正确"
    }

    learning_plan {
        int id PK "计划唯一标识"
        int user_id FK "用户ID"
        int word_id FK "单词ID"
        date planned_date "计划日期"
        varchar item_type "新词/生词/复习"
        int star_level "星级(1-5)"
    }

    import_history {
        int id PK "记录唯一标识"
        varchar import_type "导入类型"
        varchar filename "文件名"
        int total_records "总记录数"
        int new_words "新增单词数"
        int updated_words "更新单词数"
        int error_count "错误数量"
        datetime start_time "开始时间"
        varchar status "状态"
        boolean can_rollback "是否可回滚"
    }

    voucher_log {
        int id PK "日志唯一标识"
        int user_id FK "用户ID"
        int vouchers_change "购物券变化量"
        varchar reason "获得/消耗原因"
        varchar trigger_type "触发类型"
        date date "日期"
        timestamp created_at "创建时间"
    }

    simple_status {
        int id PK "记录唯一标识"
        text status "状态信息"
        timestamp created_at "创建时间"
    }

    word_patterns {
        int id PK "Pattern唯一标识"
        varchar pattern_type "Pattern类型"
        varchar pattern_value "Pattern值"
        varchar pattern_name "Pattern名称"
        text description "详细描述"
        int word_count "关联单词数"
        int priority_level "优先级"
        boolean is_active "是否启用"
        timestamp created_at "创建时间"
    }

    word_pattern_relations {
        int id PK "关联唯一标识"
        int pattern_id FK "Pattern ID"
        int word_id FK "单词ID"
        real match_strength "匹配强度"
        text match_reason "匹配原因"
        boolean is_primary "是否主要Pattern"
        timestamp created_at "创建时间"
    }

    user_pattern_interactions {
        int id PK "交互唯一标识"
        int user_id FK "用户ID"
        int pattern_id FK "Pattern ID"
        int word_id FK "单词ID"
        varchar interaction_type "交互类型"
        varchar session_id "会话ID"
        timestamp created_at "创建时间"
    }
```

## 📋 表结构详细设计

### 1. 📊 user 表 - 用户信息

**业务描述**: 存储用户基本信息和积分系统数据

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | INTEGER | PK | - | 用户唯一标识 |
| username | VARCHAR(80) | UNIQUE, NOT NULL | - | 用户名 |
| password | VARCHAR(255) | NOT NULL | - | 加密密码(bcrypt) |
| registration_date | DATE | - | - | 注册日期 |
| points | INTEGER | - | 0 | 用户积分(奖励系统) |
| vouchers | INTEGER | - | 0 | 购物券数量 |
| created_at | TIMESTAMP | - | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | - | CURRENT_TIMESTAMP | 更新时间 |

**索引设计**:
- PRIMARY KEY (id)
- UNIQUE INDEX (username)

---

### 2. 📚 word 表 - 词汇库

**业务描述**: 存储单词词汇信息，支持英中双语学习

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | INTEGER | PK | - | 单词唯一标识 |
| english_word | VARCHAR(100) | NOT NULL | - | 英文单词 |
| chinese_meaning | VARCHAR(100) | NOT NULL | - | 中文释义 |
| section | VARCHAR(50) | NOT NULL | - | 章节分类 |
| learning_requirement | TEXT | NOT NULL | 'spelling' | 学习要求 |

**索引设计**:
- PRIMARY KEY (id)
- INDEX (section) - 支持按章节查询
- INDEX (english_word) - 支持单词搜索

**业务规则**:
- 支持重复英文单词(不同中文释义)
- section用于组织学习内容
- learning_requirement指定学习模式:
  - **spelling**: 拼写模式，用户看中文输入英文
  - **recognition**: 选择题模式，用户从选项中选择正确答案，支持中译英和英译中两种题型

---

### 3. 🔗 user_word 表 - 用户-单词关系

**业务描述**: 存储用户对特定单词的学习状态和长期熟练度

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | INTEGER | PK | - | 记录唯一标识 |
| user_id | INTEGER | FK, NOT NULL | - | 用户ID |
| word_id | INTEGER | FK, NOT NULL | - | 单词ID |
| learning_count | INTEGER | - | 0 | 学习总次数 |
| correct_count | INTEGER | - | 0 | 正确回答次数 |
| last_learning_date | DATETIME | - | - | 最后学习时间 |
| memory_method | TEXT | - | - | 记忆方法描述 |
| status | VARCHAR(10) | - | 'new' | 单词状态(new/review/attention) |
| proficiency | REAL | - | 0.0 | 熟练度评分(0-100) |

**索引设计**:
- PRIMARY KEY (id)
- UNIQUE INDEX (user_id, word_id)
- INDEX (user_id, proficiency) - 支持熟练度排序
- INDEX (user_id, status) - 支持状态查询

**状态说明**:
- **new**: 新单词，尚未学习
- **review**: 已学习的单词，需要复习
- **attention**: 添加到生词本的单词

**熟练度计算公式**:
```
proficiency = 平均正确率(40%) + 平均回答用时(30%) + 距离上次学习间隔(30%)

维度1: 正确率评分 = (correct_count / learning_count) × 100
维度2: 回答用时评分 = f(avg_response_time) [5-15秒最优]
维度3: 学习间隔评分 = f(days_since_last_learning) [越近越高]
```

---

### 4. 📝 word_record 表 - 学习记录

**业务描述**: 记录用户每次学习的详细数据，用于统计分析和熟练度计算

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | INTEGER | PK | - | 记录唯一标识 |
| user_id | INTEGER | FK, NOT NULL | - | 用户ID |
| word_id | INTEGER | FK, NOT NULL | - | 单词ID |
| date | DATE | - | - | 学习日期 |
| duration_seconds | FLOAT | - | - | 学习耗时(秒) |
| user_input | VARCHAR(50) | - | - | 用户输入答案 |
| answer | VARCHAR(50) | - | - | 正确答案 |
| is_correct | BOOLEAN | NOT NULL | - | 是否正确 |

**索引设计**:
- PRIMARY KEY (id)
- INDEX (user_id, word_id, date) - 支持用户单词学习历史查询
- INDEX (user_id, date) - 支持按日期统计
- INDEX (word_id, is_correct) - 支持单词难度分析

**业务规则**:
- 每次学习尝试都记录一条记录
- duration_seconds用于计算平均回答用时
- user_input和answer用于错误分析
- 🔧 **[2025-07-20修复]** 数据用于实时更新user_word.proficiency和状态转换
- **实时统计更新**: 每次submit_answer时立即更新user_word表的learning_count、correct_count、proficiency、last_learning_date
- **实时状态转换**: 每次submit_answer时立即触发状态转换逻辑，确保数据一致性

---

### 5. 📅 learning_plan 表 - 学习计划

**业务描述**: 存储每日学习计划，实现"既定事实"原则的学习安排

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | INTEGER | PK | - | 计划唯一标识 |
| user_id | INTEGER | FK, NOT NULL | - | 用户ID |
| word_id | INTEGER | FK, NOT NULL | - | 单词ID |
| planned_date | DATE | NOT NULL | - | 计划日期 |
| item_type | VARCHAR(10) | NOT NULL | - | 新词/生词/复习 |
| star_level | INTEGER | - | 3 | 星级(1-5) |

**索引设计**:
- PRIMARY KEY (id)
- UNIQUE INDEX (user_id, word_id, planned_date) - 防止重复计划
- INDEX (user_id, planned_date) - 支持日期查询

**业务规则**:
- **不可变原则**: 已生成的学习计划不得修改
- **每日30词**: 10新词 + 20复习词
- **新词不足时**: 使用熟练度user_word.proficiency倒序选择复习词补充，直到满足10个新单词。注意：补充单词属性不变，依然是复习词
- **复习词不足时**: 使用状态为'attention'的单词或新单词补充，直到满足20个复习词汇。注意：补充单词属性不变，依然是新词
- **星级系统**: 1-5星表示当日学习进度
- **item_type枚举**: 'new'(新词), 'review'(复习)
- **状态识别**: 基于user_word表的status字段确定单词状态
- **计划生成时机**: 
  - 主要时机：当日学习完成后立即生成次日计划
  - 备用时机：次日首次登录时检查并生成(如有遗漏)
  - 新用户：首次登录时生成当日计划

**星级变化规则**:
```
答对: +1星 (最高5星) + 10积分
答错: -2星 (最低1星) - 20积分

星级意义:
1-2星: "需要加强" → 拼写模式/Recognition模式
3-4星: "基本掌握" → 传统模式/Recognition模式  
5星: "当日完成" → 可选择复习

学习模式选择:
- learning_requirement = 'spelling': 根据星级选择拼写或传统模式
- learning_requirement = 'recognition': 使用Recognition选择题模式
```

## 🎯 激励系统设计

### 积分系统 (即时更新)

**积分获得规则**:
- **基础奖励**: 答对 +10积分，答错 -20积分
- **连击奖励**: 连续答对有额外奖励加成
- **速度奖励**: 快速答题(<5秒)有奖励加成
- **会话完成奖励**: 完成学习会话的综合奖励

**积分消耗场景**:
- **提示功能**: 每次使用提示扣除5积分
- **特殊功能**: 未来扩展的高级功能

**更新机制**: 每次答题立即更新，使用数据库事务保证一致性

### 购物券系统 (即时更新)

**购物券获得规则**:
- **连击奖励**: 连续答对10题 → 立即发放1张购物券
- **优秀表现**: 20个单词+80%正确率 → 立即发放2张购物券
- **良好表现**: 15个单词+80%正确率 → 立即发放1张购物券

**购物券消耗场景**:
- **AI帮助**: 使用AI记忆帮助 → 消耗1张购物券
- **特殊提示**: 高级提示功能 → 消耗2张购物券(未来)
- **额外单词包**: 额外学习内容 → 消耗3张购物券(未来)

**每日限制**: 每用户每日最多获得2张购物券

**更新机制**:
- 达成条件立即发放并持久化到数据库
- 使用voucher_log表记录所有获得/消耗历史
- 基于数据库的可靠每日限制检查

## 🎮 学习模式设计

### 学习模式类型

系统支持两种主要学习模式，由`word.learning_requirement`字段决定：

#### 1. Spelling模式 (learning_requirement = 'spelling')
- **交互方式**: 用户看中文释义，输入英文单词
- **难度分层**: 根据星级自动选择子模式
  - **星级1-2**: 拼写模式 - 逐字母输入，实时反馈
  - **星级3-5**: 传统模式 - 完整单词输入
- **适用场景**: 需要强化拼写记忆的单词

#### 2. Recognition模式 (learning_requirement = 'recognition')
- **交互方式**: 选择题形式，从4个选项中选择正确答案
- **题型变换**: 
  - **中译英**: 显示中文释义，选择对应英文
  - **英译中**: 显示英文单词，选择对应中文
- **智能干扰项**: 系统自动生成相似度高的干扰选项
- **适用场景**: 注重理解和识别的单词

### Recognition模式技术实现

#### 题目生成流程
```mermaid
flowchart TD
    A[用户进入Recognition模式] --> B[调用RecognitionService.generate_question]
    B --> C[获取目标单词]
    C --> D[随机选择题型: cn_to_en或en_to_cn]
    D --> E[生成3个智能干扰项]
    E --> F[组合正确答案和干扰项]
    F --> G[随机打乱选项顺序]
    G --> H[返回题目数据给前端]
    H --> I[前端显示选择题界面]
```

#### 干扰项生成策略
1. **英文干扰项** (cn_to_en题型):
   - 优先选择相似长度的单词 (±2字符)
   - 避免首字母相同且长度相同的单词
   - 随机选择，确保多样性

2. **中文干扰项** (en_to_cn题型):
   - 优先从同章节选择语义相关词汇
   - 章节内词汇不足时从其他章节补充
   - 确保语义领域的合理性

#### 答案验证和反馈
- **即时反馈**: 选择后立即显示正确/错误状态
- **音频播放**: 答题后自动播放单词发音
- **自动跳转**: 2秒延迟后自动进入下一题
- **视觉提示**: 
  - 正确选项标记为绿色
  - 错误选项标记为红色
  - 显示正确答案解释

## 📁 静态资源管理

### 资源文件组织结构

系统为每个单词提供对应的音频和图片资源，支持多媒体学习体验：

```
static/
├── audio/
│   └── words/           # 单词音频文件
│       ├── apple.mp3    # 标准化命名
│       ├── book.mp3
│       └── ...
└── images/
    └── words/           # 单词图片文件
        ├── apple.jpg    # 标准化命名
        ├── book.jpg
        └── ...
```

### 文件命名规范

#### 命名标准化规则
所有资源文件遵循统一的命名规范，通过`ResourceFinder.normalize_word_for_filename()`函数处理：

1. **基础转换**:
   - 转换为小写: `Apple` → `apple`
   - 空格替换下划线: `ice cream` → `ice_cream`
   - 移除单引号: `don't` → `dont`
   - 斜杠替换下划线: `and/or` → `and_or`

2. **特殊处理**:
   - 时间表达式: `a.m./p.m.` → `a_m_p_m`
   - 连字符替换: `twenty-one` → `twenty_one`
   - 移除圆括号: `word(s)` → `words`
   - 移除点号: `Mr.` → `Mr`

3. **清理规则**:
   - 合并多个下划线为单个下划线
   - 移除开头和结尾的下划线
   - 过滤空字符串组件

#### 文件格式要求
- **音频文件**: MP3格式，推荐16kHz采样率
- **图片文件**: JPG格式，推荐300x300像素

### 资源完整性保障

#### "不重不漏"原则
系统确保每个数据库中的单词都有对应的音频和图片文件：

1. **完整性检查**:
   - 数据库单词总数: 959个
   - 音频文件覆盖率: 100% (959/959)
   - 图片文件覆盖率: 100% (959/959)

2. **重复文件处理**:
   - 自动检测和解决重复文件
   - 基于文件质量评分选择最佳版本
   - 质量评分因子: 文件大小、命名规范、完整性

3. **缺失文件生成**:
   - 自动为缺失单词生成占位符资源
   - 音频文件: 复制相似单词的发音作为模板
   - 图片文件: 生成标准化的占位符图像

#### 智能资源查找
`ResourceFinder`服务提供多重查找策略：

```python
# 主要查找方法
def find_audio_file(word: str) -> Optional[Path]
def find_image_file(word: str) -> Optional[Path]

# 查找策略
1. 标准化文件名匹配
2. 候选变体尝试 (处理特殊字符组合)
3. 降级匹配 (忽略部分特殊字符)
4. 兜底机制 (返回默认资源)
```

### 资源维护工具

#### 自动化脚本
- `scripts/auto_resolve_duplicates.py`: 自动解决重复文件
- `scripts/generate_missing_resources.py`: 生成缺失资源
- `scripts/final_verification.py`: 完整性验证
- `scripts/maintain_clean_resources.py`: 定期维护

#### 维护工作流
1. **日常监控**: 检测新增单词的资源需求
2. **定期清理**: 移除无效或低质量资源
3. **完整性验证**: 确保数据库与文件系统同步
4. **性能优化**: 压缩文件大小，优化加载速度

## 🔄 数据流程设计

### 学习模式选择流程

根据单词的`learning_requirement`字段，系统会选择不同的学习模式：

```mermaid
flowchart TD
    A[获取当前单词] --> B{检查learning_requirement}
    B -->|spelling| C[拼写模式]
    B -->|recognition| D[Recognition模式]
    
    C --> E{检查星级}
    E -->|星级>=3| F[传统模式：显示中文，输入英文]
    E -->|星级<3| G[拼写模式：逐字母提示]
    
    D --> H[生成选择题]
    H --> I{题型随机选择}
    I -->|cn_to_en| J[中译英：显示中文，选择英文]
    I -->|en_to_cn| K[英译中：显示英文，选择中文]
    
    F --> L[提交答案]
    G --> L
    J --> M[提交Recognition答案]
    K --> M
    
    L --> N[更新学习数据]
    M --> N
    N --> O[播放音频]
    O --> P[显示反馈]
    P --> Q[2秒后自动跳转下一题]
```

### 学习计划生成流程

```mermaid
flowchart TD
    A[用户登录] --> B{是否有当日计划?}
    B -->|否| C[生成学习计划]
    B -->|是| D[加载现有计划]
    
    C --> E[选择10个新词]
    E --> F{新词足够?}
    F -->|否| G[用熟练度倒序复习词补充到10个]
    F -->|是| H[选择20个复习词按熟练度倒序]
    G --> H
    
    H --> I{复习词足够?}
    I -->|否| J[用生词本/新词补充到20个]
    I -->|是| K[设置初始星级]
    J --> K
    
    K --> L[新词3星/复习词4星]
    L --> M[写入learning_plan表]
    M --> D
    
    D --> N[开始学习]
```

**学习计划生成详细规则 (2025-08-02更新)**:

### 🎯 总体配置
- **每日总计**: 30个单词 (10新词位置 + 20复习位置)
- **新词位置**: 默认3星级
- **复习位置**: 默认4星级

### 📋 5步完整生成流程

#### 1️⃣ **新词选择** (`_select_new_words`)
从真正的新词中选择10个：
```sql
SELECT w.* FROM word w
JOIN user_word uw ON w.id = uw.word_id
WHERE uw.user_id = ? AND uw.status = 'new'
  AND NOT EXISTS (
    SELECT 1 FROM learning_plan lp
    WHERE lp.user_id = ? AND lp.word_id = w.id AND lp.star_level >= 2
  )
ORDER BY w.priority ASC, w.id
LIMIT 10
```
- **排除条件**: 已在任何学习计划中达到star_level>=2的单词
- **排序规则**: 优先级 → ID，确保稳定性和一致性

#### 2️⃣ **新词不足补充** (`_select_review_words_for_new_positions`)
当新词 < 10个时，用复习词补充到新词位置：
```sql
SELECT w.*, uw.proficiency FROM word w
JOIN user_word uw ON w.id = uw.word_id
WHERE uw.user_id = ? AND uw.status = 'review'
  AND (uw.skip_review_until IS NULL OR uw.skip_review_until <= date('now'))
ORDER BY w.priority ASC, uw.proficiency DESC, w.id
LIMIT ?
```
- **免复习过滤**: 🔧 **[2025-08-02修复]** 排除免复习期内的单词
- **选择策略**: 高熟练度优先，补充到新词位置但item_type='review'

#### 3️⃣ **智能复习词选择** (`SmartReviewSelector.select_review_words`)

**第一阶段：候选词过滤**
```sql
SELECT w.*, uw.proficiency, uw.learning_count, uw.correct_count, 
       uw.last_learning_date, uw.status, uw.skip_review_until
FROM word w
JOIN user_word uw ON w.id = uw.word_id
WHERE uw.user_id = ?
  AND uw.status IN ('review', 'attention')
  AND w.id NOT IN (已选新词ID列表)  -- 避免重复
  AND (uw.skip_review_until IS NULL OR uw.skip_review_until <= date('now'))  -- 免复习过滤
ORDER BY w.priority ASC, w.id ASC
```

**第二阶段：智能评分算法**
```
智能分数 = 基础分数 × 时间因子 × 频率因子

基础分数 = (100 - 熟练度) / 100  // 熟练度越低分数越高

时间因子计算规则：
- 今天刚学过: 0.1 (重度惩罚，避免过度学习)
- 距离目标间隔太近: 0.3-0.7 (轻度惩罚)
- 刚好达到目标间隔: 1.0 (最佳复习时机)
- 超过目标间隔: 1.0-2.0 (逐渐增加权重，上限2.0)

频率因子计算 (基于最近7天学习次数)：
- 0次: 1.2 (奖励，需要复习)
- 1-2次: 1.0 (正常频率)
- 3-4次: 0.7 (轻度惩罚，频率较高)
- 5+次: 0.4 (重度惩罚，避免过度学习)

目标间隔规则 (基于熟练度的差异化间隔)：
- 熟练度 0-50%: 1天间隔 (需要频繁复习)
- 熟练度 50-75%: 3天间隔 (中等间隔)
- 熟练度 75-100%: 7天间隔 (较长间隔)
```

**第三阶段：加权随机选择**
```
根据智能分数进行加权随机选择，避免完全固化的学习模式
权重 = 智能分数^1.5  // 平方根放大分数差异
使用加权随机算法选择，确保高分单词更容易被选中但保持适度随机性
```

#### 4️⃣ **复习词不足补充** (`_select_attention_or_new_words`)
当复习词 < 20个时，按优先级补充：
```sql
-- 优先使用attention状态单词
SELECT w.*, uw.proficiency FROM word w
JOIN user_word uw ON w.id = uw.word_id
WHERE uw.user_id = ?
  AND uw.status = 'attention'
  AND w.id NOT IN (已选单词ID列表)  -- 避免重复
  AND NOT EXISTS (
    SELECT 1 FROM learning_plan lp
    WHERE lp.user_id = ? AND lp.word_id = w.id AND lp.star_level >= 2
  )
ORDER BY w.priority ASC, uw.proficiency ASC, uw.last_learning_date DESC
LIMIT ?
```
- **补充策略**: attention词 → 新词，item_type='new'
- **排序规则**: 优先级 → 低熟练度 → 最近学习时间

#### 5️⃣ **最终计划记录生成** (`_create_plan_records`)
```sql
INSERT INTO learning_plan (user_id, word_id, planned_date, item_type, star_level)
VALUES (?, ?, ?, ?, ?)
```
- **新词位置**: star_level = 3
- **复习位置**: star_level = 4
- **完整性验证**: 确保总计30个单词

### 🚫 免复习机制 (Skip Review Feature)

#### 核心过滤逻辑
**统一过滤条件**: 所有复习词选择方法都包含此过滤：
```sql
AND (uw.skip_review_until IS NULL OR uw.skip_review_until <= date('now'))
```

#### 过滤效果
- `skip_review_until IS NULL` → 从未设置免复习 ✅ 可被选中
- `skip_review_until <= date('now')` → 免复习期已过 ✅ 可被选中
- `skip_review_until > date('now')` → 仍在免复习期 ❌ 被排除

#### 免复习设置机制
```sql
-- 用户设置免复习期
UPDATE user_word 
SET skip_review_until = ?, updated_at = CURRENT_TIMESTAMP
WHERE user_id = ? AND word_id IN (?)
```
- **推荐条件**: 
  - 熟练度 ≥ 90% → 直接推荐
  - 熟练度 ≥ 80% + 今日正确率 ≥ 80% → 推荐
  - 熟练度 ≥ 70% + 今日正确率 ≥ 90% → 推荐
- **用户控制**: 完全由用户决定哪些单词免复习及天数

### 🎯 算法特点与优势

#### 智能化特点
1. **避免重复学习**: 已选单词不会在其他位置重复出现
2. **时间间隔优化**: 基于熟练度的差异化复习间隔
3. **频率控制**: 防止过度学习同一单词
4. **优先级排序**: 考虑单词优先级和熟练度
5. **加权随机性**: 避免完全固化的学习模式
6. **免复习尊重**: 完全尊重用户的免复习选择

#### 容错处理
- **新词不足**: 用高熟练度复习词补充
- **复习词不足**: 用attention词和新词补充
- **候选词不足**: 确保至少返回可用单词
- **日期解析错误**: 提供默认值保证稳定性

#### 一致性保证
- **🔧 [2025-08-02修复]** 所有复习词选择方法统一应用免复习过滤
- **避免重复**: 严格的去重机制确保单词不重复
- **稳定排序**: 使用一致的排序规则确保结果可预期

### 学习过程数据更新

```mermaid
flowchart TD
    A[进入学习界面] --> B[从learning_plan获取每日学习数据]
    B --> C[根据learning_requirement选择学习模式]
    C --> |spelling| D1[拼写/传统模式学习]
    C --> |recognition| D2[Recognition选择题模式]
    
    D1 --> E1[用户输入英文单词]
    D2 --> E2[用户选择选项A/B/C/D]
    
    E1 --> F[更新word_record表]
    E2 --> F
    F --> |duration_seconds<br/>user_input/selected_option<br/>is_correct| G[更新learning_plan.star_level]
    G --> H{继续学习?}
    H -->|是| C
    H -->|否| I[统计word_record数据]
    I --> J[更新user_word表]
    J --> |learning_count<br/>correct_count<br/>last_learning_date| K[执行状态更新规则]
    K --> L[兜底逻辑：star_level>=2的新词转为review]
    L --> M{当日所有单词达到5星?}
    M -->|否| N[保存当前进度，结束]
    M -->|是| O[计算proficiency]
    O --> P[更新user_word.proficiency]
    P --> Q[生成次日学习计划]
    Q --> R[基于最新proficiency选择复习词]
    R --> S[写入次日learning_plan]
    S --> T[学习流程结束]
    N --> T
```

### 激励系统数据流程

```mermaid
flowchart TD
    A[用户答题] --> B{答题结果}

    %% 积分系统流程
    B -->|答对| C[积分 +10]
    B -->|答错| D[积分 -20]
    C --> E[立即更新user.points]
    D --> E
    E --> F[清理用户缓存]

    %% 购物券即时检查
    B --> G{连击检查}
    G -->|连续答对10题| H[触发连击奖励]
    H --> I{每日限制检查}
    I -->|未达上限| J[发放1张购物券]
    I -->|已达上限| K[跳过发放]

    J --> L[更新user.vouchers]
    L --> M[记录voucher_log]
    M --> N[清理用户缓存]

    %% 会话完成奖励
    O[学习会话完成] --> P{表现评估}
    P -->|20词+80%正确率| Q[优秀表现奖励]
    P -->|15词+80%正确率| R[良好表现奖励]
    P -->|不满足条件| S[无奖励]

    Q --> T{每日限制检查}
    R --> T
    T -->|剩余≥2张| U[发放2张购物券]
    T -->|剩余=1张| V[发放1张购物券]
    T -->|剩余=0张| W[达到每日上限]

    U --> X[更新user.vouchers]
    V --> X
    X --> Y[记录voucher_log]
    Y --> Z[清理用户缓存]

    %% 购物券消耗
    AA[使用AI帮助] --> BB{购物券检查}
    BB -->|购物券≥1| CC[消耗1张购物券]
    BB -->|购物券<1| DD[显示不足提示]

    CC --> EE[更新user.vouchers]
    EE --> FF[记录voucher_log]
    FF --> GG[提供AI帮助]

    %% 数据一致性
    F --> HH[前端状态更新]
    N --> HH
    Z --> HH
    GG --> HH
```

**详细学习流程说明**:

1. **进入学习**: 从learning_plan表获取当日学习数据（30个单词）
2. **学习模式选择**:
   - **spelling模式**: 根据星级选择拼写模式(1-2星)或传统模式(3-4星)
   - **recognition模式**: 显示选择题界面，支持中译英和英译中两种题型
3. **单词学习循环**:
   - **spelling模式**: 用户看中文输入英文单词
   - **recognition模式**: 用户从4个选项(A/B/C/D)中选择正确答案
   - 实时记录到word_record表：duration_seconds, user_input/selected_option, is_correct
   - 根据答题结果更新learning_plan.star_level（答对+1星，答错-2星）
3. **退出学习触发**:
   - 用户主动退出学习界面
   - 网络中断或异常退出
4. **数据同步更新**:
   - 统计word_record中的学习数据
   - 更新user_word表：learning_count, correct_count, last_learning_date
5. **状态更新规则**（🔧 **[2025-07-20修复]** 关键规则强化）:
   - **主要规则**: 基于proficiency更新状态
     - proficiency >= 50: status = 'review'
     - proficiency < 30: status = 'attention'（表现很差时）
     - **🔧 关键修复**: 30 <= proficiency < 50 且 current_status = 'new' 时，转为 'review'
   - **兜底规则**: 基于learning_plan.star_level更新状态
     - 针对new状态的单词，如果star_level >= 2，自动转为'review'状态
     - 理由：新词从3星开始，达到2星说明用户已有学习尝试，应进入复习阶段
     - 适用场景：未完成当日学习或中途退出的情况
   - **🔧 实时触发**: 每次submit_answer时立即执行状态转换，不再依赖finish_learning_session
6. **完成判断**:
   - 检查当日所有单词是否达到5星
   - 如未完成：保存进度，执行兜底状态更新规则，等待下次学习
   - 如完成：触发熟练度计算和次日计划生成
7. **熟练度计算**:
   - 基于word_record历史数据计算proficiency
   - 更新到user_word.proficiency字段
8. **次日计划生成**:
   - 基于最新的user_word.proficiency选择复习词
   - 生成次日learning_plan记录
   - 优先复习proficiency较低的单词

**关键设计原则**:
- **数据分离**: learning_plan不存储proficiency，需要时查询user_word表
- **实时记录**: 每次答题立即记录到word_record
- **批量更新**: 退出时批量更新user_word统计数据
- **异常处理**: 支持中断恢复，确保数据一致性

---

### 6. 📋 import_history 表 - 导入历史记录

**业务描述**: 记录单词数据导入的详细历史，支持数据追踪和回滚

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | INTEGER | PK | - | 记录唯一标识 |
| import_type | VARCHAR(20) | NOT NULL | - | 导入类型 |
| filename | VARCHAR(255) | - | - | 文件名 |
| file_size | INTEGER | - | - | 文件大小 |
| total_records | INTEGER | NOT NULL | - | 总记录数 |
| new_words | INTEGER | NOT NULL | - | 新增单词数 |
| updated_words | INTEGER | NOT NULL | - | 更新单词数 |
| error_count | INTEGER | NOT NULL | - | 错误数量 |
| execution_time_ms | INTEGER | - | - | 执行时间(毫秒) |
| start_time | DATETIME | NOT NULL | - | 开始时间 |
| end_time | DATETIME | - | - | 结束时间 |
| status | VARCHAR(20) | NOT NULL | - | 状态(success/failed/processing) |
| can_rollback | BOOLEAN | NOT NULL | - | 是否可回滚 |
| created_at | DATETIME | NOT NULL | - | 创建时间 |
| updated_at | DATETIME | NOT NULL | - | 更新时间 |

**索引设计**:
- PRIMARY KEY (id)
- INDEX (import_type) - 支持按类型查询
- INDEX (start_time) - 支持按时间查询
- INDEX (status) - 支持按状态查询

---

### 7. 🎫 voucher_log 表 - 购物券历史记录

**业务描述**: 记录用户购物券的获得和消耗历史，支持即时更新机制和每日限制管理

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | INTEGER | PK | - | 日志唯一标识 |
| user_id | INTEGER | NOT NULL, FK | - | 用户ID |
| vouchers_change | INTEGER | NOT NULL | - | 购物券变化量(正数获得，负数消耗) |
| reason | VARCHAR(255) | NOT NULL | - | 获得/消耗原因 |
| trigger_type | VARCHAR(50) | NOT NULL | - | 触发类型 |
| date | DATE | NOT NULL | - | 日期 |
| created_at | TIMESTAMP | - | CURRENT_TIMESTAMP | 创建时间 |

**trigger_type 枚举值**:
- `answer_streak`: 连击奖励(连续答对10题)
- `session_excellent`: 优秀表现(20词+80%正确率)
- `session_good`: 良好表现(15词+80%正确率)
- `ai_help`: AI帮助消耗
- `special_hint`: 特殊提示消耗
- `manual_award`: 手动发放
- `system_adjustment`: 系统调整

**索引设计**:
- PRIMARY KEY (id)
- INDEX (user_id, date) - 支持用户每日查询
- INDEX (user_id, trigger_type) - 支持按类型统计
- INDEX (date, vouchers_change) - 支持全局统计

**外键约束**:
- FOREIGN KEY (user_id) REFERENCES user(id)

---

### 8. 📊 simple_status 表 - 系统状态记录

**业务描述**: 记录系统状态和简单的操作日志

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | INTEGER | PK | - | 记录唯一标识 |
| status | TEXT | NOT NULL | - | 状态信息 |
| created_at | TIMESTAMP | - | CURRENT_TIMESTAMP | 创建时间 |

**索引设计**:
- PRIMARY KEY (id)
- INDEX (created_at) - 支持按时间查询

---

### 9. 🧠 word_patterns 表 - 单词Pattern定义

**业务描述**: 存储单词学习模式的定义信息，支持多层次认知体系

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | INTEGER | PK | - | Pattern唯一标识 |
| pattern_type | VARCHAR(50) | NOT NULL | - | Pattern类型 |
| pattern_value | VARCHAR(100) | NOT NULL | - | Pattern值 |
| pattern_name | VARCHAR(200) | NOT NULL | - | Pattern名称 |
| description | TEXT | - | - | 详细描述 |
| word_count | INTEGER | - | 0 | 关联单词数 |
| priority_level | INTEGER | - | 3 | 优先级(1-5) |
| is_active | BOOLEAN | - | 1 | 是否启用 |
| created_at | TIMESTAMP | - | CURRENT_TIMESTAMP | 创建时间 |

**Pattern类型(pattern_type)枚举值**:
- `letter_combo`: 字母组合(er, ar, th, oo等)
- `theme`: 主题分组(time, colors, family等)
- `prefix`: 前缀(re-, un-, dis-等)
- `suffix`: 后缀(-ing, -ed, -ly等)
- `semantic`: 语义关联(动作、情感、方向等)
- `word_length`: 词长分组(3字母、4字母等)
- `first_letter`: 首字母分组(A开头、B开头等)
- `rhyme`: 音韵模式(押韵尾音)
- `phonetic`: 发音规律(双元音、音标等)
- `silent_letter`: 不发音字母(gh, kn, wr等)
- `syllable`: 音节结构(开音节、闭音节等)
- `root`: 词根(vis, aud, port等)
- `grammar`: 语法功能(动词、名词、形容词等)
- `collocation`: 搭配模式(时间词汇、学校场景等)

**认知层次分类**:
- **基础感知层(basic)**: letter_combo, word_length, first_letter, phonetic
- **语义关联层(intermediate)**: theme, semantic, rhyme, silent_letter, syllable
- **语法功能层(advanced)**: prefix, suffix, root, grammar, collocation

**索引设计**:
- PRIMARY KEY (id)
- UNIQUE INDEX (pattern_type, pattern_value) - 防止重复Pattern
- INDEX (pattern_type) - 支持按类型查询
- INDEX (is_active, priority_level) - 支持活跃Pattern查询
- INDEX (word_count) - 支持按单词数排序

---

### 10. 🔗 word_pattern_relations 表 - 单词Pattern关联

**业务描述**: 存储单词与Pattern的关联关系，支持多对多映射和匹配强度评分

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | INTEGER | PK | - | 关联唯一标识 |
| pattern_id | INTEGER | FK, NOT NULL | - | Pattern ID |
| word_id | INTEGER | FK, NOT NULL | - | 单词ID |
| match_strength | REAL | - | 0.5 | 匹配强度(0.0-1.0) |
| match_reason | TEXT | - | - | 匹配原因说明 |
| is_primary | BOOLEAN | - | 0 | 是否为主要Pattern |
| created_at | TIMESTAMP | - | CURRENT_TIMESTAMP | 创建时间 |

**业务规则**:
- **匹配强度范围**: 0.0-1.0，值越高表示关联度越强
- **主要Pattern**: 每个单词可以有1-3个主要Pattern(is_primary=1)
- **匹配原因**: 人类可读的解释，用于前端展示
- **关联数量**: 每个单词平均关联2-5个Pattern

**匹配强度等级**:
- **0.9-1.0**: 完全匹配(如词根、前后缀)
- **0.7-0.8**: 强关联(如主题分组、语义关联)
- **0.5-0.6**: 中等关联(如字母组合、音韵)
- **0.3-0.4**: 弱关联(如词长、首字母)

**索引设计**:
- PRIMARY KEY (id)
- UNIQUE INDEX (pattern_id, word_id) - 防止重复关联
- INDEX (word_id, is_primary) - 支持单词主要Pattern查询
- INDEX (pattern_id, match_strength DESC) - 支持Pattern内单词排序
- INDEX (match_strength) - 支持按强度查询

**外键约束**:
- FOREIGN KEY (pattern_id) REFERENCES word_patterns(id)
- FOREIGN KEY (word_id) REFERENCES word(id)

---

### 11. 📊 user_pattern_interactions 表 - 用户Pattern交互记录

**业务描述**: 记录用户与Pattern推荐系统的交互行为，用于效果分析和个性化优化

| 字段名 | 类型 | 约束 | 默认值 | 描述 |
|--------|------|------|--------|------|
| id | INTEGER | PK | - | 交互唯一标识 |
| user_id | INTEGER | FK, NOT NULL | - | 用户ID |
| pattern_id | INTEGER | FK, NOT NULL | - | Pattern ID |
| word_id | INTEGER | FK, NOT NULL | - | 单词ID |
| interaction_type | VARCHAR(20) | NOT NULL | - | 交互类型 |
| session_id | VARCHAR(100) | - | - | 学习会话ID |
| created_at | TIMESTAMP | - | CURRENT_TIMESTAMP | 创建时间 |

**交互类型(interaction_type)枚举值**:
- `view`: 查看Pattern提示
- `click`: 点击相似单词
- `helpful`: 标记为有帮助
- `not_helpful`: 标记为没帮助
- `ignore`: 忽略提示
- `open_panel`: 打开Pattern面板
- `close_panel`: 关闭Pattern面板

**业务规则**:
- **会话关联**: session_id关联学习会话，用于分析学习上下文
- **行为分析**: 通过交互数据分析Pattern有效性
- **个性化优化**: 基于用户行为调整Pattern推荐权重
- **A/B测试**: 支持不同Pattern策略的效果对比

**数据保留策略**:
- **活跃数据**: 最近30天的交互记录
- **历史归档**: 超过30天的数据定期归档
- **统计汇总**: 按月汇总用户行为统计

**索引设计**:
- PRIMARY KEY (id)
- INDEX (user_id, created_at) - 支持用户行为时序查询
- INDEX (pattern_id, interaction_type) - 支持Pattern效果统计
- INDEX (session_id) - 支持会话内行为分析
- INDEX (word_id, interaction_type) - 支持单词Pattern效果分析

**外键约束**:
- FOREIGN KEY (user_id) REFERENCES user(id)
- FOREIGN KEY (pattern_id) REFERENCES word_patterns(id)
- FOREIGN KEY (word_id) REFERENCES word(id)

---

## 📊 关键业务指标

### 用户学习质量指标

| 指标名称 | 计算公式 | 业务意义 |
|----------|----------|----------|
| 日完成率 | 5星单词数 / 当日计划数 | 当日学习完成度 |
| 整体正确率 | 总正确数 / 总尝试数 | 学习准确性 |
| 平均熟练度 | AVG(user_word.proficiency) | 长期掌握水平 |
| 平均回答时间 | AVG(duration_seconds) | 学习效率 |
| 连续学习天数 | 连续有学习记录的天数 | 学习坚持度 |

### 激励系统指标

| 指标名称 | 计算公式 | 业务意义 |
|----------|----------|----------|
| 积分获得率 | 总积分变化 / 学习天数 | 积分系统活跃度 |
| 购物券获得率 | 购物券获得数 / 学习天数 | 奖励系统效果 |
| 购物券使用率 | 购物券消耗数 / 购物券获得数 | 功能使用活跃度 |
| 连击奖励频率 | 连击奖励次数 / 学习会话数 | 连续答对表现 |
| 每日奖励达成率 | 达到每日上限天数 / 总学习天数 | 学习表现稳定性 |
| AI帮助使用频率 | AI帮助使用次数 / 学习单词数 | 辅助功能依赖度 |

### 单词难度指标

| 指标名称 | 计算公式 | 业务意义 |
|----------|----------|----------|
| 单词错误率 | 错误次数 / 总尝试次数 | 单词客观难度 |
| 平均学习次数 | AVG(learning_count) | 掌握所需时间 |
| 平均首次正确率 | 首次尝试正确用户数 / 总用户数 | 单词直观性 |

## 🎵 静态资源管理

### 音频和图片资源

**资源目录结构**:
```
static/
├── audio/
│   └── words/          # 单词发音文件
│       ├── apple.mp3
│       ├── book.mp3
│       └── ...
└── images/
    └── words/          # 单词图片文件
        ├── apple.jpg
        ├── book.jpg
        └── ...
```

**命名规范**:
- **音频文件**: `{normalized_word}.mp3`
- **图片文件**: `{normalized_word}.jpg`
- **命名标准化**: 小写字母，空格替换为下划线，移除特殊字符

**资源覆盖原则**:
- **不重不漏**: 数据库中每个单词都有对应的音频和图片资源
- **智能查找**: 支持多种命名格式的自动匹配
- **资源生成**: 缺失资源自动生成占位文件

**资源管理功能**:
- **重复文件清理**: 自动检测并清理重复的音频/图片文件
- **缺失资源补充**: 自动生成缺失的音频/图片占位文件
- **质量评分**: 基于文件大小、命名规范等因素评估文件质量
- **智能选择**: 重复文件时自动保留质量最高的版本

**技术实现**:
- 使用 `ResourceFinder` 类进行智能资源查找
- 支持实时资源查找和缓存机制
- 自动处理特殊字符和多种命名格式

## 🛡️ 数据完整性约束

### 外键约束
```sql
-- user_word表
FOREIGN KEY (user_id) REFERENCES user(id)
FOREIGN KEY (word_id) REFERENCES word(id)

-- word_record表
FOREIGN KEY (user_id) REFERENCES user(id)
FOREIGN KEY (word_id) REFERENCES word(id)

-- learning_plan表
FOREIGN KEY (user_id) REFERENCES user(id)
FOREIGN KEY (word_id) REFERENCES word(id)

-- voucher_log表
FOREIGN KEY (user_id) REFERENCES user(id)
```

### 业务规则约束
```sql
-- 熟练度范围约束
CHECK (proficiency >= 0 AND proficiency <= 100)

-- 星级范围约束  
CHECK (star_level >= 1 AND star_level <= 5)

-- item_type枚举约束
CHECK (item_type IN ('new', 'review'))

-- status枚举约束
CHECK (status IN ('new', 'review', 'attention'))

-- 学习时间非负约束
CHECK (duration_seconds >= 0)

-- 学习次数一致性约束
CHECK (correct_count <= learning_count)

-- 购物券变化量约束
CHECK (vouchers_change != 0)

-- 购物券总数非负约束
CHECK (vouchers >= 0)

-- 积分可以为负数(允许透支)
-- points字段无需非负约束
```

## 🚀 性能优化设计

### 索引策略
```sql
-- 复合索引优化查询
CREATE INDEX idx_user_date_performance ON word_record(user_id, date, is_correct);
CREATE INDEX idx_user_proficiency ON user_word(user_id, proficiency DESC);
CREATE INDEX idx_plan_user_date ON learning_plan(user_id, planned_date);

-- 支持状态查询
CREATE INDEX idx_user_status ON user_word(user_id, status);

-- 购物券系统索引
CREATE INDEX idx_voucher_log_user_date ON voucher_log(user_id, date);
CREATE INDEX idx_voucher_log_user_type ON voucher_log(user_id, trigger_type);
CREATE INDEX idx_voucher_log_date_change ON voucher_log(date, vouchers_change);
```

### 分区策略
```sql
-- 按日期分区word_record表(大数据量时)
PARTITION BY RANGE (date) (
    PARTITION p2024 VALUES LESS THAN ('2025-01-01'),
    PARTITION p2025 VALUES LESS THAN ('2026-01-01')
);
```

## 🔧 数据维护策略

### 定期清理任务
- **历史记录归档**: word_record表超过1年的数据归档
- **计划表清理**: 过期30天的learning_plan记录清理
- **购物券日志归档**: voucher_log表超过6个月的数据归档
- **数据一致性校验**: 定期校验user_word与word_record的统计数据一致性
- **激励系统校验**: 定期校验用户积分和购物券数据一致性

### 数据一致性检查
```sql
-- 检查学习计数一致性
SELECT uw.user_id, uw.word_id, uw.learning_count,
       (SELECT COUNT(*) FROM word_record wr 
        WHERE wr.user_id = uw.user_id AND wr.word_id = uw.word_id) as actual_count
FROM user_word uw
WHERE uw.learning_count != actual_count;

-- 检查正确率一致性
SELECT uw.user_id, uw.word_id, uw.correct_count,
       (SELECT COUNT(*) FROM word_record wr 
        WHERE wr.user_id = uw.user_id AND wr.word_id = uw.word_id AND wr.is_correct = 1) as actual_correct
FROM user_word uw
WHERE uw.correct_count != actual_correct;

-- 检查当日学习计划完整性
SELECT user_id, planned_date, COUNT(*) as word_count
FROM learning_plan
WHERE planned_date = CURRENT_DATE
GROUP BY user_id, planned_date
HAVING COUNT(*) != 25;

-- 检查购物券数据一致性
SELECT u.id, u.vouchers as current_vouchers,
       COALESCE(SUM(vl.vouchers_change), 0) as calculated_vouchers
FROM user u
LEFT JOIN voucher_log vl ON u.id = vl.user_id
GROUP BY u.id, u.vouchers
HAVING u.vouchers != calculated_vouchers;

-- 检查每日购物券限制
SELECT user_id, date, SUM(vouchers_change) as daily_earned
FROM voucher_log
WHERE vouchers_change > 0
GROUP BY user_id, date
HAVING SUM(vouchers_change) > 2;

-- 检查购物券日志完整性
SELECT user_id, COUNT(*) as log_count,
       SUM(CASE WHEN vouchers_change > 0 THEN vouchers_change ELSE 0 END) as total_earned,
       SUM(CASE WHEN vouchers_change < 0 THEN ABS(vouchers_change) ELSE 0 END) as total_consumed
FROM voucher_log
GROUP BY user_id;
```

## 📈 扩展性设计

### 水平扩展支持
- **用户分片**: 按user_id哈希分片
- **时间分片**: 按date字段分区
- **读写分离**: 学习记录写入主库，统计查询从库

### 新功能扩展点
- **多语言支持**: word表增加language字段
- **音频支持**: word表增加audio_url字段
- **图片支持**: word表增加image_url字段
- **学习模式**: learning_plan增加mode字段
- **社交功能**: 增加friend表和shared_progress表
- **激励系统扩展**:
  - **等级系统**: user表增加level字段，基于积分计算等级
  - **成就系统**: 增加achievement表记录用户成就
  - **排行榜**: 增加leaderboard表支持竞争机制
  - **购物券商店**: 增加voucher_shop表支持购物券兑换
  - **任务系统**: 增加daily_task表支持每日任务奖励

---

## 📋 总结

该数据模型设计具有以下特点：

✅ **业务导向**: 紧密贴合背单词学习的业务流程
✅ **性能优化**: 合理的索引和查询优化设计
✅ **数据完整性**: 完善的约束和一致性保证
✅ **扩展性**: 支持未来功能扩展和性能扩展

---

## 🔧 重要修复记录

### 2025-07-20 数据一致性和状态转换修复

#### 问题背景
发现naonao用户存在严重的数据不一致问题：
1. **统计数据不一致**: 101个单词的word_record和user_word表数据不同步
2. **状态逻辑不一致**: 47个单词status=new但有学习记录和高proficiency
3. **复习选择缺陷**: attention状态单词被排除在复习选择之外

#### 根本原因
- `finish_learning_session`调用缺失导致统计数据和状态转换不执行
- `submit_answer`只更新word_record，不更新user_word统计数据
- 复习词选择逻辑只包含status='review'，排除了attention单词

#### 修复方案

##### 1. 实时统计更新机制
```python
# 在LearningService.submit_answer中添加
def _update_user_word_stats_realtime(cursor, user_id, word_id, current_date):
    # 实时计算并更新user_word统计数据
    # 包括: learning_count, correct_count, proficiency, last_learning_date
```

##### 2. 实时状态转换机制
```python
# 在每次submit_answer后立即触发
StatusTransitionService.update_word_status_by_proficiency(user_id, word_id)
```

##### 3. 复习词选择逻辑修复
```sql
-- 修复前: 只选择review状态
WHERE uw.status = 'review'

-- 修复后: 包含attention状态
WHERE uw.status IN ('review', 'attention')
```

##### 4. 状态转换逻辑完善
```python
# 修复30-50 proficiency范围的处理
if proficiency >= 50:
    new_status = 'review'
elif proficiency < 30:
    new_status = 'attention'
else:  # 30 <= proficiency < 50
    if current_status == 'new':
        new_status = 'review'  # 关键修复
```

#### 修复效果
- ✅ **数据一致性**: 0个剩余不一致问题
- ✅ **状态逻辑**: 完全自洽的数据状态
- ✅ **复习优先级**: 低proficiency的attention单词被正确选入
- ✅ **实时同步**: 不再依赖finish_learning_session

#### 数据验证
修复后naonao用户数据分布：
- **new**: 768个单词, proficiency=0.0 (完全一致)
- **attention**: 29个单词, proficiency=42.2-91.0 (包含需复习单词)
- **review**: 162个单词, proficiency=54.3-100.0 (已掌握单词)

#### 业务影响
- 🎯 **学习效果提升**: 最需要复习的单词被优先安排
- 🔄 **数据实时性**: 学习进度实时反映，无延迟
- 🛡️ **系统稳定性**: 即使异常退出也能保持数据一致性

### 2025-07-26 Recognition模式完善

#### 新增功能
1. **Recognition选择题模式**: 
   - 支持中译英和英译中两种题型
   - 智能干扰项生成算法
   - 4选1选择题格式

2. **静态资源管理**:
   - 完善的音频和图片资源管理系统
   - 自动重复文件检测和清理
   - 缺失资源自动生成
   - 实现"不重不漏"的资源覆盖

#### 修复问题
1. **答案提交流程**:
   - 修复APIErrorCodes缺失属性问题
   - 完善答案提交后的音频播放
   - 实现2秒自动跳转到下一题

2. **用户体验优化**:
   - 修复英译中模式的答案泄露问题
   - 完善下一题按钮功能
   - 统一spelling和recognition模式的用户体验

#### 技术实现
- `RecognitionService`: 选择题生成和答案验证
- `ResourceFinder`: 智能静态资源查找
- 前端JavaScript: Recognition模式界面交互
- 自动化脚本: 资源管理和维护

#### 数据库影响
- `word.learning_requirement`字段支持'recognition'值
- `word_record.user_input`字段存储选择的选项(A/B/C/D)
- 保持与spelling模式相同的数据流和统计逻辑

---
✅ **可维护性**: 清晰的表结构和业务逻辑分离

该模型支持多种学习模式、个性化学习路径、智能熟练度评估和高效的复习计划生成，为用户提供优质的单词学习体验。