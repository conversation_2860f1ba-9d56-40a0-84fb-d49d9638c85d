#!/bin/bash

# Word Learning App v5 一键部署脚本
# 简单有效，一个脚本搞定所有事情

set -e

echo "🚀 Word Learning App v5 一键部署"
echo "================================="

# 获取密码
echo -n "请输入QNAP密码: "
read -s QNAP_PASSWORD
echo
export SSHPASS="$QNAP_PASSWORD"

# 测试SSH连接
echo "🔗 测试QNAP连接..."
if ! sshpass -e ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no lesong@************* "echo '连接测试成功'" 2>/dev/null; then
    echo "❌ SSH连接失败，请检查："
    echo "   1. QNAP服务器是否运行：ping *************"
    echo "   2. SSH服务是否启用"
    echo "   3. 用户名密码是否正确"
    echo "   4. 是否需要使用SSH密钥认证"
    exit 1
fi
echo "✅ SSH连接正常"

# 1. 构建镜像
echo "📦 构建 AMD64 Docker 镜像..."
docker build --platform linux/amd64 -f Dockerfile.cn-mirror -t word_learning_app_v5_amd64:latest .

# 2. 传输镜像
echo "📤 保存并传输镜像..."
docker save word_learning_app_v5_amd64:latest > word_learning_app_v5_amd64_latest.tar
sshpass -e scp word_learning_app_v5_amd64_latest.tar lesong@*************:/share/homes/lesong/
rm -f word_learning_app_v5_amd64_latest.tar

# 3. 同步静态资源
echo "📤 同步静态资源到QNAP..."
sshpass -e rsync -av --progress ./static/ lesong@*************:/share/homes/lesong/word_learning_app_v5/static/

# 4. 同步数据库
echo "📦 同步数据库到QNAP..."
sshpass -e rsync -av --progress ./instance/ lesong@*************:/share/homes/lesong/word_learning_app_v5/instance/

# 5. 部署到 QNAP
echo "🎯 部署到 QNAP..."
sshpass -e ssh lesong@************* << 'EOF'
DOCKER="/share/ZFS530_DATA/.qpkg/container-station/bin/docker"

# 创建目录
mkdir -p /share/homes/lesong/word_learning_app_v5/{instance,static/{audio,cache,css,exports,images,js}}

# 加载镜像
$DOCKER load < /share/homes/lesong/word_learning_app_v5_amd64_latest.tar
rm -f /share/homes/lesong/word_learning_app_v5_amd64_latest.tar

# 停止旧容器
$DOCKER stop word_learning_app_v5 2>/dev/null || true
$DOCKER rm word_learning_app_v5 2>/dev/null || true

# 启动新容器（修复端口和时区）
$DOCKER run -d --name word_learning_app_v5 \
  -p 5003:5005 \
  -e TZ=Asia/Shanghai \
  -v /share/homes/lesong/word_learning_app_v5/instance:/app/instance \
  -v /share/homes/lesong/word_learning_app_v5/static:/app/static \
  --restart unless-stopped \
  word_learning_app_v5_amd64:latest

sleep 5
echo "✅ 部署完成!"
echo "🌐 访问地址: http://*************:5003"
$DOCKER ps -f name=word_learning_app_v5
EOF

echo ""
echo "🎉 部署完成!"
echo "🌐 应用地址: http://*************:5003"