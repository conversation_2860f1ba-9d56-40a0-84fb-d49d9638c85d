/* ========== 响应式JavaScript功能 ========== */

// 设备检测
const DeviceDetector = {
    isMobile: () => window.innerWidth <= 768,
    isTablet: () => window.innerWidth > 768 && window.innerWidth <= 1024,
    isDesktop: () => window.innerWidth > 1024,
    isLandscape: () => window.innerWidth > window.innerHeight,
    isPortrait: () => window.innerWidth <= window.innerHeight,
    
    // 检测触摸设备
    isTouchDevice: () => {
        return 'ontouchstart' in window || 
               navigator.maxTouchPoints > 0 || 
               navigator.msMaxTouchPoints > 0;
    },
    
    // 检测iOS设备
    isIOS: () => {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
    },
    
    // 检测Android设备
    isAndroid: () => {
        return /Android/.test(navigator.userAgent);
    }
};

// 响应式布局管理器
const ResponsiveManager = {
    init() {
        this.setupEventListeners();
        this.handleResize();
        this.optimizeForDevice();
    },
    
    setupEventListeners() {
        // 窗口大小变化监听
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.handleResize();
            }, 250);
        });
        
        // 方向变化监听
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });
        
        // 触摸事件优化
        if (DeviceDetector.isTouchDevice()) {
            this.setupTouchOptimizations();
        }
    },
    
    handleResize() {
        this.updateViewportClasses();
        this.adjustLayoutForScreenSize();
        this.optimizeInputSizes();
    },
    
    handleOrientationChange() {
        this.updateViewportClasses();
        this.adjustForOrientation();
        
        // iOS Safari viewport fix
        if (DeviceDetector.isIOS()) {
            setTimeout(() => {
                window.scrollTo(0, 1);
                window.scrollTo(0, 0);
            }, 500);
        }
    },
    
    updateViewportClasses() {
        const body = document.body;
        
        // 清除现有类
        body.classList.remove('mobile', 'tablet', 'desktop', 'landscape', 'portrait');
        
        // 添加设备类
        if (DeviceDetector.isMobile()) {
            body.classList.add('mobile');
        } else if (DeviceDetector.isTablet()) {
            body.classList.add('tablet');
        } else {
            body.classList.add('desktop');
        }
        
        // 添加方向类
        if (DeviceDetector.isLandscape()) {
            body.classList.add('landscape');
        } else {
            body.classList.add('portrait');
        }
        
        // 添加触摸设备类
        if (DeviceDetector.isTouchDevice()) {
            body.classList.add('touch-device');
        }
    },
    
    adjustLayoutForScreenSize() {
        const container = document.querySelector('.dashboard-container');
        if (!container) return;
        
        if (DeviceDetector.isMobile()) {
            // 移动端布局调整
            this.adjustMobileLayout();
        } else if (DeviceDetector.isTablet()) {
            // 平板布局调整
            this.adjustTabletLayout();
        } else {
            // 桌面布局调整
            this.adjustDesktopLayout();
        }
    },
    
    adjustMobileLayout() {
        // 移动端特定调整
        const leftColumn = document.querySelector('.left-column');
        const rightColumn = document.querySelector('.right-column');
        
        if (leftColumn && rightColumn) {
            // 在移动端，学习区域优先显示
            rightColumn.style.order = '1';
            leftColumn.style.order = '2';
        }
        
        // 调整按钮大小
        this.adjustButtonSizes('mobile');
    },
    
    adjustTabletLayout() {
        // 平板特定调整
        this.adjustButtonSizes('tablet');
    },
    
    adjustDesktopLayout() {
        // 桌面特定调整
        const leftColumn = document.querySelector('.left-column');
        const rightColumn = document.querySelector('.right-column');
        
        if (leftColumn && rightColumn) {
            leftColumn.style.order = '1';
            rightColumn.style.order = '2';
        }
        
        this.adjustButtonSizes('desktop');
    },
    
    adjustForOrientation() {
        if (DeviceDetector.isMobile()) {
            const container = document.querySelector('.dashboard-container');
            if (!container) return;
            
            if (DeviceDetector.isLandscape()) {
                // 横屏模式：使用水平布局
                container.style.flexDirection = 'row';
                container.style.gap = 'var(--spacing-sm)';
            } else {
                // 竖屏模式：使用垂直布局
                container.style.flexDirection = 'column';
                container.style.gap = 'var(--spacing-md)';
            }
        }
    },
    
    adjustButtonSizes(deviceType) {
        const buttons = document.querySelectorAll('button, .btn');
        
        buttons.forEach(button => {
            switch(deviceType) {
                case 'mobile':
                    button.style.minHeight = '48px';
                    button.style.fontSize = '1rem';
                    break;
                case 'tablet':
                    button.style.minHeight = '44px';
                    button.style.fontSize = '0.95rem';
                    break;
                case 'desktop':
                    button.style.minHeight = '40px';
                    button.style.fontSize = '0.875rem';
                    break;
            }
        });
    },
    
    optimizeInputSizes() {
        const inputs = document.querySelectorAll('input[type="text"], textarea');
        
        inputs.forEach(input => {
            if (DeviceDetector.isMobile()) {
                // 移动端输入框优化
                input.style.fontSize = '16px'; // 防止iOS缩放
                input.style.padding = 'var(--spacing-md)';
            }
        });
    },
    
    setupTouchOptimizations() {
        // 禁用双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', (event) => {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
        
        // 优化触摸滚动
        document.addEventListener('touchstart', (event) => {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        }, { passive: false });
        
        // 添加触摸反馈
        this.addTouchFeedback();
    },
    
    addTouchFeedback() {
        const touchElements = document.querySelectorAll('button, .btn, a, input, select');
        
        touchElements.forEach(element => {
            element.addEventListener('touchstart', () => {
                element.classList.add('touch-active');
            });
            
            element.addEventListener('touchend', () => {
                setTimeout(() => {
                    element.classList.remove('touch-active');
                }, 150);
            });
            
            element.addEventListener('touchcancel', () => {
                element.classList.remove('touch-active');
            });
        });
    },
    
    optimizeForDevice() {
        // iOS特定优化
        if (DeviceDetector.isIOS()) {
            this.applyIOSOptimizations();
        }
        
        // Android特定优化
        if (DeviceDetector.isAndroid()) {
            this.applyAndroidOptimizations();
        }
        
        // 触摸设备优化
        if (DeviceDetector.isTouchDevice()) {
            this.applyTouchOptimizations();
        }
    },
    
    applyIOSOptimizations() {
        // iOS Safari viewport fix
        const viewport = document.querySelector('meta[name="viewport"]');
        if (viewport) {
            viewport.setAttribute('content', 
                'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
            );
        }
        
        // 禁用iOS的橡皮筋效果
        document.body.addEventListener('touchmove', (event) => {
            if (event.target === document.body) {
                event.preventDefault();
            }
        }, { passive: false });
    },
    
    applyAndroidOptimizations() {
        // Android特定的优化
        document.body.style.webkitTapHighlightColor = 'transparent';
    },
    
    applyTouchOptimizations() {
        // 增加触摸目标大小
        const touchTargets = document.querySelectorAll('button, a, input, select');
        touchTargets.forEach(target => {
            const computedStyle = window.getComputedStyle(target);
            const minSize = 44; // 最小触摸目标大小
            
            if (parseInt(computedStyle.height) < minSize) {
                target.style.minHeight = minSize + 'px';
            }
            if (parseInt(computedStyle.width) < minSize) {
                target.style.minWidth = minSize + 'px';
            }
        });
    }
};

// 性能优化工具
const PerformanceOptimizer = {
    init() {
        this.optimizeAnimations();
        this.setupIntersectionObserver();
        this.optimizeImages();
    },
    
    optimizeAnimations() {
        // 检查用户是否偏好减少动画
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
        
        if (prefersReducedMotion.matches) {
            document.body.classList.add('reduce-motion');
        }
        
        prefersReducedMotion.addEventListener('change', (e) => {
            if (e.matches) {
                document.body.classList.add('reduce-motion');
            } else {
                document.body.classList.remove('reduce-motion');
            }
        });
    },
    
    setupIntersectionObserver() {
        // 懒加载和性能优化
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('in-view');
                }
            });
        }, {
            threshold: 0.1
        });
        
        // 观察需要懒加载的元素
        document.querySelectorAll('.lazy-load').forEach(el => {
            observer.observe(el);
        });
    },
    
    optimizeImages() {
        // 图片懒加载和优化
        const images = document.querySelectorAll('img');
        
        images.forEach(img => {
            if (img.loading !== 'lazy') {
                img.loading = 'lazy';
            }
        });
    }
};

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    ResponsiveManager.init();
    PerformanceOptimizer.init();
});

// 导出供其他脚本使用
window.ResponsiveUtils = {
    DeviceDetector,
    ResponsiveManager,
    PerformanceOptimizer
};
