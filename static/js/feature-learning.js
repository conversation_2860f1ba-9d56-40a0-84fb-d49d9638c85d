/**
 * 特征学习前端交互模块
 * 处理学习洞察、实时反馈和智能分析功能
 */

class FeatureLearningManager {
    constructor() {
        this.apiBase = '/feature-learning/api';
        this.debounceTimer = null;
        this.similarityCache = new Map();
    }

    /**
     * 初始化特征学习功能
     */
    init() {
        this.bindEvents();
    }

    /**
     * 绑定事件处理器
     */
    bindEvents() {
        // 实时相似度检查（在学习页面使用）
        const userInputs = document.querySelectorAll('.user-input-field');
        userInputs.forEach(input => {
            input.addEventListener('input', (e) => this.handleInputChange(e));
        });

        // 学习洞察刷新按钮
        const refreshBtn = document.getElementById('refresh-insights');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshInsights());
        }
    }

    /**
     * 处理用户输入变化（实时相似度检查）
     */
    handleInputChange(event) {
        const userInput = event.target.value.trim();
        const correctAnswer = event.target.dataset.correctAnswer;

        if (!userInput || !correctAnswer) {
            this.clearSimilarityFeedback(event.target);
            return;
        }

        // 防抖处理，避免频繁请求
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
            this.checkSimilarityRealtime(userInput, correctAnswer, event.target);
        }, 300);
    }

    /**
     * 实时相似度检查
     */
    async checkSimilarityRealtime(userInput, correctAnswer, inputElement) {
        const cacheKey = `${userInput}|${correctAnswer}`;
        
        // 检查缓存
        if (this.similarityCache.has(cacheKey)) {
            this.displaySimilarityFeedback(this.similarityCache.get(cacheKey), inputElement);
            return;
        }

        try {
            const response = await fetch(`${this.apiBase}/enhanced-similarity`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_input: userInput,
                    correct_answer: correctAnswer
                })
            });

            const data = await response.json();

            if (data.success) {
                // 缓存结果
                this.similarityCache.set(cacheKey, data.data);
                this.displaySimilarityFeedback(data.data, inputElement);
            }
        } catch (error) {
            console.error('实时相似度检查失败:', error);
        }
    }

    /**
     * 显示相似度反馈
     */
    displaySimilarityFeedback(similarityData, inputElement) {
        let feedbackContainer = inputElement.parentNode.querySelector('.similarity-feedback');
        
        if (!feedbackContainer) {
            feedbackContainer = document.createElement('div');
            feedbackContainer.className = 'similarity-feedback mt-1';
            inputElement.parentNode.appendChild(feedbackContainer);
        }

        const similarity = (similarityData.similarity_score * 100).toFixed(0);
        let feedbackClass = 'text-danger';
        let feedbackIcon = 'fas fa-times';

        if (similarityData.exact_match) {
            feedbackClass = 'text-success';
            feedbackIcon = 'fas fa-check';
        } else if (similarityData.is_close_match) {
            feedbackClass = 'text-warning';
            feedbackIcon = 'fas fa-exclamation-triangle';
        }

        feedbackContainer.innerHTML = `
            <small class="${feedbackClass}">
                <i class="${feedbackIcon} me-1"></i>
                相似度: ${similarity}% - ${similarityData.suggested_feedback}
            </small>
        `;
    }

    /**
     * 清除相似度反馈
     */
    clearSimilarityFeedback(inputElement) {
        const feedbackContainer = inputElement.parentNode.querySelector('.similarity-feedback');
        if (feedbackContainer) {
            feedbackContainer.remove();
        }
    }

    /**
     * 获取用户学习洞察
     */
    async getLearningInsights() {
        try {
            const response = await fetch(`${this.apiBase}/insights`);
            const data = await response.json();

            if (data.success) {
                return data.data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('获取学习洞察失败:', error);
            throw error;
        }
    }

    /**
     * 预测单词难度
     */
    async predictDifficulty(wordInfo) {
        try {
            const response = await fetch(`${this.apiBase}/predict-difficulty`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(wordInfo)
            });

            const data = await response.json();

            if (data.success) {
                return data.data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('预测单词难度失败:', error);
            throw error;
        }
    }

    /**
     * 获取个性化推荐
     */
    async getRecommendations() {
        try {
            const response = await fetch(`${this.apiBase}/recommendations`);
            const data = await response.json();

            if (data.success) {
                return data.data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('获取推荐失败:', error);
            throw error;
        }
    }

    /**
     * 刷新学习洞察
     */
    async refreshInsights() {
        try {
            this.showLoading('insights-container', true);
            
            const insights = await this.getLearningInsights();
            const recommendations = await this.getRecommendations();
            
            this.displayInsightsData(insights, recommendations);
            
        } catch (error) {
            this.showError('刷新失败: ' + error.message);
        } finally {
            this.showLoading('insights-container', false);
        }
    }

    /**
     * 显示洞察数据
     */
    displayInsightsData(insights, recommendations) {
        // 基础统计
        if (insights.basic_stats) {
            this.updateBasicStats(insights.basic_stats);
        }

        // 学习模式
        if (insights.patterns && insights.patterns.status === 'success') {
            this.updatePatterns(insights.patterns.results);
        }

        // 推荐建议
        if (recommendations) {
            this.updateRecommendations(recommendations);
        }
    }

    /**
     * 更新基础统计
     */
    updateBasicStats(stats) {
        const elements = {
            'total-attempts': stats.total_attempts,
            'accuracy-rate': (stats.accuracy_rate * 100).toFixed(1) + '%',
            'avg-duration': stats.average_duration.toFixed(1) + 's',
            'recent-trend': this.getTrendText(stats.recent_trend)
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });

        // 更新趋势图标
        this.updateTrendIcon(stats.recent_trend);
    }

    /**
     * 获取趋势文本
     */
    getTrendText(trend) {
        const trendTexts = {
            'improving': '上升中',
            'declining': '需改进',
            'stable': '保持稳定',
            'insufficient_data': '数据不足'
        };
        return trendTexts[trend] || '分析中';
    }

    /**
     * 更新趋势图标
     */
    updateTrendIcon(trend) {
        const iconElement = document.getElementById('trend-arrow');
        if (!iconElement) return;

        const trendConfig = {
            'improving': { icon: '↗', class: 'trend-improving' },
            'declining': { icon: '↘', class: 'trend-declining' },
            'stable': { icon: '→', class: 'trend-stable' }
        };

        const config = trendConfig[trend] || { icon: '', class: '' };
        iconElement.textContent = config.icon;
        iconElement.className = `trend-arrow ${config.class}`;
    }

    /**
     * 显示加载状态
     */
    showLoading(containerId, show) {
        const container = document.getElementById(containerId);
        if (!container) return;

        if (show) {
            container.style.opacity = '0.5';
            container.style.pointerEvents = 'none';
        } else {
            container.style.opacity = '1';
            container.style.pointerEvents = 'auto';
        }
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        // 创建临时错误提示
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        errorDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(errorDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 3000);
    }

    /**
     * 显示成功信息
     */
    showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
        successDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        successDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(successDiv);

        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.remove();
            }
        }, 3000);
    }

    /**
     * 在学习页面显示智能提示
     */
    showSmartHints(wordInfo) {
        this.predictDifficulty(wordInfo).then(prediction => {
            const difficulty = prediction.predicted_difficulty;
            const confidence = (prediction.confidence * 100).toFixed(0);

            let hintMessage = '';
            let hintClass = '';

            switch(difficulty) {
                case 'easy':
                    hintMessage = `💡 AI预测这个单词对您来说较容易 (置信度: ${confidence}%)`;
                    hintClass = 'alert-success';
                    break;
                case 'medium':
                    hintMessage = `⚡ AI预测这个单词难度适中 (置信度: ${confidence}%)`;
                    hintClass = 'alert-info';
                    break;
                case 'hard':
                    hintMessage = `🎯 AI预测这个单词有挑战性，请仔细思考 (置信度: ${confidence}%)`;
                    hintClass = 'alert-warning';
                    break;
            }

            this.displayHint(hintMessage, hintClass);
        }).catch(error => {
            console.error('获取智能提示失败:', error);
        });
    }

    /**
     * 显示智能提示
     */
    displayHint(message, className) {
        const hintContainer = document.getElementById('smart-hints');
        if (!hintContainer) return;

        hintContainer.innerHTML = `
            <div class="alert ${className} alert-dismissible fade show">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
    }

    /**
     * 批量测试相似度（用于调试）
     */
    async testSimilarityBatch(testCases) {
        const results = [];

        for (const testCase of testCases) {
            try {
                const response = await fetch(`${this.apiBase}/enhanced-similarity`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testCase)
                });

                const data = await response.json();
                
                if (data.success) {
                    results.push({
                        ...testCase,
                        result: data.data
                    });
                }
            } catch (error) {
                console.error('批量测试失败:', error);
            }
        }

        return results;
    }
}

// 全局实例
window.featureLearningManager = new FeatureLearningManager();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.featureLearningManager.init();
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FeatureLearningManager;
}