/**
 * 购物券奖励系统
 * 管理购物券获取条件的检测和奖励发放
 */

class VoucherRewardsSystem {
    constructor() {
        console.warn('🚫 购物券奖励系统已禁用 - VoucherRewardsSystem类已停用');
        this.disabled = true;
        // 所有功能已禁用
        return this;
    }

    init() {
        if (this.disabled) {
            console.log('🚫 购物券奖励系统已禁用，跳过初始化');
            return;
        }
    }

    bindEvents() {
        // 监听学习结果
        document.addEventListener('learningResult', (event) => {
            const { isCorrect, newProficiency, oldProficiency } = event.detail;
            this.handleLearningResult(isCorrect, newProficiency, oldProficiency);
        });

        // 监听每日计划完成
        document.addEventListener('dailyPlanComplete', (event) => {
            this.handleDailyPlanComplete();
        });
    }

    handleLearningResult(isCorrect, newProficiency, oldProficiency) {
        if (this.disabled) {
            console.log('🚫 购物券奖励系统已禁用，跳过学习结果处理');
            return;
        }
    }

    handleDailyPlanComplete() {
        // 更新连续学习天数
        this.updateStreakDays();
        
        this.updateProgressDisplay();
    }

    updateStreakDays() {
        // 从localStorage获取连续天数（简化实现）
        const today = new Date().toDateString();
        const lastStudyDate = localStorage.getItem('lastStudyDate');
        const currentStreak = parseInt(localStorage.getItem('currentStreak') || '0');

        if (lastStudyDate === today) {
            // 今天已经学过了
            return;
        }

        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        
        if (lastStudyDate === yesterday.toDateString()) {
            // 连续学习
            this.streakDays = currentStreak + 1;
        } else {
            // 重新开始计算
            this.streakDays = 1;
        }

        localStorage.setItem('lastStudyDate', today);
        localStorage.setItem('currentStreak', this.streakDays.toString());

        // 检查连续学习奖励
        if (this.streakDays >= this.rewardConditions.streak) {
            this.grantVoucher(1, `连续学习${this.rewardConditions.streak}天`);
            // 重置连续天数，避免重复奖励
            this.streakDays = 0;
            localStorage.setItem('currentStreak', '0');
        }
    }

    // 保存五星计数到localStorage
    saveFiveStarCount() {
        localStorage.setItem('fiveStarCount', this.fiveStarCount.toString());
    }

    // 从localStorage加载五星计数
    loadFiveStarCount() {
        const saved = localStorage.getItem('fiveStarCount');
        if (saved) {
            this.fiveStarCount = parseInt(saved) || 0;
        }
    }

    grantVoucher(count, reason) {
        if (this.disabled) {
            console.log('🚫 购物券奖励系统已禁用，不会发放购物券');
            return;
        }

        // 更新奖励进度动画
        this.animateRewardProgress(reason);
        
        // 显示获得购物券的成功提示
        this.showVoucherSuccess(count, reason);
    }

    animateRewardProgress(reason) {
        // 根据原因找到对应的进度元素并添加动画
        let targetElement = null;
        
        if (reason.includes('连续答对')) {
            targetElement = document.getElementById('consecutive-progress');
        } else if (reason.includes('连续学习')) {
            targetElement = document.getElementById('streak-progress');
        }

        if (targetElement) {
            targetElement.classList.add('completed');
            setTimeout(() => {
                targetElement.classList.remove('completed');
            }, 3000);
        }
    }

    updateProgressDisplay() {
        console.log(`📊 📊 📊 开始更新进度显示: fiveStarCount=${this.fiveStarCount}`);
        
        // 更新五星单词累计进度
        const consecutiveElement = document.getElementById('consecutive-progress');
        console.log(`🔍 查找consecutive-progress元素:`, consecutiveElement);
        
        if (consecutiveElement) {
            const newText = `${this.fiveStarCount}/${this.rewardConditions.consecutive}`;
            const oldText = consecutiveElement.textContent;
            consecutiveElement.textContent = newText;
            console.log(`📱 界面更新成功: "${oldText}" → "${newText}"`);
            
            if (this.fiveStarCount >= this.rewardConditions.consecutive) {
                consecutiveElement.classList.add('active');
                console.log(`✅ 添加active样式`);
            } else {
                consecutiveElement.classList.remove('active');
                console.log(`🔄 移除active样式`);
            }
            
            // 强制触发重绘
            consecutiveElement.style.display = 'none';
            consecutiveElement.offsetHeight; // 触发重排
            consecutiveElement.style.display = '';
            console.log(`🔄 强制重绘完成`);
        } else {
            console.error(`❌ ❌ ❌ 找不到consecutive-progress元素！检查DOM结构！`);
            // 列出所有可能的元素
            const allElements = document.querySelectorAll('[id*="progress"]');
            console.log(`🔍 所有包含progress的元素:`, allElements);
        }

        // 更新连续学习进度
        const streakElement = document.getElementById('streak-progress');
        if (streakElement) {
            const currentStreak = parseInt(localStorage.getItem('currentStreak') || '0');
            streakElement.textContent = `${currentStreak}/${this.rewardConditions.streak}`;
            console.log(`📅 连续学习进度更新: ${currentStreak}/${this.rewardConditions.streak}`);
            if (currentStreak >= this.rewardConditions.streak) {
                streakElement.classList.add('active');
            }
        } else {
            console.log(`❌ 找不到streak-progress元素`);
        }
        
        console.log(`📊 进度显示更新完成`);
    }

    // 手动触发学习结果事件（已禁用）
    triggerLearningResult(isCorrect, newProficiency = 0, oldProficiency = 0) {
        console.log('🚫 购物券奖励系统已禁用，triggerLearningResult无效');
        return;
    }
    
    // 手动测试函数（供调试使用）
    testFiveStarReward() {
        console.log('🧪 🧪 🧪 测试五星奖励系统');
        console.log('🧪 当前五星计数:', this.fiveStarCount);
        
        // 模拟获得一个五星单词（不直接设置为9）
        console.log('🧪 模拟获得一个五星单词: 3星 → 5星');
        this.triggerLearningResult(true, 5, 3);
        
        console.log('🧪 测试完成，查看控制台日志和界面变化');
    }

    // 手动触发每日计划完成事件
    triggerDailyPlanComplete() {
        const event = new CustomEvent('dailyPlanComplete');
        document.dispatchEvent(event);
    }

    // 重置所有进度（用于测试）
    resetProgress() {
        this.consecutiveCorrect = 0;
        this.fiveStarCount = 0;
        this.streakDays = 0;
        localStorage.removeItem('lastStudyDate');
        localStorage.removeItem('currentStreak');
        localStorage.removeItem('fiveStarCount');
        this.updateProgressDisplay();
        console.log('🔄 购物券奖励进度已重置');
    }

    // 初始化时自动重置，避免旧逻辑的错误计数
    autoResetOnLoad() {
        const lastResetVersion = localStorage.getItem('voucherRewardVersion');
        const currentVersion = 'v3_cumulative_stars'; // 版本标识：累计五星计数
        
        if (lastResetVersion !== currentVersion) {
            console.log('🔄 检测到奖励逻辑更新，自动重置进度');
            this.resetProgress();
            localStorage.setItem('voucherRewardVersion', currentVersion);
        }
    }
    
    // 更新调试信息到页面
    updateDebugInfo(action, data) {
        try {
            const debugLastTrigger = document.getElementById('debug-last-trigger');
            if (debugLastTrigger) {
                const timestamp = new Date().toLocaleTimeString();
                debugLastTrigger.textContent = `最后触发: ${action} (${timestamp})`;
                
                if (data) {
                    debugLastTrigger.title = JSON.stringify(data);
                }
            }
        } catch (error) {
            console.warn('更新调试信息失败:', error);
        }
    }
    
    // 显示购物券获得成功提示
    showVoucherSuccess(count, reason) {
        try {
            // 可以在这里添加一个简单的成功提示，不干扰学习
            console.log(`🎉 🎉 🎉 恭喜！您获得了 ${count} 张购物券！`);
            console.log(`🎯 获得原因: ${reason}`);
            
            // 临时高亮购物券奖励区域
            const rewardsBox = document.getElementById('voucher-rewards-box');
            if (rewardsBox) {
                rewardsBox.style.boxShadow = '0 0 20px #4CAF50';
                rewardsBox.style.borderColor = '#4CAF50';
                setTimeout(() => {
                    rewardsBox.style.boxShadow = '0 2px 8px rgba(255, 152, 0, 0.2)';
                    rewardsBox.style.borderColor = '#ffb74d';
                }, 3000);
            }
        } catch (error) {
            console.warn('显示购物券成功提示失败:', error);
        }
    }
}

// 全局函数，已禁用
function initVoucherRewards() {
    console.log('🚫 购物券奖励系统已禁用，initVoucherRewards无效');
    return null;
}

// 购物券奖励系统已禁用，不再自动初始化
// document.addEventListener('DOMContentLoaded', () => {
//     window.voucherRewardsSystem = new VoucherRewardsSystem();
// });
console.log('🚫 购物券奖励系统已完全禁用');

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VoucherRewardsSystem;
}