/**
 * 缓存管理器 - 处理图片和静态资源缓存问题
 */

class CacheManager {
    constructor() {
        this.cacheBuster = Date.now();
    }

    /**
     * 清除所有缓存
     */
    clearAllCache() {
        try {
            // 清除浏览器缓存
            if ('caches' in window) {
                caches.keys().then(cacheNames => {
                    cacheNames.forEach(cacheName => {
                        caches.delete(cacheName);
                    });
                });
            }

            // 清除应用级缓存
            if (window.imageLoader) {
                window.imageLoader.clearCache();
            }

            // 重新加载页面清除内存缓存
            this.cacheBuster = Date.now();
            
            console.log('✅ 缓存已清除');
            return true;
        } catch (error) {
            console.error('❌ 清除缓存失败:', error);
            return false;
        }
    }

    /**
     * 获取带缓存清除的图片URL
     */
    getImageUrl(word, extension = 'jpg') {
        return `/static/images/words/${word}.${extension}?cb=${this.cacheBuster}&t=${Date.now()}`;
    }

    /**
     * 强制刷新图片缓存
     */
    refreshImageCache(word) {
        const imgElements = document.querySelectorAll(`img[src*="${word}"]`);
        imgElements.forEach(img => {
            const newUrl = this.getImageUrl(word);
            img.src = newUrl;
        });
    }

    /**
     * 检查并修复格式错误的图片
     */
    async checkAndFixImageFormats() {
        const formatIssues = [];
        
        // 检查已知的格式问题
        const knownIssues = [
            { word: 'rice_cooker', incorrectExt: 'jpg', correctExt: 'webp' }
        ];

        for (const issue of knownIssues) {
            try {
                // 测试正确格式的图片是否存在
                const correctUrl = `/static/images/words/${issue.word}.${issue.correctExt}`;
                const response = await fetch(correctUrl, { method: 'HEAD' });
                
                if (response.ok) {
                    formatIssues.push({
                        word: issue.word,
                        issue: `文件实际为${issue.correctExt}格式，但请求的是${issue.incorrectExt}`,
                        correctUrl: correctUrl
                    });
                }
            } catch (error) {
                console.warn(`检查${issue.word}格式时出错:`, error);
            }
        }

        return formatIssues;
    }
}

// 全局实例
window.cacheManager = new CacheManager();

// 添加调试功能
window.debugCache = {
    clear: () => window.cacheManager.clearAllCache(),
    refresh: (word) => window.cacheManager.refreshImageCache(word),
    checkFormats: () => window.cacheManager.checkAndFixImageFormats()
};

// 自动检查和修复格式问题
document.addEventListener('DOMContentLoaded', async () => {
    const issues = await window.cacheManager.checkAndFixImageFormats();
    if (issues.length > 0) {
        console.warn('🔧 发现图片格式问题:', issues);
    }
});

console.log('📦 缓存管理器已加载');