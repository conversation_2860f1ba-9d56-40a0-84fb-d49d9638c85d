/**
 * Pattern学习辅助功能
 * 提供单词pattern提示和相似单词推荐
 * 版本: 2.1.0 - 修复pattern_id问题
 */

class PatternHelper {
    constructor() {
        this.apiBase = '/api';
        this.currentWordId = null;
        this.currentPatternData = null;
        this.isVisible = false;
        this.isPanelOpen = false;
        this.interactionCount = 0;
        
        this.init();
    }
    
    init() {
        this.createPatternUI();
        this.bindEvents();
        
        // 监听单词切换事件
        document.addEventListener('wordChanged', (event) => {
            this.onWordChanged(event.detail);
        });
        
        // 监听学习会话开始事件
        document.addEventListener('learningSessionStarted', () => {
            this.sessionId = 'session_' + Date.now();
        });
    }
    
    createPatternUI() {
        // 创建浮动图标
        const floatingIcon = document.createElement('div');
        floatingIcon.id = 'pattern-floating-icon';
        floatingIcon.className = 'pattern-floating-icon hidden';
        floatingIcon.innerHTML = `
            <div class="icon-wrapper">
                <span class="icon">🦄</span>
                <div class="badge hidden">0</div>
            </div>
        `;
        
        // 创建右侧滑出面板
        const slidePanel = document.createElement('div');
        slidePanel.id = 'pattern-slide-panel';
        slidePanel.className = 'pattern-slide-panel';
        slidePanel.innerHTML = `
            <div class="panel-header">
                <h4>🧠 语言学专家推荐</h4>
                <div class="expert-badge">专业语言学分析</div>
                <button class="panel-close-btn" title="关闭">×</button>
            </div>
            <div class="panel-content">
                <div class="pattern-recommendations">
                    <!-- 推荐内容将在这里动态填充 -->
                </div>
            </div>
            <div class="pattern-feedback">
                <span class="feedback-label">这些提示对你有帮助吗？</span>
                <div class="feedback-buttons">
                    <button class="feedback-btn helpful" data-type="helpful">👍 有帮助</button>
                    <button class="feedback-btn not-helpful" data-type="not_helpful">👎 没帮助</button>
                </div>
            </div>
        `;
        
        // 插入到页面中
        const body = document.body;
        body.appendChild(floatingIcon);
        body.appendChild(slidePanel);
        
        this.floatingIcon = floatingIcon;
        this.slidePanel = slidePanel;
    }
    
    bindEvents() {
        // 浮动图标点击事件
        this.floatingIcon.addEventListener('click', () => {
            this.openPanel();
        });
        
        // 关闭面板按钮
        this.slidePanel.querySelector('.panel-close-btn').addEventListener('click', () => {
            this.closePanel();
        });
        
        // 反馈按钮
        this.slidePanel.querySelectorAll('.feedback-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.recordFeedback(e.target.dataset.type);
            });
        });
        
        // 点击相似单词
        this.slidePanel.addEventListener('click', (e) => {
            if (e.target.classList.contains('similar-word')) {
                this.recordInteraction('click');
                this.highlightSimilarWord(e.target);
            }
        });
        
        // 点击面板外部关闭
        document.addEventListener('click', (e) => {
            if (this.isPanelOpen && 
                !this.slidePanel.contains(e.target) && 
                !this.floatingIcon.contains(e.target)) {
                this.closePanel();
            }
        });
    }
    
    async onWordChanged(wordData) {
        console.log('🦄 Pattern Helper: 收到单词切换事件', wordData);

        if (!wordData || !wordData.id) {
            console.log('🦄 Pattern Helper: 无效的单词数据');
            return;
        }

        this.currentWordId = wordData.id;
        this.interactionCount = 0;

        console.log('🦄 Pattern Helper: 开始加载推荐，单词ID:', this.currentWordId);

        // 立即加载数据，但不显示面板
        this.loadPatternSuggestions();
    }
    
    async loadPatternSuggestions() {
        if (!this.currentWordId) return;
        
        try {
            const response = await fetch(`${this.apiBase}/word_pattern_suggestions/${this.currentWordId}`, {
                method: 'GET',
                credentials: 'same-origin'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const result = await response.json();

            // 添加调试信息
            console.log('API响应:', result);
            console.log('单词ID:', this.currentWordId);

            if (result.success && result.data) {
                console.log('🦄 推荐数据:', result.data);
                console.log('🦄 是否有推荐:', result.data.has_recommendations);
                console.log('🦄 推荐数量:', result.data.recommendations?.length || 0);

                this.currentPatternData = result.data;
                console.log('🦄 开始渲染推荐');
                this.renderPatternSuggestions(result.data);
                console.log('🦄 开始显示浮动图标');
                this.showFloatingIcon();
                this.recordInteraction('view');
            } else {
                console.log('🦄 没有找到相关的pattern建议');
                console.log('🦄 API响应详情:', result);
                this.hideFloatingIcon();
            }
            
        } catch (error) {
            console.error('加载pattern建议失败:', error);
            this.hideFloatingIcon();
        }
    }
    
    renderPatternSuggestions(data) {
        const contentDiv = this.slidePanel.querySelector('.pattern-recommendations');
        const badge = this.floatingIcon.querySelector('.badge');
        
        if (!data.has_recommendations) {
            contentDiv.innerHTML = '<div class="no-patterns">暂无相似单词提示</div>';
            badge.classList.add('hidden');
            return;
        }
        
        // 检查是否是语言学专家推荐
        const isLinguisticExpert = data.recommendations.some(rec =>
            rec.pattern_info && rec.pattern_info.is_linguistic_expert
        );

        console.log('🦄 是否语言学专家推荐:', isLinguisticExpert);
        console.log('🦄 推荐详情:', data.recommendations.map(rec => ({
            pattern_type: rec.pattern_info?.pattern_type,
            is_linguistic_expert: rec.pattern_info?.is_linguistic_expert,
            similar_words_count: rec.similar_words?.length
        })));

        if (isLinguisticExpert) {
            console.log('🦄 使用语言学专家渲染');
            console.log('使用语言学专家推荐渲染');
            this.renderLinguisticExpertRecommendations(data, contentDiv, badge);
            return;
        }
        
        // 传统推荐渲染逻辑
        this.renderTraditionalRecommendations(data, contentDiv, badge);
    }
    
    renderLinguisticExpertRecommendations(data, contentDiv, badge) {
        console.log('🦄 开始渲染语言学专家推荐');
        console.log('🦄 推荐数据:', data);
        console.log('🦄 contentDiv:', contentDiv);
        console.log('🦄 badge:', badge);
        let totalWords = 0;
        let html = `
            <div class="expert-recommendation-header">
                <div class="expert-indicator">
                    <span class="expert-icon">👨‍🎓</span>
                    <span class="expert-text">基于语言学专家标注的准确推荐</span>
                </div>
                <div class="recommendation-stats">
                    <span class="stats-text">为您找到 ${data.recommendations.length} 个学习组</span>
                </div>
            </div>
        `;

        // 渲染每个推荐组
        data.recommendations.forEach((rec, index) => {
            const categoryIcon = this.getLinguisticCategoryIcon(rec.pattern_info.pattern_type);
            const priorityClass = this.getPriorityClass(rec.pattern_info.educational_value);
            totalWords += rec.similar_words.length;

            html += `
                <div class="linguistic-recommendation-group ${priorityClass}" data-category="${rec.pattern_info.pattern_type}">
                    <div class="category-header" onclick="this.parentElement.classList.toggle('expanded')">
                        <div class="header-left">
                            <span class="category-icon">${categoryIcon}</span>
                            <span class="category-name">${rec.pattern_info.pattern_name}</span>
                            <span class="word-count">(${rec.similar_words.length}个单词)</span>
                        </div>
                        <div class="header-right">
                            <span class="educational-value ${priorityClass}">${rec.pattern_info.educational_value}</span>
                            <span class="expand-icon">▼</span>
                        </div>
                    </div>
                    <div class="recommendation-content">
                        <div class="linguistic-explanation">
                            <span class="explanation-icon">💡</span>
                            ${rec.recommendation_reason}
                        </div>
                        ${rec.pattern_info.linguistic_principle ? `
                            <div class="linguistic-principle">
                                <span class="principle-label">🧠 学习原理：</span>
                                <span class="principle-text">${rec.pattern_info.linguistic_principle}</span>
                            </div>
                        ` : ''}
                        <div class="similar-words-grid">
                            ${rec.similar_words.map((word, wordIndex) => `
                                <div class="linguistic-similar-word" data-word-id="${word.word_id}"
                                     onclick="this.classList.toggle('selected')"
                                     style="animation-delay: ${wordIndex * 0.1}s">
                                    <div class="word-text">
                                        <strong class="word-english">${word.english_word}</strong>
                                        <span class="word-meaning">${word.chinese_meaning}</span>
                                    </div>
                                    <div class="word-meta">
                                        <div class="proficiency-bar">
                                            <div class="proficiency-fill" style="width: ${word.proficiency}%"></div>
                                            <span class="proficiency-text">${Math.round(word.proficiency)}%</span>
                                        </div>
                                        <span class="status-indicator ${word.learning_status}">${this.getStatusText(word.learning_status)}</span>
                                    </div>
                                    <div class="word-actions">
                                        <button class="action-btn review-btn" onclick="event.stopPropagation(); this.parentElement.parentElement.parentElement.recordWordAction('review', ${word.word_id})" title="复习此单词">
                                            📚
                                        </button>
                                        <button class="action-btn compare-btn" onclick="event.stopPropagation(); this.parentElement.parentElement.parentElement.recordWordAction('compare', ${word.word_id})" title="对比学习">
                                            🔍
                                        </button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                        <div class="group-actions">
                            <button class="group-action-btn" onclick="this.parentElement.parentElement.parentElement.recordGroupAction('helpful', '${rec.pattern_info.pattern_type}')">
                                👍 这组推荐有帮助
                            </button>
                            <button class="group-action-btn secondary" onclick="this.parentElement.parentElement.parentElement.recordGroupAction('not_helpful', '${rec.pattern_info.pattern_type}')">
                                👎 这组推荐没帮助
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        // 添加整体反馈区域
        html += `
            <div class="overall-feedback">
                <div class="feedback-title">💬 您对这次推荐的整体感受如何？</div>
                <div class="feedback-options">
                    <button class="feedback-option excellent" onclick="this.parentElement.parentElement.parentElement.recordOverallFeedback('excellent')">
                        🌟 非常有帮助
                    </button>
                    <button class="feedback-option good" onclick="this.parentElement.parentElement.parentElement.recordOverallFeedback('good')">
                        👍 比较有帮助
                    </button>
                    <button class="feedback-option neutral" onclick="this.parentElement.parentElement.parentElement.recordOverallFeedback('neutral')">
                        😐 一般般
                    </button>
                    <button class="feedback-option poor" onclick="this.parentElement.parentElement.parentElement.recordOverallFeedback('poor')">
                        👎 没什么帮助
                    </button>
                </div>
            </div>
        `;

        console.log('🦄 设置HTML内容:', html.length, '字符');
        contentDiv.innerHTML = html;
        console.log('🦄 HTML内容已设置');

        // 更新徽章
        if (totalWords > 0) {
            badge.textContent = totalWords;
            badge.classList.remove('hidden');
            console.log('🦄 徽章已显示，单词数:', totalWords);
        } else {
            badge.classList.add('hidden');
            console.log('🦄 徽章已隐藏');
        }

        // 添加动画效果
        this.addRecommendationAnimations(contentDiv);
        console.log('🦄 语言学专家推荐渲染完成');
    }
    
    renderTraditionalRecommendations(data, contentDiv, badge) {
        // 按认知层次分组
        const groupedByLevel = this.groupRecommendationsByLevel(data.recommendations);
        let totalWords = 0;
        let html = '';
        
        // 按认知层次顺序渲染
        const levelOrder = ['basic', 'intermediate', 'advanced'];
        const levelInfo = {
            'basic': { name: '基础感知层', icon: '🎯', description: '拼写发音基础' },
            'intermediate': { name: '语义关联层', icon: '🧠', description: '词义理解深化' },
            'advanced': { name: '高级应用层', icon: '🚀', description: '语法搭配提升' }
        };
        
        levelOrder.forEach(level => {
            if (!groupedByLevel[level] || groupedByLevel[level].length === 0) return;
            
            const levelConfig = levelInfo[level];
            const levelRecommendations = groupedByLevel[level];
            
            // 按维度分组
            const groupedByDimension = this.groupRecommendationsByDimension(levelRecommendations);
            
            html += `
                <div class="cognitive-level-section" data-level="${level}">
                    <div class="level-header">
                        <span class="level-icon">${levelConfig.icon}</span>
                        <span class="level-name">${levelConfig.name}</span>
                        <span class="level-description">${levelConfig.description}</span>
                    </div>
            `;
            
            // 按维度顺序渲染
            const dimensionOrder = ['orthography', 'semantic', 'morphology', 'collocation'];
            const dimensionInfo = {
                'orthography': { name: '拼写发音', icon: '📝', color: '#4CAF50' },
                'semantic': { name: '词义关联', icon: '💭', color: '#2196F3' },
                'morphology': { name: '构词变形', icon: '🔧', color: '#FF9800' },
                'collocation': { name: '搭配用法', icon: '🎯', color: '#9C27B0' }
            };
            
            dimensionOrder.forEach(dimension => {
                if (!groupedByDimension[dimension] || groupedByDimension[dimension].length === 0) return;
                
                const dimensionConfig = dimensionInfo[dimension];
                const dimensionRecommendations = groupedByDimension[dimension];
                
                html += `
                    <div class="dimension-section" data-dimension="${dimension}">
                        <div class="dimension-header" style="border-left-color: ${dimensionConfig.color}">
                            <span class="dimension-icon">${dimensionConfig.icon}</span>
                            <span class="dimension-name">${dimensionConfig.name}</span>
                        </div>
                `;
                
                // 渲染该维度下的所有推荐
                dimensionRecommendations.forEach((rec, index) => {
                    const isFirst = index === 0;
                    totalWords += rec.similar_words.length;
                    
                    const typeIcon = this.getPatternTypeIcon(rec.pattern_info.pattern_type);
                    const conceptTag = rec.pattern_info.is_concept_integrated ? 
                        `<span class="concept-tag">🦄 ${rec.pattern_info.concept_group}</span>` : '';
                    
                    html += `
                        <div class="pattern-group ${isFirst ? 'primary' : ''}" data-pattern-type="${rec.pattern_info.pattern_type}">
                            <div class="pattern-info">
                                <div class="pattern-type">
                                    <span class="pattern-type-icon">${typeIcon}</span>
                                    <span class="pattern-name">${rec.pattern_info.pattern_name || rec.pattern_info.pattern_type}</span>
                                    ${conceptTag}
                                </div>
                                <div class="pattern-reason">${rec.recommendation_reason}</div>
                                ${rec.pattern_info.match_reason ? `<div class="pattern-description">匹配原因：${rec.pattern_info.match_reason}</div>` : ''}
                            </div>
                            <div class="similar-words">
                                ${rec.similar_words.map(word => `
                                    <div class="similar-word" data-word-id="${word.word_id}">
                                        <strong>${word.english_word}</strong>
                                        <div class="word-meaning">${word.chinese_meaning}</div>
                                        <div class="word-status">${this.getStatusText(word.learning_status)} 
                                            <span class="proficiency-badge">${Math.round(word.proficiency)}%</span>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                });
                
                html += `</div>`; // 关闭 dimension-section
            });
            
            html += `</div>`; // 关闭 cognitive-level-section
        });
        
        contentDiv.innerHTML = html;
        
        // 更新徽章数字
        if (totalWords > 0) {
            badge.textContent = totalWords;
            badge.classList.remove('hidden');
        } else {
            badge.classList.add('hidden');
        }
    }
    
    groupRecommendationsByLevel(recommendations) {
        const groups = {
            'basic': [],
            'intermediate': [],
            'advanced': []
        };
        
        recommendations.forEach(rec => {
            const level = rec.pattern_info.cognitive_level || 'basic';
            if (groups[level]) {
                groups[level].push(rec);
            }
        });
        
        return groups;
    }
    
    groupRecommendationsByDimension(recommendations) {
        const groups = {
            'orthography': [],
            'semantic': [],
            'morphology': [],
            'collocation': []
        };
        
        recommendations.forEach(rec => {
            const dimension = rec.pattern_info.dimension_category || 'semantic';
            if (groups[dimension]) {
                groups[dimension].push(rec);
            }
        });
        
        return groups;
    }
    
    showFloatingIcon() {
        console.log('🦄 Pattern Helper: 显示浮动图标');
        this.floatingIcon.classList.remove('hidden');
        this.isVisible = true;

        // 添加淡入动画
        setTimeout(() => {
            this.floatingIcon.classList.add('visible');
            console.log('🦄 Pattern Helper: 浮动图标动画完成');
        }, 10);
    }
    
    hideFloatingIcon() {
        this.floatingIcon.classList.remove('visible');
        this.isVisible = false;
        
        setTimeout(() => {
            this.floatingIcon.classList.add('hidden');
        }, 300);
    }
    
    openPanel() {
        this.slidePanel.classList.add('open');
        this.isPanelOpen = true;
        document.body.classList.add('pattern-panel-open');
        this.recordInteraction('open_panel');
    }
    
    closePanel() {
        this.slidePanel.classList.remove('open');
        this.isPanelOpen = false;
        document.body.classList.remove('pattern-panel-open');
    }
    
    
    highlightSimilarWord(element) {
        // 移除其他高亮
        this.slidePanel.querySelectorAll('.similar-word.highlighted').forEach(el => {
            el.classList.remove('highlighted');
        });
        
        // 添加高亮
        element.classList.add('highlighted');
        
        // 3秒后移除高亮
        setTimeout(() => {
            element.classList.remove('highlighted');
        }, 3000);
    }
    
    async recordInteraction(type) {
        if (!this.currentPatternData || !this.currentWordId) return;
        
        this.interactionCount++;
        
        // 获取第一个pattern的ID作为主要pattern
        const primaryPattern = this.currentPatternData.recommendations[0];
        if (!primaryPattern) return;
        
        const patternId = primaryPattern.pattern_info.pattern_id;
        
        try {
            await fetch(`${this.apiBase}/pattern_interaction`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    pattern_id: patternId,
                    word_id: this.currentWordId,
                    interaction_type: type,
                    session_id: this.sessionId
                })
            });
        } catch (error) {
            console.error('记录交互失败:', error);
        }
    }
    
    async recordFeedback(feedbackType) {
        await this.recordInteraction(feedbackType);
        
        // 显示感谢提示
        const feedbackDiv = this.patternCard.querySelector('.pattern-feedback');
        const originalHTML = feedbackDiv.innerHTML;
        
        feedbackDiv.innerHTML = '<span class="feedback-thanks">感谢你的反馈！ 🙏</span>';
        
        // 3秒后恢复原样
        setTimeout(() => {
            feedbackDiv.innerHTML = originalHTML;
            this.bindFeedbackEvents();
        }, 3000);
    }
    
    bindFeedbackEvents() {
        this.patternCard.querySelectorAll('.feedback-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.recordFeedback(e.target.dataset.type);
            });
        });
    }
    
    getPatternTypeIcon(patternType) {
        const icons = {
            // 拼写发音维度 (orthography)
            'letter_combo': '🔤',
            'phonetic': '🔊',
            'rhyme': '🎵',
            'syllable': '✨',
            'first_letter': '🅰️',
            'silent_letter': '🤫',
            'word_length': '📏',
            
            // 词义关联维度 (semantic)
            'semantic': '🧠',
            'theme': '🎯',
            'synonym': '🔄',
            'antonym': '↔️',
            
            // 构词变形维度 (morphology)
            'morphology': '🔧',
            'root': '🌳',
            'prefix': '⬅️',
            'suffix': '➡️',
            'adjective_forms': '📊',
            'plural_forms': '🔢',
            'verb_forms': '⚡',
            
            // 搭配用法维度 (collocation)
            'collocation': '🎯',
            'usage_pattern': '💡',
            'contextual': '📖',
            'grammar': '📚',
            
            // 其他类型
            'similar_spelling': '🔍',
            'meaning_groups': '🎨'
        };
        return icons[patternType] || '🦄';
    }
    
    getDimensionIcon(dimension) {
        const icons = {
            'orthography': '📝',
            'semantic': '💭', 
            'morphology': '🔧',
            'collocation': '🎯'
        };
        return icons[dimension] || '📝';
    }
    
    getCognitiveLevelIcon(level) {
        const icons = {
            'basic': '🎯',
            'intermediate': '🧠',
            'advanced': '🚀'
        };
        return icons[level] || '🎯';
    }
    
    getStatusText(status) {
        const statusTexts = {
            'new': '新词',
            'review': '复习',
            'attention': '生词本'
        };
        return statusTexts[status] || status;
    }
    
    getLinguisticCategoryIcon(categoryType) {
        const icons = {
            // 前缀类别
            'true_prefix': '🔤',
            'prefix': '⬅️',
            
            // 语义类别已移除
            // 学习集群类别已移除
            
            // 对比类别
            'contrast': '⚖️',
            'antonym': '🔄',
            
            // 词汇关系
            'morphology': '🔧',
            'etymology': '📚'
        };
        return icons[categoryType] || '🔍';
    }
    
    // 公开方法：手动触发pattern建议
    loadSuggestionsForWord(wordId) {
        this.currentWordId = wordId;
        this.loadPatternSuggestions();
    }
    
    // 公开方法：检查是否支持pattern功能
    isPatternSupportEnabled() {
        return this.currentPatternData && this.currentPatternData.has_recommendations;
    }

    getPriorityClass(educationalValue) {
        const value = educationalValue?.toLowerCase();
        if (value === '很高' || value === 'very_high') return 'priority-very-high';
        if (value === '高' || value === 'high') return 'priority-high';
        if (value === '中等' || value === 'medium') return 'priority-medium';
        return 'priority-low';
    }

    addRecommendationAnimations(container) {
        // 添加渐入动画
        const groups = container.querySelectorAll('.linguistic-recommendation-group');
        groups.forEach((group, index) => {
            group.style.opacity = '0';
            group.style.transform = 'translateY(20px)';

            setTimeout(() => {
                group.style.transition = 'all 0.3s ease';
                group.style.opacity = '1';
                group.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // 默认展开第一个高优先级组
        const firstHighPriority = container.querySelector('.priority-very-high, .priority-high');
        if (firstHighPriority) {
            firstHighPriority.classList.add('expanded');
        }
    }

    recordWordAction(action, wordId) {
        // 记录单词级别的操作
        this.recordInteraction(`word_${action}`);

        // 发送详细的操作记录
        fetch('/api/pattern/word_action', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: action,
                word_id: wordId,
                target_word_id: this.currentWordId,
                session_id: this.sessionId
            })
        }).catch(error => {
            console.error('记录单词操作失败:', error);
        });

        // 视觉反馈
        const wordElement = document.querySelector(`[data-word-id="${wordId}"]`);
        if (wordElement) {
            wordElement.classList.add('action-performed');
            setTimeout(() => {
                wordElement.classList.remove('action-performed');
            }, 1000);
        }
    }

    recordGroupAction(action, groupType) {
        // 记录组级别的反馈
        this.recordInteraction(`group_${action}`);

        // 发送详细的反馈记录
        fetch('/api/pattern/group_feedback', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                feedback_type: action,
                group_type: groupType,
                target_word_id: this.currentWordId,
                session_id: this.sessionId
            })
        }).catch(error => {
            console.error('记录组反馈失败:', error);
        });

        // 视觉反馈
        const groupElement = document.querySelector(`[data-category="${groupType}"]`);
        if (groupElement) {
            const actionButtons = groupElement.querySelectorAll('.group-action-btn');
            actionButtons.forEach(btn => {
                btn.disabled = true;
                btn.style.opacity = '0.5';
            });

            // 显示感谢消息
            const actionsDiv = groupElement.querySelector('.group-actions');
            const thankYou = document.createElement('div');
            thankYou.className = 'feedback-thanks';
            thankYou.textContent = action === 'helpful' ? '✅ 感谢您的反馈！' : '📝 我们会改进这类推荐';
            actionsDiv.appendChild(thankYou);
        }
    }

    recordOverallFeedback(rating) {
        // 记录整体反馈
        this.recordInteraction(`overall_${rating}`);

        // 发送详细的整体反馈
        fetch('/api/pattern/overall_feedback', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                rating: rating,
                target_word_id: this.currentWordId,
                session_id: this.sessionId,
                recommendations_count: this.currentPatternData?.recommendations?.length || 0
            })
        }).catch(error => {
            console.error('记录整体反馈失败:', error);
        });

        // 视觉反馈
        const feedbackOptions = document.querySelectorAll('.feedback-option');
        feedbackOptions.forEach(option => {
            option.disabled = true;
            option.style.opacity = '0.5';
        });

        // 显示感谢消息
        const feedbackDiv = document.querySelector('.overall-feedback');
        const thankYou = document.createElement('div');
        thankYou.className = 'overall-feedback-thanks';
        thankYou.innerHTML = `
            <div class="thanks-icon">🙏</div>
            <div class="thanks-text">感谢您的宝贵反馈！这将帮助我们提供更好的推荐。</div>
        `;
        feedbackDiv.appendChild(thankYou);

        // 3秒后自动关闭面板
        setTimeout(() => {
            this.closePanel();
        }, 3000);
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    if (typeof window.patternHelper === 'undefined') {
        window.patternHelper = new PatternHelper();
    }
});

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PatternHelper;
}