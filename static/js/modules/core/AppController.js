/**
 * 🚀 前后端分离版本 - 简化的应用控制器
 * 只负责前端UI协调，业务逻辑由后端API处理
 */

import { apiService } from '../services/ApiService.js';

class AppController {
    constructor() {
        this.initialized = false;
        this.currentPage = 'learning';
    }

    // 🚀 初始化应用
    async init() {
        console.log('🚀 AppController 初始化开始...');

        try {
            // 检查后端API连接
            await this.checkApiConnection();
            
            // 初始化前端UI组件
            this.initializeUIComponents();
            
            this.initialized = true;
            console.log('✅ AppController 初始化完成');
            
            return { success: true };
        } catch (error) {
            console.error('❌ AppController 初始化失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 🚀 检查API连接
    async checkApiConnection() {
        try {
            const response = await apiService.getUserStatus();
            if (response.success) {
                console.log('✅ 后端API连接正常');
            } else {
                console.warn('⚠️ 后端API响应异常:', response);
            }
        } catch (error) {
            console.warn('⚠️ 后端API连接失败，使用兼容模式:', error.message);
        }
    }

    // 🔧 初始化前端UI组件
    initializeUIComponents() {
        console.log('🎨 初始化前端UI组件...');

        // 绑定全局事件监听器
        this.bindGlobalEvents();

        // 初始化页面特定组件
        if (this.currentPage === 'learning') {
            this.initializeLearningPage();
        }

        console.log('✅ 前端UI组件初始化完成');
    }

    // 🔧 绑定全局事件
    bindGlobalEvents() {
        // 键盘快捷键支持
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && event.target.tagName === 'INPUT') {
                event.preventDefault();
                this.handleEnterKey(event.target);
            }
        });

        console.log('✅ 全局事件绑定完成');
    }

    // 🔧 学习页面初始化
    initializeLearningPage() {
        console.log('📚 初始化学习页面组件...');

        // 音频控制组件
        this.initializeAudioControls();

        // 输入模式组件
        this.initializeInputModeControls();

        console.log('✅ 学习页面组件初始化完成');
    }

    // 🔧 音频控制初始化
    initializeAudioControls() {
        const pronounceButton = document.getElementById('pronounce-button');
        if (pronounceButton) {
            pronounceButton.addEventListener('click', () => {
                console.log('🔊 音频播放请求');
                // 实际播放逻辑由 learning_logic.js 处理
            });
        }
    }

    // 🔧 输入模式控制初始化
    initializeInputModeControls() {
        const userInput = document.getElementById('user-input');
        const spellingInput = document.getElementById('spelling-input');

        if (userInput) {
            userInput.addEventListener('keydown', (event) => {
                if (event.key === 'Enter') {
                    console.log('📝 传统模式输入提交');
                }
            });
        }

        if (spellingInput) {
            spellingInput.addEventListener('input', (event) => {
                console.log('🔤 拼写模式输入变化');
            });
        }
    }

    // 🔧 处理回车键
    handleEnterKey(inputElement) {
        const inputId = inputElement.id;
        
        switch (inputId) {
            case 'user-input':
                // 传统模式检查拼写
                if (typeof window.checkSpelling === 'function') {
                    window.checkSpelling();
                }
                break;
            case 'spelling-input':
                // 拼写模式继续
                console.log('🔤 拼写模式回车');
                break;
            default:
                console.log('⚡ 通用回车处理');
        }
    }

    // 🔧 获取初始化状态
    isInitialized() {
        return this.initialized;
    }
}

// 🚀 导出
export { AppController };

console.log('✅ 简化版 AppController 加载完成 - 只保留前端UI协调逻辑');