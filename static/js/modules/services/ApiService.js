/**
 * 🚀 前后端分离版本 - 简化的API服务
 * 从685行缩减到约100行，只保留API调用逻辑
 */

class ApiService {
    constructor() {
        this.baseUrl = '';
        this.requestId = 0;
    }

    // 🚀 通用API调用方法
    async callApi(endpoint, method = 'GET', data = null) {
        const requestId = ++this.requestId;
        console.log(`🌐 API请求 #${requestId}: ${method} ${endpoint}`, data);

        try {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            if (data && method !== 'GET') {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(endpoint, options);
            const result = await response.json();

            console.log(`✅ API响应 #${requestId}:`, result);
            return result;
        } catch (error) {
            console.error(`❌ API错误 #${requestId}:`, error);
            throw error;
        }
    }

    // 🚀 用户状态API
    async getUserStatus() {
        return this.callApi('/api/user_status');
    }

    // 🚀 每日学习计划API
    async getDailyLearningPlan(filters = {}) {
        return this.callApi('/api/daily_learning_plan', 'POST', filters);
    }

    // 🚀 单词过滤API
    async filterWords(words, filters) {
        return this.callApi('/api/filter_words', 'POST', { words, filters });
    }

    // 🚀 奖励系统API
    async calculateRewards(learningResult) {
        return this.callApi('/api/rewards_system/calculate', 'POST', { learning_result: learningResult });
    }

    // 🚀 购物券API
    async updateVouchers(vouchersChange, reason) {
        return this.callApi('/api/voucher/update', 'POST', { vouchers_change: vouchersChange, reason });
    }

    // 🚀 AI提示API
    async getHint(word, hintLevel) {
        return this.callApi('/api/ai/hint', 'POST', { word, hint_level: hintLevel });
    }

    // 🚀 AI记忆帮助API
    async getMemoryHelp(word) {
        return this.callApi('/api/ai/memory_help', 'POST', { word });
    }

    // 🚀 拼写检查API
    async checkSpelling(userInput, correctAnswer) {
        return this.callApi('/api/spell_check', 'POST', { user_input: userInput, correct_answer: correctAnswer });
    }

    // 🚀 学习会话API
    async submitAnswer(wordData, userInput, isCorrect, duration) {
        return this.callApi('/api/learning_session/submit', 'POST', {
            word: wordData.word,
            user_input: userInput,
            is_correct: isCorrect,
            duration_seconds: duration
        });
    }
}

// 🚀 导出单例
export const apiService = new ApiService();
export { ApiService };

console.log('✅ 简化版 ApiService 加载完成 - 从685行缩减到约100行');