/**
 * 学习激励彩蛋系统
 * 负责显示激励内容和动画效果
 */

class EncouragementSystem {
    constructor() {
        this.isShowing = false;
        this.queue = [];
        // this.voucherCount = 0; // 购物券功能已禁用
        this.init();
    }

    init() {
        // 创建激励显示容器
        this.createEncouragementContainer();

        // 绑定事件监听器
        this.bindEvents();

        // 初始化购物券计数器已禁用
        // this.initVoucherCounter();
    }

    createEncouragementContainer() {
        // 检查是否已存在
        if (document.getElementById('encouragement-container')) {
            return;
        }

        const container = document.createElement('div');
        container.id = 'encouragement-container';
        container.innerHTML = `
            <div class="encouragement-overlay" id="encouragement-overlay">
                <div class="encouragement-modal" id="encouragement-modal">
                    <div class="encouragement-content">
                        <div class="encouragement-icon">🎉</div>
                        <div class="encouragement-text" id="encouragement-text">
                            太棒了！你答对了！
                        </div>
                        <div class="encouragement-close" id="encouragement-close">
                            ✨ 继续学习 ✨
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .encouragement-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .encouragement-overlay.show {
                opacity: 1;
                visibility: visible;
            }

            .encouragement-modal {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 20px;
                padding: 30px;
                text-align: center;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                transform: scale(0.5) rotate(-10deg);
                transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
                max-width: 400px;
                margin: 20px;
            }

            .encouragement-overlay.show .encouragement-modal {
                transform: scale(1) rotate(0deg);
            }

            .encouragement-content {
                color: white;
            }

            .encouragement-icon {
                font-size: 4rem;
                margin-bottom: 20px;
                animation: bounce 1s infinite;
            }

            .encouragement-text {
                font-size: 1.5rem;
                font-weight: bold;
                margin-bottom: 25px;
                line-height: 1.4;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            }

            .encouragement-close {
                background: rgba(255, 255, 255, 0.2);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 25px;
                padding: 12px 24px;
                cursor: pointer;
                transition: all 0.3s ease;
                display: inline-block;
                font-weight: bold;
            }

            .encouragement-close:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            }

            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% {
                    transform: translateY(0);
                }
                40% {
                    transform: translateY(-10px);
                }
                60% {
                    transform: translateY(-5px);
                }
            }

            @keyframes sparkle {
                0% { opacity: 0; transform: scale(0); }
                50% { opacity: 1; transform: scale(1); }
                100% { opacity: 0; transform: scale(0); }
            }

            .sparkle {
                position: absolute;
                width: 10px;
                height: 10px;
                background: #ffd700;
                border-radius: 50%;
                animation: sparkle 1s ease-in-out;
                pointer-events: none;
            }

            /* 移动端适配 */
            @media (max-width: 768px) {
                .encouragement-modal {
                    margin: 10px;
                    padding: 20px;
                }
                
                .encouragement-icon {
                    font-size: 3rem;
                }
                
                .encouragement-text {
                    font-size: 1.2rem;
                }
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(container);
    }

    bindEvents() {
        // 点击关闭按钮
        document.addEventListener('click', (e) => {
            if (e.target.id === 'encouragement-close') {
                this.hide();
            }
        });

        // 点击遮罩层关闭
        document.addEventListener('click', (e) => {
            if (e.target.id === 'encouragement-overlay') {
                this.hide();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isShowing) {
                this.hide();
            }
        });
    }

    show(encouragementData) {
        // 如果正在显示，加入队列
        if (this.isShowing) {
            this.queue.push(encouragementData);
            return;
        }

        this.isShowing = true;
        
        const overlay = document.getElementById('encouragement-overlay');
        const textElement = document.getElementById('encouragement-text');
        const iconElement = overlay.querySelector('.encouragement-icon');

        // 设置内容
        if (encouragementData && encouragementData.text) {
            textElement.textContent = encouragementData.text;
        }

        // 根据类型设置不同的图标和样式
        if (encouragementData && encouragementData.type) {
            this.setThemeByType(encouragementData.type, iconElement, overlay);
        }

        // 显示动画
        overlay.classList.add('show');

        // 添加闪烁效果
        this.addSparkleEffect();

        // 自动隐藏（5秒后）
        setTimeout(() => {
            if (this.isShowing) {
                this.hide();
            }
        }, 5000);

        // 记录显示日志
        console.log('🎉 显示激励彩蛋:', encouragementData);

        // 如果是711购物券，增加计数
        if (encouragementData && encouragementData.type === '711购物券彩蛋') {
            this.addVoucher();
        }
    }

    setThemeByType(type, iconElement, overlay) {
        const modal = overlay.querySelector('.encouragement-modal');
        
        // 重置样式
        modal.style.background = '';
        
        switch (type) {
            case '连续答对':
                iconElement.textContent = '🔥';
                modal.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
                break;
            case '完成学习':
                iconElement.textContent = '🏆';
                modal.style.background = 'linear-gradient(135deg, #feca57 0%, #ff9ff3 100%)';
                break;
            case '学习坚持':
                iconElement.textContent = '⭐';
                modal.style.background = 'linear-gradient(135deg, #48dbfb 0%, #0abde3 100%)';
                break;
            case '特殊成就':
                iconElement.textContent = '💎';
                modal.style.background = 'linear-gradient(135deg, #a55eea 0%, #26de81 100%)';
                break;
            case '711购物券彩蛋':
                iconElement.textContent = '🛒';
                modal.style.background = 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)';
                // 添加特殊的购物券样式
                this.addShoppingVoucherEffect(modal);
                break;
            default:
                iconElement.textContent = '🎉';
                modal.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        }
    }

    addShoppingVoucherEffect(modal) {
        // 为711购物券添加特殊效果

        // 添加购物券边框样式
        modal.style.border = '3px dashed #fff';
        modal.style.boxShadow = '0 0 30px rgba(255, 152, 0, 0.6), inset 0 0 20px rgba(255, 255, 255, 0.2)';

        // 添加购物券角标（移除固定金额，使用通用标识）
        const voucherCorner = document.createElement('div');
        voucherCorner.style.cssText = `
            position: absolute;
            top: -10px;
            right: -10px;
            background: #ff5722;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            transform: rotate(15deg);
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        `;
        voucherCorner.textContent = '🎁';  // 使用礼物图标代替固定金额
        modal.appendChild(voucherCorner);

        // 添加购物券孔洞效果
        const holes = document.createElement('div');
        holes.style.cssText = `
            position: absolute;
            top: 50%;
            left: -8px;
            width: 16px;
            height: 16px;
            background: rgba(0,0,0,0.1);
            border-radius: 50%;
            transform: translateY(-50%);
        `;
        modal.appendChild(holes);

        const holes2 = holes.cloneNode();
        holes2.style.left = 'calc(100% - 8px)';
        modal.appendChild(holes2);

        // 添加购物车动画
        this.addShoppingCartAnimation();
    }

    addShoppingCartAnimation() {
        // 创建飞行的购物车图标
        const cart = document.createElement('div');
        cart.style.cssText = `
            position: fixed;
            font-size: 2rem;
            z-index: 10001;
            pointer-events: none;
            left: 20px;
            top: 50%;
            animation: flyCart 3s ease-in-out;
        `;
        cart.textContent = '🛒';

        // 添加购物车飞行动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes flyCart {
                0% {
                    transform: translateX(-100px) translateY(0) rotate(0deg);
                    opacity: 0;
                }
                20% {
                    opacity: 1;
                }
                50% {
                    transform: translateX(calc(50vw - 50px)) translateY(-20px) rotate(10deg);
                }
                80% {
                    transform: translateX(calc(100vw - 20px)) translateY(0) rotate(-5deg);
                }
                100% {
                    transform: translateX(calc(100vw + 50px)) translateY(10px) rotate(0deg);
                    opacity: 0;
                }
            }
        `;

        document.head.appendChild(style);
        document.body.appendChild(cart);

        // 3秒后移除
        setTimeout(() => {
            if (cart.parentNode) {
                cart.parentNode.removeChild(cart);
            }
            if (style.parentNode) {
                style.parentNode.removeChild(style);
            }
        }, 3000);
    }

    addSparkleEffect() {
        const modal = document.querySelector('.encouragement-modal');
        const rect = modal.getBoundingClientRect();

        // 创建多个闪烁点
        for (let i = 0; i < 8; i++) {
            setTimeout(() => {
                const sparkle = document.createElement('div');
                sparkle.className = 'sparkle';
                sparkle.style.left = Math.random() * rect.width + 'px';
                sparkle.style.top = Math.random() * rect.height + 'px';
                
                modal.appendChild(sparkle);
                
                // 1秒后移除
                setTimeout(() => {
                    if (sparkle.parentNode) {
                        sparkle.parentNode.removeChild(sparkle);
                    }
                }, 1000);
            }, i * 200);
        }
    }

    hide() {
        const overlay = document.getElementById('encouragement-overlay');
        overlay.classList.remove('show');
        
        setTimeout(() => {
            this.isShowing = false;
            
            // 处理队列中的下一个激励
            if (this.queue.length > 0) {
                const next = this.queue.shift();
                setTimeout(() => this.show(next), 500);
            }
        }, 300);
    }

    // 购物券计数器管理
    initVoucherCounter() {
        console.log('🚫 购物券计数器已禁用');
        return;
    }

    addVoucher() {
        console.log('🚫 购物券增加功能已禁用');
        return;
    }

    fetchVoucherCountFromServer() {
        console.log('🚫 购物券获取功能已禁用');
        return;
    }

    addVoucherToServer(count = 1, reason = '学习激励') { // 🚫 购物券功能已禁用
        console.log('🚫 购物券服务已禁用，不会发放购物券');
        return;
    }

    updateVoucherDisplay() {
        console.log('🚫 购物券显示已禁用');
        return;
    }

    showVoucherIndicator() {
        console.log('🚫 购物券指示器已禁用');
        return;
    }

    hideVoucherIndicator() {
        console.log('🚫 购物券指示器已禁用');
        return;
    }

    // 重置购物券计数（已禁用）
    resetVoucherCount() {
        console.log('🚫 购物券重置已禁用');
        return;
    }

    // 静态方法，供外部调用
    static show(encouragementData) {
        if (!window.encouragementSystem) {
            window.encouragementSystem = new EncouragementSystem();
        }
        window.encouragementSystem.show(encouragementData);
    }
}

// 全局函数，方便调用
function showEncouragement(encouragementData) {
    EncouragementSystem.show(encouragementData);
}

// 购物券管理全局函数（已禁用）
function getVoucherCount() {
    console.log('🚫 购物券计数功能已禁用');
    return 0;
}

function resetVoucherCount() {
    console.log('🚫 全局购物券重置已禁用');
    return;
}

function addVoucher() {
    console.log('🚫 全局购物券功能已禁用');
    return;
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    window.encouragementSystem = new EncouragementSystem();
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EncouragementSystem;
}
