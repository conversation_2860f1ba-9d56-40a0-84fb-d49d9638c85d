/* Pattern Helper - 浮动图标 + 右侧滑出面板设计 */

/* 浮动图标样式 */
.pattern-floating-icon {
    position: fixed;
    bottom: 100px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pattern-floating-icon.visible {
    opacity: 1;
    transform: scale(1);
}

.pattern-floating-icon.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.pattern-floating-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

.pattern-floating-icon .icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pattern-floating-icon .icon {
    font-size: 24px;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2));
}

.pattern-floating-icon .badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    border: 2px solid white;
    transform: scale(1);
    transition: transform 0.2s ease;
}

.pattern-floating-icon .badge.hidden {
    display: none;
}

/* 右侧滑出面板 */
.pattern-slide-panel {
    position: fixed;
    top: 0;
    right: -30%;
    width: 30%;
    height: 100vh;
    background: #fafbfc;
    box-shadow: -4px 0 25px rgba(0,0,0,0.15);
    z-index: 1001;
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Roboto, sans-serif;
    font-size: 16px;
    line-height: 1.6;
}

.pattern-slide-panel.open {
    right: 0;
}

/* 面板头部 */
.panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-direction: column;
    gap: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.panel-header h4 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    line-height: 1.3;
}

.expert-badge {
    background: rgba(255,255,255,0.2);
    color: rgba(255,255,255,0.9);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 13px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.panel-close-btn {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: background 0.2s ease;
    position: absolute;
    top: 20px;
    right: 20px;
}

.panel-close-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* 面板内容区域 */
.panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
}

/* 相似单词推荐区域 */
.pattern-recommendations {
    padding: 0 20px;
}

.no-patterns {
    text-align: center;
    color: #666;
    padding: 40px 20px;
    font-style: italic;
}

/* 语言学专家推荐特殊样式 */
.expert-recommendation-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    padding: 20px;
    margin-bottom: 0;
    position: relative;
    overflow: hidden;
}

.expert-recommendation-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.expert-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    z-index: 1;
}

.expert-icon {
    font-size: 1.4em;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.expert-text {
    font-weight: 600;
    font-size: 1em;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.recommendation-stats {
    margin-top: 8px;
    opacity: 0.9;
    font-size: 0.9em;
    position: relative;
    z-index: 1;
}

.linguistic-recommendation-group {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    margin-bottom: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.linguistic-recommendation-group:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.linguistic-recommendation-group.expanded .expand-icon {
    transform: rotate(180deg);
}

.linguistic-recommendation-group.expanded .recommendation-content {
    max-height: 1000px;
    opacity: 1;
}

.linguistic-recommendation-group .category-header {
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    padding: 16px 20px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.2s ease;
}

.linguistic-recommendation-group .category-header:hover {
    background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.category-icon {
    font-size: 1.3em;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
}

.category-name {
    font-weight: 700;
    color: #2d3748;
    font-size: 1.05em;
}

.word-count {
    color: #718096;
    font-size: 0.9em;
    font-weight: 500;
}

.educational-value {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.educational-value.priority-very-high {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    box-shadow: 0 2px 4px rgba(238, 90, 36, 0.3);
}

.educational-value.priority-high {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    color: #2d3748;
    box-shadow: 0 2px 4px rgba(254, 202, 87, 0.3);
}

.educational-value.priority-medium {
    background: linear-gradient(135deg, #48dbfb, #0abde3);
    color: white;
    box-shadow: 0 2px 4px rgba(72, 219, 251, 0.3);
}

.educational-value.priority-low {
    background: linear-gradient(135deg, #a4b0be, #747d8c);
    color: white;
    box-shadow: 0 2px 4px rgba(164, 176, 190, 0.3);
}

.expand-icon {
    font-size: 0.9em;
    color: #718096;
    transition: transform 0.3s ease;
}

.recommendation-content {
    max-height: 0;
    opacity: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.category-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 2px solid #f5f6f8;
}

.category-icon {
    font-size: 20px;
}

.category-name {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    flex: 1;
}

.educational-value {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.linguistic-explanation {
    font-size: 16px;
    color: #34495e;
    line-height: 1.7;
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.linguistic-principle {
    background: linear-gradient(135deg, #e8f2ff 0%, #f0f8ff 100%);
    border: 1px solid #d4e7ff;
    border-radius: 10px;
    padding: 14px;
    margin-bottom: 20px;
    font-size: 14px;
    color: #2c5aa0;
}

.principle-label {
    font-weight: 600;
    color: #1e3a8a;
}

.similar-words-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 16px;
    margin-top: 20px;
}

.linguistic-similar-word {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.linguistic-similar-word:hover {
    background: #f8f9ff;
    border-color: #667eea;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
}

.linguistic-similar-word .word-text {
    margin-bottom: 12px;
}

.linguistic-similar-word .word-text strong {
    display: block;
    font-size: 18px;
    color: #667eea;
    margin-bottom: 6px;
    font-weight: 600;
}

.linguistic-similar-word .word-meaning {
    font-size: 14px;
    color: #6c757d;
    line-height: 1.4;
}

.linguistic-similar-word .word-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #868e96;
}

.proficiency-level {
    background: #e9ecef;
    color: #495057;
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: 600;
}

.status-indicator {
    padding: 2px 8px;
    border-radius: 10px;
    font-weight: 500;
}

.status-indicator.review {
    background: #fff3cd;
    color: #856404;
}

.status-indicator.attention {
    background: #f8d7da;
    color: #721c24;
}

.status-indicator.new {
    background: #d1ecf1;
    color: #0c5460;
}

/* 认知层次区域样式 */
.cognitive-level-section {
    margin-bottom: 20px;
}

.level-header {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border-left: 4px solid #667eea;
    padding: 15px 20px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #333;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.level-icon {
    font-size: 20px;
}

.level-name {
    font-size: 16px;
    color: #667eea;
}

.level-description {
    font-size: 13px;
    color: #888;
    font-weight: 400;
    margin-left: auto;
}

/* 维度区域样式 */
.dimension-section {
    margin-bottom: 15px;
    margin-left: 20px;
}

.dimension-header {
    background: #f8f9fa;
    border-left: 3px solid #ddd;
    padding: 10px 15px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #555;
    border-radius: 6px;
    font-size: 14px;
}

.dimension-icon {
    font-size: 16px;
}

.dimension-name {
    color: #444;
}

/* Pattern组样式 */
.pattern-group {
    border-bottom: 1px solid #f0f0f0;
    padding: 15px 20px;
    margin-left: 15px;
    transition: background 0.2s ease;
    border-radius: 6px;
    margin-bottom: 8px;
}

.pattern-group:hover {
    background: #fafbff;
}

.pattern-group.primary {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border-left: 3px solid #667eea;
    position: relative;
}

.pattern-group.primary::before {
    content: '⭐';
    position: absolute;
    top: 12px;
    right: 15px;
    font-size: 14px;
    opacity: 0.6;
}

/* 概念标签样式 */
.concept-tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    margin-left: 8px;
    opacity: 0.9;
}

.pattern-info {
    margin-bottom: 15px;
}

.pattern-type {
    font-size: 13px;
    font-weight: 600;
    color: #667eea;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.pattern-name {
    flex: 1;
}

.pattern-type-icon {
    font-size: 16px;
    opacity: 0.8;
}

.pattern-reason {
    font-size: 14px;
    color: #444;
    line-height: 1.6;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 15px;
    border-left: 3px solid #667eea;
    font-weight: 500;
}

.pattern-reason .emoji {
    font-size: 16px;
    margin-right: 4px;
}

.pattern-description {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
    font-style: italic;
    margin-top: 8px;
}

/* 相似单词网格 */
.similar-words {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.similar-word {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #333;
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.similar-word:hover {
    background: #f8f9ff;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.similar-word.highlighted {
    background: #667eea;
    color: white;
    border-color: #667eea;
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.3);
}

.similar-word strong {
    display: block;
    font-size: 16px;
    margin-bottom: 6px;
    color: #667eea;
}

.similar-word.highlighted strong {
    color: white;
}

.word-meaning {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
    line-height: 1.3;
}

.similar-word.highlighted .word-meaning {
    color: rgba(255,255,255,0.9);
}

.word-status {
    font-size: 11px;
    color: #888;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.similar-word.highlighted .word-status {
    color: rgba(255,255,255,0.8);
}

.proficiency-badge {
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 10px;
}

.similar-word.highlighted .proficiency-badge {
    background: rgba(255,255,255,0.2);
    color: white;
}

/* 反馈区域 */
.pattern-feedback {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 20px;
    margin-top: auto;
}

.feedback-label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    font-weight: 500;
}

.feedback-buttons {
    display: flex;
    gap: 10px;
}

.feedback-btn {
    flex: 1;
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s ease;
}

.feedback-btn:hover {
    background: #f8f9fa;
    border-color: #bbb;
}

.feedback-btn.helpful:hover {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.feedback-btn.not-helpful:hover {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

/* 防止主体内容滚动 */
body.pattern-panel-open {
    overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .pattern-slide-panel {
        width: 35%;
        right: -35%;
    }
}

@media (max-width: 768px) {
    .pattern-floating-icon {
        bottom: 80px;
        right: 15px;
        width: 50px;
        height: 50px;
    }
    
    .pattern-floating-icon .icon {
        font-size: 20px;
    }
    
    .pattern-floating-icon .badge {
        width: 18px;
        height: 18px;
        font-size: 11px;
        top: -6px;
        right: -6px;
    }

    .pattern-slide-panel {
        width: 90%;
        right: -90%;
        font-size: 15px;
    }
    
    .panel-header {
        padding: 20px 15px;
    }
    
    .panel-header h4 {
        font-size: 18px;
    }
    
    .expert-badge {
        font-size: 12px;
        padding: 3px 10px;
    }
    
    .panel-content {
        padding: 15px 0;
    }
    
    .pattern-recommendations {
        padding: 0 15px;
    }
    
    .expert-recommendation-header {
        padding: 16px;
        margin-bottom: 20px;
    }
    
    .expert-indicator {
        font-size: 15px;
        gap: 10px;
    }
    
    .expert-icon {
        font-size: 20px;
    }
    
    .linguistic-recommendation-group {
        padding: 18px;
        margin-bottom: 16px;
    }
    
    .category-name {
        font-size: 16px;
    }
    
    .linguistic-explanation {
        font-size: 15px;
        padding: 14px;
    }
    
    .linguistic-principle {
        font-size: 13px;
        padding: 12px;
    }
    
    .similar-words-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 12px;
    }
    
    .linguistic-similar-word {
        padding: 12px;
    }
    
    .linguistic-similar-word .word-text strong {
        font-size: 16px;
    }
    
    .level-header {
        padding: 12px 15px;
        gap: 8px;
    }
    
    .level-name {
        font-size: 14px;
    }
    
    .level-description {
        font-size: 12px;
    }
    
    .dimension-section {
        margin-left: 10px;
    }
    
    .dimension-header {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    .pattern-group {
        padding: 12px 15px;
        margin-left: 10px;
    }
    
    .concept-tag {
        font-size: 10px;
        padding: 1px 6px;
    }
    
    .similar-words {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 6px;
    }
    
    .similar-word {
        padding: 6px 8px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .pattern-slide-panel {
        width: 100%;
        right: -100%;
        font-size: 14px;
    }
    
    .panel-header {
        padding: 16px 12px;
    }
    
    .panel-header h4 {
        font-size: 16px;
    }
    
    .expert-badge {
        font-size: 11px;
        padding: 2px 8px;
    }
    
    .panel-content {
        padding: 12px 0;
    }
    
    .pattern-recommendations {
        padding: 0 12px;
    }
    
    .expert-recommendation-header {
        padding: 12px;
        margin-bottom: 16px;
    }
    
    .expert-indicator {
        font-size: 14px;
        gap: 8px;
        flex-direction: column;
        align-items: flex-start;
    }
    
    .expert-icon {
        font-size: 18px;
    }
    
    .linguistic-recommendation-group {
        padding: 14px;
        margin-bottom: 14px;
    }
    
    .category-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .category-name {
        font-size: 15px;
    }
    
    .linguistic-explanation {
        font-size: 14px;
        padding: 12px;
    }
    
    .linguistic-principle {
        font-size: 12px;
        padding: 10px;
    }
    
    .similar-words-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }
    
    .linguistic-similar-word {
        padding: 10px;
    }
    
    .linguistic-similar-word .word-text strong {
        font-size: 15px;
    }
    
    .linguistic-similar-word .word-meaning {
        font-size: 13px;
    }
    
    .level-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        padding: 10px 12px;
    }
    
    .level-description {
        margin-left: 0;
    }
    
    .dimension-section {
        margin-left: 5px;
    }
    
    .dimension-header {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .pattern-group {
        padding: 10px 12px;
        margin-left: 5px;
    }
    
    .similar-words {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    }
}

/* 面板打开时的遮罩效果（可选） */
.pattern-panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.3);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.pattern-panel-overlay.visible {
    opacity: 1;
    visibility: visible;
}

/* 增强版语言学推荐样式 */
.word-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    margin-top: 8px;
}

.action-btn {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 6px 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9em;
}

.action-btn:hover {
    background: #edf2f7;
    transform: scale(1.05);
}

.review-btn:hover {
    background: #e6fffa;
    border-color: #38b2ac;
}

.compare-btn:hover {
    background: #fef5e7;
    border-color: #ed8936;
}

.group-actions {
    padding: 16px 20px;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 12px;
    justify-content: center;
}

.group-action-btn {
    background: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 25px;
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 0.9em;
}

.group-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.group-action-btn:not(.secondary):hover {
    background: #48bb78;
    border-color: #48bb78;
    color: white;
}

.group-action-btn.secondary:hover {
    background: #fed7d7;
    border-color: #fc8181;
    color: #c53030;
}

.feedback-thanks {
    background: linear-gradient(135deg, #c6f6d5, #9ae6b4);
    color: #22543d;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    text-align: center;
    animation: fadeInScale 0.5s ease;
}

@keyframes fadeInScale {
    0% { opacity: 0; transform: scale(0.8); }
    100% { opacity: 1; transform: scale(1); }
}

.overall-feedback {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    padding: 20px;
    border-top: 1px solid #e2e8f0;
    text-align: center;
}

.feedback-title {
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 16px;
    font-size: 1.05em;
}

.feedback-options {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

.feedback-option {
    background: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 25px;
    padding: 10px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 0.85em;
    min-width: 120px;
}

.feedback-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.feedback-option.excellent:hover {
    background: linear-gradient(135deg, #ffd89b, #19547b);
    border-color: #19547b;
    color: white;
}

.feedback-option.good:hover {
    background: linear-gradient(135deg, #c6f6d5, #48bb78);
    border-color: #48bb78;
    color: #22543d;
}

.feedback-option.neutral:hover {
    background: linear-gradient(135deg, #feebc8, #ed8936);
    border-color: #ed8936;
    color: white;
}

.feedback-option.poor:hover {
    background: linear-gradient(135deg, #fed7d7, #e53e3e);
    border-color: #e53e3e;
    color: white;
}

.overall-feedback-thanks {
    background: linear-gradient(135deg, #e6fffa, #b2f5ea);
    border: 2px solid #38b2ac;
    border-radius: 16px;
    padding: 20px;
    margin-top: 16px;
    animation: thankYouAnimation 0.8s ease;
}

@keyframes thankYouAnimation {
    0% { opacity: 0; transform: scale(0.8) translateY(20px); }
    50% { transform: scale(1.05) translateY(-5px); }
    100% { opacity: 1; transform: scale(1) translateY(0); }
}

.thanks-icon {
    font-size: 2em;
    margin-bottom: 8px;
}

.thanks-text {
    color: #234e52;
    font-weight: 600;
    line-height: 1.5;
}