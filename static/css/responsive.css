/* ========== 高级响应式设计系统 ========== */

/* 响应式网格系统 */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(-1 * var(--spacing-sm));
}

.col {
    flex: 1;
    padding: 0 var(--spacing-sm);
    min-width: 0;
}

.col-auto {
    flex: 0 0 auto;
    width: auto;
}

/* 响应式列宽 */
@media (min-width: 576px) {
    .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
    .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
    .col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
}

@media (min-width: 768px) {
    .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

@media (min-width: 992px) {
    .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
    .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
    .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
}

/* 响应式显示工具类 */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }

@media (max-width: 575px) {
    .d-xs-none { display: none !important; }
    .d-xs-block { display: block !important; }
    .d-xs-flex { display: flex !important; }
}

@media (min-width: 576px) and (max-width: 767px) {
    .d-sm-none { display: none !important; }
    .d-sm-block { display: block !important; }
    .d-sm-flex { display: flex !important; }
}

@media (min-width: 768px) and (max-width: 991px) {
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }
}

@media (min-width: 992px) {
    .d-lg-none { display: none !important; }
    .d-lg-block { display: block !important; }
    .d-lg-flex { display: flex !important; }
}

/* 响应式间距工具类 */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

/* 响应式文本对齐 */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

@media (max-width: 767px) {
    .text-sm-left { text-align: left !important; }
    .text-sm-center { text-align: center !important; }
    .text-sm-right { text-align: right !important; }
}

/* 响应式Flexbox工具类 */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-center { align-items: center !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-stretch { align-items: stretch !important; }

@media (max-width: 767px) {
    .flex-sm-column { flex-direction: column !important; }
    .flex-sm-row { flex-direction: row !important; }
    .justify-content-sm-center { justify-content: center !important; }
    .align-items-sm-center { align-items: center !important; }
}

/* 触摸优化 */
.touch-target {
    min-height: 44px;
    min-width: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 767px) {
    .touch-target {
        min-height: 48px;
        min-width: 48px;
    }
}

/* 响应式字体大小 */
.fs-1 { font-size: clamp(1.75rem, 5vw, 3rem) !important; }
.fs-2 { font-size: clamp(1.5rem, 4vw, 2.5rem) !important; }
.fs-3 { font-size: clamp(1.25rem, 3vw, 2rem) !important; }
.fs-4 { font-size: clamp(1.125rem, 2.5vw, 1.5rem) !important; }
.fs-5 { font-size: clamp(1rem, 2vw, 1.25rem) !important; }
.fs-6 { font-size: clamp(0.875rem, 1.5vw, 1rem) !important; }

/* 响应式容器变体 */
.container-fluid {
    width: 100%;
    padding-right: var(--spacing-md);
    padding-left: var(--spacing-md);
    margin-right: auto;
    margin-left: auto;
}

.container-sm {
    max-width: 540px;
}

.container-md {
    max-width: 720px;
}

.container-lg {
    max-width: 960px;
}

.container-xl {
    max-width: 1140px;
}

/* 响应式图片 */
.img-responsive {
    max-width: 100%;
    height: auto;
    display: block;
}

.img-fluid {
    max-width: 100%;
    height: auto;
}

/* 响应式视频 */
.video-responsive {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 */
    height: 0;
    overflow: hidden;
}

.video-responsive iframe,
.video-responsive object,
.video-responsive embed {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid currentColor;
    }
    
    input, select, textarea {
        border-width: 2px;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #4ade80;
        --primary-hover: #22c55e;
        --secondary-color: #60a5fa;
        --light-color: #1f2937;
        --dark-color: #f9fafb;
    }
    
    body {
        background-color: #111827;
        color: #f9fafb;
    }
    
    .container {
        background: rgba(31, 41, 55, 0.95);
    }
    
    input, select, textarea {
        background-color: #374151;
        color: #f9fafb;
        border-color: #4b5563;
    }
}
