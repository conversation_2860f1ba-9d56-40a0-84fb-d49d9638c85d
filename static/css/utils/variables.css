/**
 * CSS变量定义
 * 作者: AI-Alpha 前端架构师
 * 创建时间: 2025-07-10 17:30
 * 目的: 统一管理颜色、间距、字体等设计令牌
 */

:root {
    /* ===== 颜色系统 ===== */
    
    /* 主题色 */
    --color-primary: #007bff;
    --color-primary-light: #66b3ff;
    --color-primary-dark: #0056b3;
    --color-primary-alpha: rgba(0, 123, 255, 0.1);
    
    /* 状态色 */
    --color-success: #28a745;
    --color-success-light: #34ce57;
    --color-success-dark: #1e7e34;
    --color-success-alpha: rgba(40, 167, 69, 0.1);
    
    --color-error: #dc3545;
    --color-error-light: #e3596b;
    --color-error-dark: #bd2130;
    --color-error-alpha: rgba(220, 53, 69, 0.1);
    
    --color-warning: #ffc107;
    --color-warning-light: #ffd54f;
    --color-warning-dark: #ff8f00;
    --color-warning-alpha: rgba(255, 193, 7, 0.1);
    
    --color-info: #17a2b8;
    --color-info-light: #3fbdce;
    --color-info-dark: #117a8b;
    --color-info-alpha: rgba(23, 162, 184, 0.1);
    
    /* 中性色 */
    --color-white: #ffffff;
    --color-gray-100: #f8f9fa;
    --color-gray-200: #e9ecef;
    --color-gray-300: #dee2e6;
    --color-gray-400: #ced4da;
    --color-gray-500: #adb5bd;
    --color-gray-600: #6c757d;
    --color-gray-700: #495057;
    --color-gray-800: #343a40;
    --color-gray-900: #212529;
    --color-black: #000000;
    
    /* 背景色 */
    --color-bg-primary: var(--color-white);
    --color-bg-secondary: var(--color-gray-100);
    --color-bg-tertiary: var(--color-gray-200);
    --color-bg-overlay: rgba(255, 255, 255, 0.95);
    --color-bg-modal: rgba(0, 0, 0, 0.5);
    
    /* 文本色 */
    --color-text-primary: var(--color-gray-900);
    --color-text-secondary: var(--color-gray-700);
    --color-text-tertiary: var(--color-gray-600);
    --color-text-muted: var(--color-gray-500);
    --color-text-inverse: var(--color-white);
    
    /* 边框色 */
    --color-border-primary: var(--color-gray-300);
    --color-border-secondary: var(--color-gray-200);
    --color-border-focus: var(--color-primary);
    --color-border-error: var(--color-error);
    
    /* ===== 间距系统 ===== */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    --spacing-xxxl: 64px;
    
    /* ===== 字体系统 ===== */
    
    /* 字体族 */
    --font-family-primary: 'Helvetica Neue', 'Arial', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    --font-family-monospace: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    
    /* 字体大小 */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-xxl: 24px;
    --font-size-xxxl: 32px;
    
    /* 行高 */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-loose: 1.8;
    
    /* 字重 */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* ===== 圆角系统 ===== */
    --border-radius-xs: 2px;
    --border-radius-sm: 4px;
    --border-radius-md: 6px;
    --border-radius-lg: 8px;
    --border-radius-xl: 12px;
    --border-radius-xxl: 16px;
    --border-radius-round: 50%;
    --border-radius-pill: 9999px;
    
    /* ===== 阴影系统 ===== */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
    --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.06);
    
    /* ===== Z-index层级 ===== */
    --z-index-dropdown: 1000;
    --z-index-sticky: 1020;
    --z-index-fixed: 1030;
    --z-index-modal-backdrop: 1040;
    --z-index-modal: 1050;
    --z-index-popover: 1060;
    --z-index-tooltip: 1070;
    
    /* ===== 过渡动画 ===== */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* 缓动函数 */
    --ease-in-quad: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    --ease-in-cubic: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    --ease-out-quad: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --ease-out-cubic: cubic-bezier(0.215, 0.61, 0.355, 1);
    --ease-in-out-quad: cubic-bezier(0.455, 0.03, 0.515, 0.955);
    --ease-in-out-cubic: cubic-bezier(0.645, 0.045, 0.355, 1);
    
    /* ===== 布局系统 ===== */
    
    /* 容器宽度 */
    --container-sm: 576px;
    --container-md: 768px;
    --container-lg: 992px;
    --container-xl: 1200px;
    --container-xxl: 1400px;
    
    /* 网格间距 */
    --grid-gap-xs: var(--spacing-xs);
    --grid-gap-sm: var(--spacing-sm);
    --grid-gap-md: var(--spacing-md);
    --grid-gap-lg: var(--spacing-lg);
    --grid-gap-xl: var(--spacing-xl);
    
    /* ===== 组件特定变量 ===== */
    
    /* 表单控件 */
    --form-control-height: 44px;
    --form-control-padding-x: var(--spacing-md);
    --form-control-padding-y: var(--spacing-sm);
    --form-control-border-width: 1px;
    --form-control-border-radius: var(--border-radius-md);
    --form-control-focus-width: 2px;
    
    /* 按钮 */
    --btn-padding-x: var(--spacing-lg);
    --btn-padding-y: var(--spacing-sm);
    --btn-border-width: 1px;
    --btn-border-radius: var(--border-radius-md);
    --btn-font-weight: var(--font-weight-medium);
    
    /* 卡片 */
    --card-padding: var(--spacing-lg);
    --card-border-width: 1px;
    --card-border-radius: var(--border-radius-lg);
    --card-shadow: var(--shadow-sm);
    
    /* 模态框 */
    --modal-backdrop-bg: var(--color-bg-modal);
    --modal-content-bg: var(--color-bg-primary);
    --modal-content-padding: var(--spacing-xl);
    --modal-content-border-radius: var(--border-radius-lg);
    
    /* ===== 学习应用特定变量 ===== */
    
    /* 单词卡片 */
    --word-card-bg: var(--color-bg-primary);
    --word-card-border: var(--color-border-primary);
    --word-card-padding: var(--spacing-xl);
    --word-card-border-radius: var(--border-radius-xl);
    --word-card-shadow: var(--shadow-md);
    
    /* 星级 */
    --star-color-active: #ffd700;
    --star-color-inactive: var(--color-gray-300);
    --star-size: 20px;
    
    /* 进度条 */
    --progress-bar-height: 8px;
    --progress-bar-bg: var(--color-gray-200);
    --progress-bar-border-radius: var(--border-radius-pill);
    
    /* 熟练度颜色 */
    --proficiency-very-low: var(--color-error);
    --proficiency-low: var(--color-warning);
    --proficiency-medium: var(--color-info);
    --proficiency-high: var(--color-success);
    
    /* 反馈颜色 */
    --feedback-correct-bg: var(--color-success-alpha);
    --feedback-correct-border: var(--color-success);
    --feedback-incorrect-bg: var(--color-error-alpha);
    --feedback-incorrect-border: var(--color-error);
    
    /* 输入状态 */
    --input-valid-border: var(--color-success);
    --input-invalid-border: var(--color-error);
    --input-focus-shadow: 0 0 0 var(--form-control-focus-width) var(--color-primary-alpha);
    
    /* 加载状态 */
    --loading-overlay-bg: rgba(255, 255, 255, 0.9);
    --loading-spinner-color: var(--color-primary);
    
    /* ===== 断点系统 ===== */
    --breakpoint-xs: 0;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-xxl: 1400px;
}