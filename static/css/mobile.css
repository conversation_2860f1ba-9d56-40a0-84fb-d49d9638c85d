/* ========== 移动端专用优化 ========== */

/* 移动端导航优化 */
@media (max-width: 768px) {
    .back-to-dashboard {
        position: fixed;
        top: var(--spacing-sm);
        left: var(--spacing-sm);
        z-index: 1000;
        background: var(--primary-color);
        color: white;
        padding: var(--spacing-sm) var(--spacing-md);
        border: none;
        border-radius: var(--border-radius-md);
        cursor: pointer;
        text-decoration: none;
        font-size: var(--font-size-sm);
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
    }

    .back-to-dashboard:hover {
        background: var(--primary-hover);
        transform: translateY(-1px);
    }
}

/* 移动端容器优化 */
@media (max-width: 768px) {
    body {
        padding-top: 60px; /* 为固定导航留出空间 */
    }

    .dashboard-container {
        margin: 0;
        border-radius: 0;
        min-height: calc(100vh - 60px);
        padding: var(--spacing-md);
    }
}

/* 移动端表单优化 */
@media (max-width: 768px) {
    input[type="text"],
    input[type="email"],
    input[type="password"],
    select,
    textarea {
        font-size: 16px; /* 防止iOS缩放 */
        padding: var(--spacing-md);
        border-radius: var(--border-radius-lg);
    }

    /* 拼写输入框移动端优化 */
    #spelling-input {
        font-size: 1.25rem;
        padding: var(--spacing-lg);
        text-align: center;
        letter-spacing: 2px;
        border-width: 3px;
    }

    #spelling-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 4px rgba(40, 167, 69, 0.2);
    }
}

/* 移动端按钮优化 */
@media (max-width: 768px) {
    .btn,
    button {
        min-height: 48px;
        font-size: 1rem;
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--border-radius-lg);
        touch-action: manipulation; /* 优化触摸响应 */
    }

    /* 音频播放按钮 */
    #play-audio,
    #play-audio-spelling,
    #pronounce-button {
        min-width: 48px;
        min-height: 48px;
        font-size: 1.25rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* 移动端字母反馈优化 */
@media (max-width: 768px) {
    #letter-feedback {
        font-size: 1.5rem;
        letter-spacing: 1px;
        padding: var(--spacing-md);
        min-height: 60px;
        gap: var(--spacing-xs);
    }

    .letter-correct,
    .letter-incorrect,
    .letter-pending {
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--border-radius-md);
        font-weight: 600;
        min-width: 32px;
        text-align: center;
    }
}

/* 移动端进度条优化 */
@media (max-width: 768px) {
    #progress-bar {
        height: 12px;
        border-radius: var(--border-radius-md);
        margin: var(--spacing-md) 0;
    }

    #progress-fill {
        border-radius: var(--border-radius-md);
    }
}

/* 移动端模态框优化 */
@media (max-width: 768px) {
    #import-modal {
        padding: var(--spacing-sm);
    }

    #import-modal > div {
        width: 95% !important;
        max-width: none !important;
        margin: 10% auto !important;
        border-radius: var(--border-radius-lg) !important;
        max-height: 80vh;
        overflow-y: auto;
    }

    .import-tab {
        font-size: var(--font-size-sm) !important;
        padding: var(--spacing-md) var(--spacing-sm) !important;
    }
}

/* 移动端表格优化 */
@media (max-width: 768px) {
    .low-correct-rate-container {
        margin: var(--spacing-md) 0;
        padding: var(--spacing-md);
        border-radius: var(--border-radius-lg);
    }

    .low-correct-rate-container table {
        font-size: var(--font-size-sm);
    }

    .low-correct-rate-container th,
    .low-correct-rate-container td {
        padding: var(--spacing-sm);
        word-break: break-word;
    }
}

/* 移动端复选框优化 */
@media (max-width: 768px) {
    .checkbox-group {
        flex-direction: row;
        align-items: center;
        gap: var(--spacing-md);
        justify-content: flex-start;
    }

    .checkbox-group label {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        margin: 0;
        padding: 0;
        border-radius: 0;
        background: none;
        width: auto;
        cursor: pointer;
        position: relative;
    }

    .checkbox-group input[type="checkbox"] {
        position: absolute !important;
        opacity: 0 !important;
        width: 0 !important;
        height: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .checkbox-group label::before {
        content: '' !important;
        display: inline-block !important;
        width: 16px !important;
        height: 16px !important;
        background: white !important;
        border: 2px solid #666 !important;
        border-radius: 2px !important;
        margin-right: 6px !important;
        vertical-align: middle !important;
        transition: all 0.2s ease !important;
    }

    .checkbox-group label:has(input[type="checkbox"]:checked)::before {
        background: #007AFF !important;
        border-color: #007AFF !important;
    }

    .checkbox-group label:has(input[type="checkbox"]:checked)::after {
        content: '✓' !important;
        position: absolute !important;
        left: 3px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        color: white !important;
        font-size: 12px !important;
        font-weight: bold !important;
        pointer-events: none !important;
    }

    /* 自定义复选框样式 */
    .custom-checkbox {
        display: flex !important;
        align-items: center !important;
        gap: 6px !important;
        cursor: pointer !important;
        -webkit-user-select: none !important;
        user-select: none !important;
    }

    .custom-checkbox-box {
        width: 16px !important;
        height: 16px !important;
        border: 2px solid #666 !important;
        border-radius: 2px !important;
        background: white !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: all 0.2s ease !important;
        flex-shrink: 0 !important;
    }

    .custom-checkbox-box.checked {
        background: #007AFF !important;
        border-color: #007AFF !important;
    }

    .custom-checkbox-box.checked::after {
        content: '✓' !important;
        color: white !important;
        font-size: 12px !important;
        font-weight: bold !important;
    }

    .custom-checkbox-text {
        font-size: 12px !important;
        color: #48484A !important;
        font-weight: 500 !important;
    }
}

/* 移动端日计划优化 */
@media (max-width: 768px) {
    #daily-plan {
        background: rgba(255, 255, 255, 0.1);
        padding: var(--spacing-md);
        border-radius: var(--border-radius-lg);
        margin: var(--spacing-md) 0;
    }

    #daily-plan h2 {
        font-size: 1.25rem;
        margin-bottom: var(--spacing-sm);
    }

    #daily-plan p {
        font-size: 1rem;
        margin: var(--spacing-xs) 0;
    }
}

/* 移动端单词容器优化 */
@media (max-width: 768px) {
    #word-container {
        margin-bottom: var(--spacing-lg);
        padding: var(--spacing-md);
        background: rgba(0, 0, 0, 0.8);
        border-radius: var(--border-radius-lg);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    #word-container img,
    #word-image {
        width: 120px;
        height: auto;
        border-radius: var(--border-radius-md);
        display: block !important;
        max-width: 100%;
        margin: 0 auto 10px auto;
        visibility: visible !important;
        opacity: 1 !important;
    }

    #word {
        font-size: 2rem;
        font-weight: 600;
        color: #ffffff;
    }

    /* 确保单词和发音按钮区域的文字清晰可见 */
    #word-and-pronounce {
        color: #ffffff;
    }

    #word-and-pronounce span {
        color: #ffffff;
    }

    #pronounce-button {
        color: #ffffff;
        background: rgba(255, 255, 255, 0.1);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius-md);
        border: 1px solid rgba(255, 255, 255, 0.2);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    #pronounce-button:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
    }
}

/* 移动端提示区域优化 */
@media (max-width: 768px) {
    #hint {
        background: rgba(0, 0, 0, 0.95) !important;
        color: #ffffff !important;
        padding: var(--spacing-md);
        border-radius: var(--border-radius-lg);
        margin: var(--spacing-md) 0;
        font-size: 1rem;
        line-height: 1.5;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }
    
    /* 确保提示区域内所有文字都是白色 */
    #hint div, #hint span, #hint small, #hint p {
        color: #ffffff !important;
    }

    #memory-help {
        background: rgba(255, 255, 255, 0.95);
        color: #333333;
        padding: var(--spacing-md);
        border-radius: var(--border-radius-lg);
        margin: var(--spacing-md) 0;
        font-size: var(--font-size-sm);
        line-height: 1.6;
        border: 2px solid rgba(0, 0, 0, 0.1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* 确保记忆帮助内容的所有文字都清晰可见 */
    #memory-help h1,
    #memory-help h2,
    #memory-help h3,
    #memory-help h4,
    #memory-help h5,
    #memory-help h6 {
        color: #1a1a1a !important;
        margin-top: var(--spacing-sm);
        margin-bottom: var(--spacing-xs);
        font-weight: 700;
    }

    #memory-help p,
    #memory-help li,
    #memory-help span,
    #memory-help div {
        color: #333333 !important;
    }

    #memory-help strong,
    #memory-help b {
        color: #d32f2f !important;
        font-weight: 700;
    }

    #memory-help em,
    #memory-help i {
        color: #1976d2 !important;
        font-style: italic;
        font-weight: 500;
    }

    #memory-help code {
        background: rgba(0, 0, 0, 0.08);
        color: #c62828 !important;
        padding: 3px 6px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-weight: 600;
        border: 1px solid rgba(0, 0, 0, 0.15);
    }

    #memory-help pre {
        background: rgba(0, 0, 0, 0.05);
        color: #333333 !important;
        padding: var(--spacing-sm);
        border-radius: var(--border-radius-md);
        overflow-x: auto;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    #memory-help ul,
    #memory-help ol {
        padding-left: var(--spacing-lg);
    }

    #memory-help blockquote {
        border-left: 4px solid #1976d2;
        padding-left: var(--spacing-sm);
        margin-left: 0;
        color: #424242 !important;
        font-style: italic;
        background: rgba(25, 118, 210, 0.05);
        padding: var(--spacing-sm);
        border-radius: var(--border-radius-md);
    }
}

/* 移动端反馈区域优化 */
@media (max-width: 768px) {
    #feedback {
        padding: var(--spacing-md);
        border-radius: var(--border-radius-lg);
        margin: var(--spacing-md) 0;
        font-size: 1.125rem;
        font-weight: 600;
        text-align: center;
    }

    #feedback.correct {
        background: rgba(76, 175, 80, 0.1);
        color: var(--success-color);
    }

    #feedback.incorrect {
        background: rgba(244, 67, 54, 0.1);
        color: var(--danger-color);
    }
}

/* 移动端横屏优化 */
@media (max-width: 768px) and (orientation: landscape) {
    body {
        padding-top: 50px;
    }

    .dashboard-container {
        flex-direction: row;
        min-height: calc(100vh - 50px);
        padding: var(--spacing-sm);
    }

    .left-column {
        order: 1;
        flex: 1;
        max-width: 300px;
    }

    .right-column {
        order: 2;
        flex: 2;
    }

    #word {
        font-size: 1.5rem;
    }

    #letter-feedback {
        font-size: 1.25rem;
    }
}

/* 移动端无障碍优化 */
@media (max-width: 768px) {
    /* 增加焦点可见性 */
    button:focus,
    input:focus,
    select:focus,
    textarea:focus,
    a:focus {
        outline: 3px solid var(--primary-color);
        outline-offset: 2px;
    }

    /* 增加点击区域 */
    .touch-target {
        min-height: 48px;
        min-width: 48px;
        padding: var(--spacing-sm);
    }
}
