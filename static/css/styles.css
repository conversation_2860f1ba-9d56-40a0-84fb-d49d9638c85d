/* ========== 基础样式和变量 ========== */
:root {
    --primary-color: #28a745;
    --primary-hover: #218838;
    --secondary-color: #007bff;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-xxl: 1.5rem;

    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;

    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}

* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-image: url('../images/background.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: #333;
    margin: 0;
    padding: 0;
    line-height: 1.6;
    font-size: var(--font-size-base);
}

/* ========== 布局容器 ========== */
.container {
    max-width: 1200px;
    margin: var(--spacing-lg) auto;
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    text-align: center;
    width: 95%;
    min-height: calc(100vh - 3rem);
}

/* 响应式容器 */
@media (max-width: 1200px) {
    .container {
        max-width: 95%;
        margin: var(--spacing-md) auto;
    }
}

@media (max-width: 768px) {
    .container {
        margin: var(--spacing-sm) auto;
        padding: var(--spacing-md);
        width: 98%;
        border-radius: var(--border-radius-lg);
    }
}

@media (max-width: 480px) {
    .container {
        margin: var(--spacing-xs) auto;
        padding: var(--spacing-sm);
        width: 100%;
        border-radius: 0;
        min-height: 100vh;
    }
}

/* ========== 文字排版 ========== */
h1 {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    margin-bottom: var(--spacing-lg);
    font-weight: 700;
    color: var(--dark-color);
    line-height: 1.2;
}

h2 {
    font-size: clamp(1.25rem, 3vw, 2rem);
    margin-bottom: var(--spacing-md);
    font-weight: 600;
    color: var(--dark-color);
}

h3 {
    font-size: clamp(1.125rem, 2.5vw, 1.5rem);
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--dark-color);
}

p {
    font-size: clamp(0.875rem, 2vw, 1.125rem);
    margin-bottom: var(--spacing-sm);
    line-height: 1.6;
    color: #555;
}

/* 响应式文字大小 */
@media (max-width: 768px) {
    h1 {
        margin-bottom: var(--spacing-md);
    }

    h2 {
        margin-bottom: var(--spacing-sm);
    }

    p {
        margin-bottom: var(--spacing-xs);
    }
}

/* ========== 按钮样式 ========== */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: clamp(0.875rem, 2vw, 1.125rem);
    font-weight: 500;
    color: #fff;
    background-color: var(--primary-color);
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-sm);
    min-height: 44px; /* 触摸友好的最小高度 */
    min-width: 44px;
    line-height: 1.4;
    text-align: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.btn:hover {
    background-color: var(--primary-hover);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25);
}

/* 按钮变体 */
.btn-secondary {
    background-color: var(--secondary-color);
}

.btn-secondary:hover {
    background-color: #0056b3;
}

.btn-danger {
    background-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

/* 响应式按钮 */
@media (max-width: 768px) {
    .btn {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: 1rem;
        min-height: 48px; /* 移动端更大的触摸目标 */
    }
}

@media (max-width: 480px) {
    .btn {
        width: 100%;
        margin-bottom: var(--spacing-sm);
        padding: var(--spacing-md);
        font-size: 1.125rem;
    }
}

/* ========== 链接样式 ========== */
a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs);
    margin: calc(-1 * var(--spacing-xs));
}

a:hover {
    text-decoration: underline;
    background-color: rgba(0, 123, 255, 0.1);
}

a:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* ========== 表单元素 ========== */
input[type="text"],
input[type="email"],
input[type="password"],
select,
textarea {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    margin: var(--spacing-xs) 0;
    box-sizing: border-box;
    border: 2px solid #ddd;
    border-radius: var(--border-radius-md);
    font-size: clamp(0.875rem, 2vw, 1rem);
    font-family: inherit;
    line-height: 1.5;
    transition: all 0.3s ease;
    background-color: white;
    min-height: 44px; /* 触摸友好 */
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

input[type="text"]:invalid,
input[type="email"]:invalid,
input[type="password"]:invalid {
    border-color: var(--danger-color);
}

/* 响应式表单 */
@media (max-width: 768px) {
    input[type="text"],
    input[type="email"],
    input[type="password"],
    select,
    textarea {
        font-size: 1rem;
        min-height: 48px;
        padding: var(--spacing-md);
    }
}

/* 拼写模式样式 */
#word-info {
    margin: 10px 0;
    font-size: 14px;
    color: #666;
}

#proficiency-stars {
    font-size: 18px;
    margin-right: 10px;
}

#learning-mode {
    background: #e3f2fd;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    color: #1976d2;
}

#spelling-container {
    margin: 20px 0;
}

#spelling-input-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
}

#spelling-input {
    font-size: 24px;
    padding: 12px 16px;
    border: 2px solid #ddd;
    border-radius: 8px;
    text-align: center;
    min-width: 200px;
    letter-spacing: 2px;
    font-family: 'Courier New', monospace;
}

#spelling-input:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.3);
}

#play-audio {
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#play-audio:hover {
    background: #1976D2;
    transform: scale(1.1);
}

#spelling-feedback {
    margin: 15px 0;
}

#letter-feedback {
    font-size: 28px;
    font-family: 'Courier New', monospace;
    letter-spacing: 3px;
    margin-bottom: 10px;
    min-height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.letter-correct {
    color: #4CAF50;
    background: #E8F5E8;
    padding: 4px 8px;
    border-radius: 4px;
    margin: 2px;
    font-weight: bold;
    animation: correctLetter 0.3s ease-in-out;
}

.letter-incorrect {
    color: #F44336;
    background: #FFEBEE;
    padding: 4px 8px;
    border-radius: 4px;
    margin: 2px;
    font-weight: bold;
    animation: incorrectLetter 0.3s ease-in-out;
}

.letter-pending {
    color: #999;
    padding: 4px 8px;
    margin: 2px;
    font-weight: bold;
}

@keyframes correctLetter {
    0% { transform: scale(0.8); background: #4CAF50; }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

@keyframes incorrectLetter {
    0% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
    100% { transform: translateX(0); }
}

#progress-bar {
    width: 100%;
    height: 8px;
    background: #E0E0E0;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

#progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

#spelling-hints {
    margin: 15px 0;
    font-size: 14px;
}

#syllable-hint, #length-hint {
    background: #FFF3E0;
    padding: 8px 12px;
    border-radius: 6px;
    margin: 5px 0;
    border-left: 4px solid #FF9800;
}

#action-buttons {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

#action-buttons button {
    background: #6C757D;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

#action-buttons button:hover {
    background: #5A6268;
    transform: translateY(-2px);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* ========== 响应式设计 ========== */

/* 大屏幕 (1200px+) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }

    #spelling-input {
        font-size: 1.5rem;
        padding: var(--spacing-lg);
    }

    #letter-feedback {
        font-size: 2rem;
    }
}

/* 平板横屏 (992px - 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .container {
        max-width: 960px;
    }
}

/* 平板竖屏 (768px - 991px) */
@media (max-width: 991px) and (min-width: 768px) {
    .container {
        max-width: 720px;
    }

    #spelling-input {
        font-size: 1.25rem;
    }
}

/* 大手机 (576px - 767px) */
@media (max-width: 767px) and (min-width: 576px) {
    .container {
        max-width: 540px;
        padding: var(--spacing-md);
    }

    #spelling-input {
        min-width: 200px;
        font-size: 1.125rem;
    }

    #letter-feedback {
        font-size: 1.5rem;
        letter-spacing: 1px;
    }
}

/* 小手机 (575px及以下) */
@media (max-width: 575px) {
    #spelling-input {
        min-width: 150px;
        font-size: 1rem;
        padding: var(--spacing-md);
    }

    #letter-feedback {
        font-size: 1.25rem;
        letter-spacing: 1px;
    }

    #action-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
    }

    #action-buttons button {
        width: 100%;
        max-width: 280px;
        margin: 0;
    }
}

/* 超小屏幕 (480px及以下) */
@media (max-width: 480px) {
    body {
        font-size: 0.875rem;
    }

    #spelling-input {
        font-size: 0.875rem;
        min-width: 120px;
    }

    #letter-feedback {
        font-size: 1.125rem;
        letter-spacing: 0.5px;
    }

    #action-buttons button {
        max-width: 100%;
        font-size: 0.875rem;
    }
}

/* 横屏模式优化 */
@media (max-height: 500px) and (orientation: landscape) {
    .container {
        margin: var(--spacing-xs) auto;
        padding: var(--spacing-sm);
        min-height: auto;
    }

    h1 {
        font-size: 1.5rem;
        margin-bottom: var(--spacing-sm);
    }

    #spelling-container {
        margin: var(--spacing-sm) 0;
    }
}

/* ========== 触摸反馈和交互优化 ========== */
.touch-active {
    transform: scale(0.95);
    opacity: 0.8;
    transition: all 0.1s ease;
}

/* 减少动画偏好支持 */
.reduce-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}

/* 设备特定类 */
.mobile .container {
    padding: var(--spacing-sm);
}

.tablet .container {
    padding: var(--spacing-md);
}

.desktop .container {
    padding: var(--spacing-lg);
}

/* 触摸设备优化 */
.touch-device button,
.touch-device .btn,
.touch-device input,
.touch-device select {
    min-height: 44px;
}

.touch-device.mobile button,
.touch-device.mobile .btn,
.touch-device.mobile input,
.touch-device.mobile select {
    min-height: 48px;
}

/* 焦点可见性增强 */
.touch-device button:focus,
.touch-device .btn:focus,
.touch-device input:focus,
.touch-device select:focus,
.touch-device a:focus {
    outline: 3px solid var(--primary-color);
    outline-offset: 2px;
}

/* 懒加载元素 */
.lazy-load {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.lazy-load.in-view {
    opacity: 1;
    transform: translateY(0);
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #000;
        --secondary-color: #000;
        --light-color: #fff;
        --dark-color: #000;
    }

    .btn {
        border: 2px solid currentColor;
        background: white;
        color: black;
    }

    .btn:hover {
        background: black;
        color: white;
    }
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }

    .container,
    .dashboard-container {
        background: rgba(42, 42, 42, 0.95);
        color: #e0e0e0;
    }

    input, select, textarea {
        background-color: #2a2a2a;
        color: #e0e0e0;
        border-color: #555;
    }

    .btn {
        background-color: #4ade80;
        color: #1a1a1a;
    }

    .btn:hover {
        background-color: #22c55e;
    }
}
