/**
 * 基础样式 - 重置和全局样式
 * 作者: AI-Alpha 前端架构师
 * 创建时间: 2025-07-10 17:35
 * 目的: 提供一致的基础样式和重置，替代原learning.html中的内联样式
 */

/* 导入变量 */
@import url('./utils/variables.css');

/* ===== CSS重置 ===== */

*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    font-family: var(--font-family-primary);
    line-height: var(--line-height-normal);
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

body {
    margin: 0;
    padding: 0;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--color-text-primary);
    text-align: left;
    background-color: var(--color-bg-primary);
    
    /* 学习页面特定背景 */
    background-image: url('/static/images/background.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    
    /* 布局 */
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== 文本元素重置 ===== */

h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
}

h1 { font-size: var(--font-size-xxxl); }
h2 { font-size: var(--font-size-xxl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-md); }
h6 { font-size: var(--font-size-sm); }

p {
    margin-top: 0;
    margin-bottom: var(--spacing-md);
}

ul, ol {
    margin-top: 0;
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-lg);
}

li {
    margin-bottom: var(--spacing-xs);
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover,
a:focus {
    color: var(--color-primary-dark);
    text-decoration: underline;
}

/* ===== 表单元素基础样式 ===== */

input,
select,
textarea,
button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
select,
textarea {
    width: 100%;
    height: var(--form-control-height);
    padding: var(--form-control-padding-y) var(--form-control-padding-x);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--color-text-primary);
    background-color: var(--color-bg-primary);
    background-clip: padding-box;
    border: var(--form-control-border-width) solid var(--color-border-primary);
    border-radius: var(--form-control-border-radius);
    transition: border-color var(--transition-fast), 
                box-shadow var(--transition-fast);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus {
    color: var(--color-text-primary);
    background-color: var(--color-bg-primary);
    border-color: var(--color-border-focus);
    outline: 0;
    box-shadow: var(--input-focus-shadow);
}

input[type="text"]:disabled,
input[type="email"]:disabled,
input[type="password"]:disabled,
input[type="number"]:disabled,
select:disabled,
textarea:disabled {
    background-color: var(--color-bg-secondary);
    border-color: var(--color-border-secondary);
    opacity: 0.6;
    cursor: not-allowed;
}

textarea {
    height: auto;
    resize: vertical;
}

/* ===== 按钮基础样式 ===== */

button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--btn-padding-y) var(--btn-padding-x);
    font-size: var(--font-size-md);
    font-weight: var(--btn-font-weight);
    line-height: var(--line-height-normal);
    color: var(--color-text-primary);
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    background-color: var(--color-bg-primary);
    border: var(--btn-border-width) solid var(--color-border-primary);
    border-radius: var(--btn-border-radius);
    transition: color var(--transition-fast),
                background-color var(--transition-fast),
                border-color var(--transition-fast),
                box-shadow var(--transition-fast);
}

button:hover {
    color: var(--color-text-primary);
    background-color: var(--color-bg-secondary);
    border-color: var(--color-border-primary);
}

button:focus {
    outline: 0;
    box-shadow: 0 0 0 var(--form-control-focus-width) var(--color-primary-alpha);
}

button:disabled {
    pointer-events: none;
    opacity: 0.6;
}

button:active {
    background-color: var(--color-bg-tertiary);
    border-color: var(--color-border-primary);
}

/* ===== 主布局容器 ===== */

.main-container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.dashboard-container {
    display: flex;
    width: 100%;
    max-width: var(--container-xl);
    padding: var(--spacing-lg);
    background-color: var(--color-bg-overlay);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    gap: var(--spacing-lg);
    overflow: auto;
    max-height: 100vh;
}

.left-column {
    flex: 1;
    min-width: 300px;
}

.right-column {
    flex: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 400px;
}

/* ===== 工具类 ===== */

/* 显示/隐藏 */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

/* 文本对齐 */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

/* 文本颜色 */
.text-primary { color: var(--color-primary) !important; }
.text-secondary { color: var(--color-text-secondary) !important; }
.text-success { color: var(--color-success) !important; }
.text-error { color: var(--color-error) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-info { color: var(--color-info) !important; }
.text-muted { color: var(--color-text-muted) !important; }

/* 背景颜色 */
.bg-primary { background-color: var(--color-primary) !important; }
.bg-secondary { background-color: var(--color-bg-secondary) !important; }
.bg-success { background-color: var(--color-success) !important; }
.bg-error { background-color: var(--color-error) !important; }
.bg-warning { background-color: var(--color-warning) !important; }
.bg-info { background-color: var(--color-info) !important; }

/* 间距 */
.m-0 { margin: 0 !important; }
.mt-0 { margin-top: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }
.mr-0 { margin-right: 0 !important; }

.m-1 { margin: var(--spacing-xs) !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.ml-1 { margin-left: var(--spacing-xs) !important; }
.mr-1 { margin-right: var(--spacing-xs) !important; }

.m-2 { margin: var(--spacing-sm) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.ml-2 { margin-left: var(--spacing-sm) !important; }
.mr-2 { margin-right: var(--spacing-sm) !important; }

.m-3 { margin: var(--spacing-md) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.ml-3 { margin-left: var(--spacing-md) !important; }
.mr-3 { margin-right: var(--spacing-md) !important; }

.m-4 { margin: var(--spacing-lg) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.ml-4 { margin-left: var(--spacing-lg) !important; }
.mr-4 { margin-right: var(--spacing-lg) !important; }

.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }
.pr-0 { padding-right: 0 !important; }

.p-1 { padding: var(--spacing-xs) !important; }
.pt-1 { padding-top: var(--spacing-xs) !important; }
.pb-1 { padding-bottom: var(--spacing-xs) !important; }
.pl-1 { padding-left: var(--spacing-xs) !important; }
.pr-1 { padding-right: var(--spacing-xs) !important; }

.p-2 { padding: var(--spacing-sm) !important; }
.pt-2 { padding-top: var(--spacing-sm) !important; }
.pb-2 { padding-bottom: var(--spacing-sm) !important; }
.pl-2 { padding-left: var(--spacing-sm) !important; }
.pr-2 { padding-right: var(--spacing-sm) !important; }

.p-3 { padding: var(--spacing-md) !important; }
.pt-3 { padding-top: var(--spacing-md) !important; }
.pb-3 { padding-bottom: var(--spacing-md) !important; }
.pl-3 { padding-left: var(--spacing-md) !important; }
.pr-3 { padding-right: var(--spacing-md) !important; }

.p-4 { padding: var(--spacing-lg) !important; }
.pt-4 { padding-top: var(--spacing-lg) !important; }
.pb-4 { padding-bottom: var(--spacing-lg) !important; }
.pl-4 { padding-left: var(--spacing-lg) !important; }
.pr-4 { padding-right: var(--spacing-lg) !important; }

/* Flex工具类 */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.justify-content-start { justify-content: flex-start !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-between { justify-content: space-between !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-center { align-items: center !important; }
.align-items-end { align-items: flex-end !important; }

/* 阴影 */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* 圆角 */
.rounded { border-radius: var(--border-radius-md) !important; }
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-pill { border-radius: var(--border-radius-pill) !important; }

/* 位置 */
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* 宽度和高度 */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }

/* ===== 动画关键帧 ===== */

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideInDown {
    from {
        transform: translate3d(0, -100%, 0);
        visibility: visible;
    }
    to {
        transform: translate3d(0, 0, 0);
    }
}

@keyframes slideInUp {
    from {
        transform: translate3d(0, 100%, 0);
        visibility: visible;
    }
    to {
        transform: translate3d(0, 0, 0);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        transform: translate3d(0, -30px, 0);
    }
    70% {
        animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes pulse {
    0% { transform: scale3d(1, 1, 1); }
    50% { transform: scale3d(1.05, 1.05, 1.05); }
    100% { transform: scale3d(1, 1, 1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== 动画工具类 ===== */

.animate-fadeIn { animation: fadeIn var(--transition-normal) ease-in-out; }
.animate-fadeOut { animation: fadeOut var(--transition-normal) ease-in-out; }
.animate-slideInDown { animation: slideInDown var(--transition-normal) ease-out; }
.animate-slideInUp { animation: slideInUp var(--transition-normal) ease-out; }
.animate-bounce { animation: bounce 1s infinite; }
.animate-pulse { animation: pulse 2s infinite; }
.animate-spin { animation: spin 1s linear infinite; }

/* ===== 响应式工具类 ===== */

@media (max-width: 767.98px) {
    .d-sm-none { display: none !important; }
    .d-sm-block { display: block !important; }
    .d-sm-flex { display: flex !important; }
    
    .dashboard-container {
        flex-direction: column;
        padding: var(--spacing-md);
    }
    
    .left-column,
    .right-column {
        min-width: unset;
    }
}

@media (max-width: 575.98px) {
    .dashboard-container {
        padding: var(--spacing-sm);
        margin: var(--spacing-sm);
    }
}

/* ===== 打印样式 ===== */

@media print {
    *,
    *::before,
    *::after {
        text-shadow: none !important;
        box-shadow: none !important;
    }
    
    a:not(.btn) {
        text-decoration: underline;
    }
    
    body {
        background: white !important;
    }
    
    .dashboard-container {
        background: white !important;
        box-shadow: none !important;
    }
}