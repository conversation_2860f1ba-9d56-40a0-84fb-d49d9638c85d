/**
 * 单词显示组件样式
 * 作者: AI-Alpha 前端架构师
 * 创建时间: 2025-07-10 17:40
 * 目的: 单词卡片、图片、音频、星级等显示元素的样式
 */

/* 导入基础变量 */
@import url('../utils/variables.css');

/* ===== 单词容器主体 ===== */

#word-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
    position: relative;
    text-align: center;
}

/* ===== 单词显示区域 ===== */

#word {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-sm);
    text-align: center;
}

#word-and-pronounce {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

#pronounce-button {
    font-size: var(--font-size-lg);
    cursor: pointer;
    transition: transform var(--transition-fast);
    user-select: none;
    margin-left: var(--spacing-sm);
}

#pronounce-button:hover {
    transform: scale(1.1);
}

#pronounce-button:active {
    transform: scale(0.95);
}

/* ===== 图片显示区域 ===== */

#word-image {
    width: 100px;
    height: auto;
    margin-bottom: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    transition: opacity var(--transition-normal), 
                filter var(--transition-normal),
                transform var(--transition-fast);
    cursor: pointer;
}

#word-image:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-md);
}

/* 图片加载状态 */
#word-image.loading {
    filter: blur(2px);
    opacity: 0.7;
}

#word-image.loaded {
    filter: none;
    opacity: 1;
}

/* 图片错误状态 */
#word-image.error {
    opacity: 0.5;
    filter: grayscale(100%);
}

/* ===== 图片状态指示器 ===== */

.image-status {
    position: absolute;
    top: var(--spacing-xs);
    right: var(--spacing-xs);
    background: var(--color-bg-modal);
    color: var(--color-text-inverse);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    display: none;
    z-index: var(--z-index-tooltip);
}

.image-status.generating {
    background: var(--color-warning);
    color: var(--color-text-primary);
    display: block;
}

.image-status.success {
    background: var(--color-success);
    display: block;
}

.image-status.error {
    background: var(--color-error);
    display: block;
}

/* ===== 提示区域 ===== */

#hint {
    display: block;
    margin-top: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    font-style: italic;
    font-size: var(--font-size-sm);
    color: var(--color-text-muted);
    text-align: center;
    line-height: var(--line-height-normal);
    max-width: 100%;
    word-wrap: break-word;
}

/* ===== 星级显示系统 ===== */

.star-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    margin: var(--spacing-sm) 0;
}

.star {
    font-size: var(--star-size);
    transition: color var(--transition-fast), 
                transform var(--transition-fast);
    cursor: default;
}

.star.active {
    color: var(--star-color-active);
}

.star.inactive {
    color: var(--star-color-inactive);
}

.star:hover {
    transform: scale(1.1);
}

/* 星级动画效果 */
@keyframes starGlow {
    0%, 100% {
        filter: brightness(1);
        transform: scale(1);
    }
    50% {
        filter: brightness(1.3);
        transform: scale(1.1);
    }
}

.star.active.glow {
    animation: starGlow 0.6s ease-in-out;
}

/* ===== 单词卡片容器 ===== */

.word-card {
    background: var(--word-card-bg);
    border: 1px solid var(--word-card-border);
    border-radius: var(--word-card-border-radius);
    padding: var(--word-card-padding);
    box-shadow: var(--word-card-shadow);
    transition: transform var(--transition-fast),
                box-shadow var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.word-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 单词卡片头部 */
.word-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--color-border-secondary);
}

.word-card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    margin: 0;
}

.word-card-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
}

/* 单词卡片内容 */
.word-card-content {
    text-align: center;
}

.word-display {
    font-size: var(--font-size-xxxl);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-primary);
    margin-bottom: var(--spacing-lg);
    line-height: var(--line-height-tight);
}

/* ===== 熟练度指示器 ===== */

.proficiency-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-pill);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.proficiency-very-low {
    background: var(--proficiency-very-low-alpha);
    color: var(--proficiency-very-low);
    border: 1px solid var(--proficiency-very-low);
}

.proficiency-low {
    background: var(--proficiency-low-alpha);
    color: var(--proficiency-low);
    border: 1px solid var(--proficiency-low);
}

.proficiency-medium {
    background: var(--proficiency-medium-alpha);
    color: var(--proficiency-medium);
    border: 1px solid var(--proficiency-medium);
}

.proficiency-high {
    background: var(--proficiency-high-alpha);
    color: var(--proficiency-high);
    border: 1px solid var(--proficiency-high);
}

/* ===== 响应式设计 ===== */

@media (max-width: 767.98px) {
    #word-container {
        margin-bottom: var(--spacing-sm);
    }
    
    #word-container img {
        width: 80px;
        margin-bottom: var(--spacing-sm);
    }
    
    #word {
        font-size: var(--font-size-xl);
    }
    
    #hint {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-sm);
    }
    
    .word-display {
        font-size: var(--font-size-xxl);
    }
    
    .star {
        font-size: 18px;
    }
}

@media (max-width: 575.98px) {
    #word {
        font-size: var(--font-size-lg);
    }
    
    .word-display {
        font-size: var(--font-size-xl);
    }
    
    .word-card {
        padding: var(--spacing-md);
    }
    
    .star {
        font-size: 16px;
    }
}

/* ===== 打印样式 ===== */

@media print {
    #word-container {
        break-inside: avoid;
    }
    
    #word-image {
        filter: none !important;
        opacity: 1 !important;
    }
    
    .image-status {
        display: none !important;
    }
    
    .star {
        color: #000 !important;
    }
}