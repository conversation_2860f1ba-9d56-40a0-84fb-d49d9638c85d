/**
 * 布局组件样式
 * 作者: AI-Alpha 前端架构师
 * 创建时间: 2025-07-10 17:50
 * 目的: 页面整体布局、容器、网格系统等布局相关样式
 */

/* 导入基础变量 */
@import url('../utils/variables.css');

/* ===== 页面整体布局 ===== */

body {
    /* 基础样式在 base.css 中定义 */
    /* 学习页面特有的布局样式 */
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* 背景遮罩层 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    z-index: -1;
    pointer-events: none;
}

/* ===== 主容器布局 ===== */

.main-container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: var(--spacing-lg);
    box-sizing: border-box;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== 仪表板容器 ===== */

.dashboard-container {
    display: flex;
    width: 100%;
    max-width: var(--container-xl);
    padding: var(--spacing-lg);
    background-color: var(--color-bg-overlay);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    gap: var(--spacing-lg);
    overflow: auto;
    max-height: 100vh;
    box-sizing: border-box;
    margin: 0 auto;
    position: relative;
}

/* 容器内部布局增强 */
.dashboard-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: var(--border-radius-xl);
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 100%
    );
    pointer-events: none;
    z-index: 0;
}

.dashboard-container > * {
    position: relative;
    z-index: 1;
}

/* ===== 左右列布局 ===== */

.left-column {
    flex: 1;
    min-width: 300px;
    /* 其他样式在 sidebar.css 中定义 */
}

.right-column {
    flex: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 400px;
    background: var(--color-bg-overlay);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-border-secondary);
    gap: var(--spacing-md);
}

/* 右列内容区域 */
.right-column-content {
    width: 100%;
    max-width: 600px;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* ===== 卡片容器布局 ===== */

.card-container {
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-lg);
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.card-container:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 卡片头部 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--color-border-secondary);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    margin: 0;
}

.card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    margin: 0;
}

/* 卡片内容 */
.card-content {
    flex: 1;
}

.card-footer {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--color-border-secondary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* ===== 网格系统 ===== */

.grid {
    display: grid;
    gap: var(--grid-gap-md);
    width: 100%;
}

.grid-cols-1 { grid-template-columns: 1fr; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.grid-gap-sm { gap: var(--grid-gap-sm); }
.grid-gap-md { gap: var(--grid-gap-md); }
.grid-gap-lg { gap: var(--grid-gap-lg); }

/* 网格项目 */
.grid-item {
    min-width: 0; /* 防止溢出 */
}

.grid-span-2 { grid-column: span 2; }
.grid-span-3 { grid-column: span 3; }
.grid-span-4 { grid-column: span 4; }

/* ===== Flex布局工具 ===== */

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.flex-row {
    flex-direction: row;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-nowrap {
    flex-wrap: nowrap;
}

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }
.items-baseline { align-items: baseline; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

/* ===== 容器尺寸变体 ===== */

.container-sm {
    max-width: var(--container-sm);
    margin: 0 auto;
}

.container-md {
    max-width: var(--container-md);
    margin: 0 auto;
}

.container-lg {
    max-width: var(--container-lg);
    margin: 0 auto;
}

.container-xl {
    max-width: var(--container-xl);
    margin: 0 auto;
}

.container-full {
    width: 100%;
    max-width: none;
}

/* ===== 固定定位元素 ===== */

.fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-index-fixed);
    background: var(--color-bg-overlay);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--color-border-secondary);
    padding: var(--spacing-sm) 0;
}

.fixed-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: var(--z-index-fixed);
    background: var(--color-bg-overlay);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--color-border-secondary);
    padding: var(--spacing-sm) 0;
}

/* ===== 浮动指示器 ===== */

.voucher-indicator {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: linear-gradient(45deg, var(--color-primary), var(--color-primary-dark));
    color: var(--color-text-inverse);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-pill);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    box-shadow: var(--shadow-lg);
    opacity: 0;
    transition: all var(--transition-normal);
    z-index: var(--z-index-modal);
    transform: translateY(-20px);
}

.voucher-indicator.show {
    opacity: 1;
    transform: translateY(0);
}

/* ===== 低正确率容器 ===== */

.low-correct-rate-container {
    margin-top: var(--spacing-xl);
    width: 100%;
    max-width: 800px;
    background-color: var(--color-bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-border-primary);
    display: none; /* 默认隐藏 */
}

.low-correct-rate-container h2 {
    margin-top: 0;
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-xl);
    color: var(--color-text-primary);
    border-bottom: 2px solid var(--color-primary);
    padding-bottom: var(--spacing-sm);
}

.low-correct-rate-container table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--spacing-md);
}

.low-correct-rate-container th,
.low-correct-rate-container td {
    border: 1px solid var(--color-border-primary);
    padding: var(--spacing-sm);
    text-align: left;
    font-size: var(--font-size-sm);
}

.low-correct-rate-container th {
    background-color: var(--color-bg-secondary);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
}

.low-correct-rate-container tbody tr:nth-child(even) {
    background-color: var(--color-bg-secondary);
}

.low-correct-rate-container tbody tr:hover {
    background-color: var(--color-primary-alpha);
}

/* ===== 管理员面板 ===== */

.admin-panel {
    position: fixed;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    z-index: var(--z-index-modal);
}

/* ===== 响应式布局 ===== */

@media (max-width: 767.98px) {
    .main-container {
        padding: var(--spacing-sm);
    }
    
    .dashboard-container {
        flex-direction: column;
        gap: var(--spacing-sm);
        padding: var(--spacing-sm);
        max-width: 100%;
        max-height: none;
        overflow: visible;
    }
    
    .left-column {
        flex: none;
        order: 1;
        min-width: unset;
        width: 100%;
    }
    
    .right-column {
        flex: none;
        order: 2;
        min-width: unset;
        width: 100%;
        padding: var(--spacing-md);
    }
    
    .card-container {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: 1fr;
    }
    
    .button-group.horizontal {
        flex-direction: column;
    }
    
    .voucher-indicator {
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        font-size: var(--font-size-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .low-correct-rate-container {
        padding: var(--spacing-md);
        margin-top: var(--spacing-md);
    }
    
    .low-correct-rate-container table {
        font-size: var(--font-size-xs);
    }
    
    .low-correct-rate-container th,
    .low-correct-rate-container td {
        padding: var(--spacing-xs);
    }
}

@media (max-width: 575.98px) {
    .dashboard-container {
        padding: var(--spacing-xs);
        margin: var(--spacing-xs);
        border-radius: var(--border-radius-lg);
    }
    
    .left-column,
    .right-column {
        padding: var(--spacing-sm);
    }
    
    .card-container {
        padding: var(--spacing-sm);
    }
    
    .grid {
        gap: var(--grid-gap-sm);
    }
    
    .container-sm,
    .container-md,
    .container-lg,
    .container-xl {
        padding: 0 var(--spacing-sm);
    }
}

/* ===== 横屏平板优化 ===== */

@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
    .dashboard-container {
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .left-column {
        flex: 0 0 240px;
        min-width: 240px;
    }
    
    .right-column {
        flex: 1;
        min-width: 300px;
    }
}

/* ===== 大屏幕优化 ===== */

@media (min-width: 1200px) {
    .dashboard-container {
        max-width: 1400px;
        padding: var(--spacing-xl);
    }
    
    .left-column {
        flex: 0 0 320px;
        min-width: 320px;
    }
    
    .right-column {
        padding: var(--spacing-xl);
    }
    
    .card-container {
        padding: var(--spacing-xl);
    }
}

/* ===== 可访问性增强 ===== */

@media (prefers-reduced-motion: reduce) {
    .dashboard-container,
    .card-container,
    .voucher-indicator {
        transition: none;
    }
    
    .dashboard-container::before {
        animation: none;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .dashboard-container {
        border: 2px solid var(--color-text-primary);
        background: var(--color-bg-primary);
    }
    
    .card-container {
        border: 2px solid var(--color-text-primary);
    }
    
    .low-correct-rate-container {
        border: 2px solid var(--color-text-primary);
    }
}

/* ===== 打印样式 ===== */

@media print {
    body {
        background: white !important;
    }
    
    .dashboard-container {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #000 !important;
        max-height: none !important;
        overflow: visible !important;
        flex-direction: column !important;
        page-break-inside: avoid;
    }
    
    .voucher-indicator,
    .admin-panel {
        display: none !important;
    }
    
    .card-container {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #000 !important;
        background: white !important;
    }
    
    .low-correct-rate-container {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
}