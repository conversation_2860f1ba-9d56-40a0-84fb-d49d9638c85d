/**
 * 反馈和消息组件样式
 * 作者: AI-Alpha 前端架构师
 * 创建时间: 2025-07-10 17:46
 * 目的: 答题反馈、错误提示、成功消息、AI记忆帮助等反馈相关样式
 */

/* 导入基础变量 */
@import url('../utils/variables.css');

/* ===== 通用反馈容器 ===== */

#feedback {
    margin: var(--spacing-md) 0;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    transition: all var(--transition-normal);
    text-align: center;
    min-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* ===== 反馈状态样式 ===== */

#feedback.correct {
    background: var(--feedback-correct-bg);
    color: var(--color-success);
    border: 1px solid var(--feedback-correct-border);
    animation: feedbackSuccess 0.6s ease-out;
}

#feedback.incorrect {
    background: var(--feedback-incorrect-bg);
    color: var(--color-error);
    border: 1px solid var(--feedback-incorrect-border);
    animation: feedbackError 0.6s ease-out;
}

#feedback.near-correct {
    background: var(--color-warning-alpha);
    color: var(--color-warning-dark);
    border: 1px solid var(--color-warning);
    animation: feedbackWarning 0.6s ease-out;
}

#feedback.info {
    background: var(--color-info-alpha);
    color: var(--color-info-dark);
    border: 1px solid var(--color-info);
}

/* ===== 消息区域样式 ===== */

#message {
    margin: var(--spacing-md) 0;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-md);
    line-height: var(--line-height-normal);
    text-align: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

#message.success {
    background: var(--feedback-correct-bg);
    color: var(--color-success);
    border: 1px solid var(--feedback-correct-border);
    opacity: 1;
}

#message.error {
    background: var(--feedback-incorrect-bg);
    color: var(--color-error);
    border: 1px solid var(--feedback-incorrect-border);
    opacity: 1;
}

#message.warning {
    background: var(--color-warning-alpha);
    color: var(--color-warning-dark);
    border: 1px solid var(--color-warning);
    opacity: 1;
}

#message.info {
    background: var(--color-info-alpha);
    color: var(--color-info-dark);
    border: 1px solid var(--color-info);
    opacity: 1;
}

/* ===== AI记忆帮助区域 ===== */

#memory-help {
    margin: var(--spacing-lg) 0;
    padding: 0;
    background: transparent;
    border-radius: var(--border-radius-lg);
    line-height: var(--line-height-loose);
    text-align: left;
    max-width: 100%;
    word-wrap: break-word;
}

#memory-help:not(:empty) {
    padding: var(--spacing-lg);
    background: var(--color-bg-secondary);
    border: 1px solid var(--color-border-secondary);
    box-shadow: var(--shadow-sm);
}

/* AI记忆帮助内容样式 */
#memory-help h1,
#memory-help h2,
#memory-help h3 {
    margin: 0 0 var(--spacing-md) 0;
    padding: 0;
    color: var(--color-text-primary);
    font-weight: var(--font-weight-semibold);
}

#memory-help h1 {
    font-size: var(--font-size-xl);
    border-bottom: 2px solid var(--color-primary);
    padding-bottom: var(--spacing-sm);
}

#memory-help h2 {
    font-size: var(--font-size-lg);
    color: var(--color-primary);
}

#memory-help h3 {
    font-size: var(--font-size-md);
    color: var(--color-text-secondary);
}

#memory-help ul {
    margin: var(--spacing-sm) 0;
    padding-left: var(--spacing-lg);
    list-style-type: disc;
}

#memory-help ol {
    margin: var(--spacing-sm) 0;
    padding-left: var(--spacing-lg);
}

#memory-help li {
    margin-bottom: var(--spacing-xs);
    color: var(--color-text-primary);
}

#memory-help p {
    margin: var(--spacing-sm) 0;
    color: var(--color-text-primary);
}

#memory-help strong {
    color: var(--color-primary);
    font-weight: var(--font-weight-semibold);
}

#memory-help em {
    color: var(--color-text-secondary);
    font-style: italic;
}

/* AI思考状态 */
.ai-thinking {
    text-align: center;
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border-radius: var(--border-radius-lg);
    border: 2px dashed var(--color-primary);
    margin: var(--spacing-lg) 0;
}

.ai-thinking .robot-icon {
    font-size: var(--font-size-xxxl);
    margin-bottom: var(--spacing-md);
    animation: robotThinking 2s ease-in-out infinite;
}

.ai-thinking .thinking-text {
    font-weight: var(--font-weight-semibold);
    color: var(--color-primary);
    margin-bottom: var(--spacing-sm);
}

.loading-dots {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-sm);
}

.loading-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color-primary);
    animation: dotBlink 1.4s infinite both;
}

.loading-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

/* ===== 报告消息区域 ===== */

#report-message {
    margin: var(--spacing-lg) 0;
    padding: 0;
    background: transparent;
    border-radius: var(--border-radius-lg);
}

#report-message:not(:empty) {
    padding: var(--spacing-lg);
    background: var(--color-bg-primary);
    border: 1px solid var(--color-border-primary);
    box-shadow: var(--shadow-md);
}

/* ===== 特殊消息样式 ===== */

.encouragement-message {
    background: linear-gradient(135deg, var(--color-success-alpha) 0%, var(--color-info-alpha) 100%);
    border: 1px solid var(--color-success);
    color: var(--color-success-dark);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    text-align: center;
    margin: var(--spacing-lg) 0;
    animation: encouragementGlow 1s ease-out;
}

.warning-message {
    background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-dark) 100%);
    color: var(--color-text-inverse);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    text-align: center;
    margin: var(--spacing-lg) 0;
    box-shadow: var(--shadow-lg);
}

.error-message {
    background: var(--feedback-incorrect-bg);
    border: 2px solid var(--feedback-incorrect-border);
    color: var(--color-error-dark);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin: var(--spacing-lg) 0;
}

.info-message {
    background: var(--color-info-alpha);
    border: 1px solid var(--color-info);
    color: var(--color-info-dark);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin: var(--spacing-md) 0;
}

/* ===== 表情符号和图标 ===== */

.emoji {
    font-size: var(--font-size-xl);
    margin: 0 var(--spacing-xs);
    display: inline-block;
    animation: emojiPulse 1s ease-in-out infinite alternate;
}

.flower {
    font-size: var(--font-size-xl);
    color: var(--color-success);
    animation: flowerBounce 0.8s ease-out;
}

/* ===== 动画效果 ===== */

@keyframes feedbackSuccess {
    0% {
        transform: scale(0.9);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes feedbackError {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

@keyframes feedbackWarning {
    0% {
        transform: scale(1);
        filter: brightness(1);
    }
    50% {
        transform: scale(1.02);
        filter: brightness(1.1);
    }
    100% {
        transform: scale(1);
        filter: brightness(1);
    }
}

@keyframes robotThinking {
    0%, 100% {
        transform: rotate(-5deg);
    }
    50% {
        transform: rotate(5deg);
    }
}

@keyframes dotBlink {
    0%, 80%, 100% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    40% {
        opacity: 1;
        transform: scale(1.2);
    }
}

@keyframes encouragementGlow {
    0% {
        box-shadow: 0 0 0 0 var(--color-success-alpha);
    }
    50% {
        box-shadow: 0 0 20px 10px var(--color-success-alpha);
    }
    100% {
        box-shadow: 0 0 0 0 var(--color-success-alpha);
    }
}

@keyframes emojiPulse {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.1);
    }
}

@keyframes flowerBounce {
    0% {
        transform: scale(0) rotate(0deg);
    }
    50% {
        transform: scale(1.2) rotate(180deg);
    }
    100% {
        transform: scale(1) rotate(360deg);
    }
}

/* ===== 响应式设计 ===== */

@media (max-width: 767.98px) {
    #feedback,
    #message,
    #memory-help,
    #report-message {
        margin: var(--spacing-md) 0;
        padding: var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .ai-thinking {
        padding: var(--spacing-lg);
    }
    
    .encouragement-message,
    .warning-message,
    .error-message {
        padding: var(--spacing-md);
        margin: var(--spacing-md) 0;
    }
    
    .emoji,
    .flower {
        font-size: var(--font-size-lg);
    }
}

@media (max-width: 575.98px) {
    #feedback,
    #message {
        font-size: var(--font-size-sm);
        padding: var(--spacing-sm);
    }
    
    #memory-help h1 {
        font-size: var(--font-size-lg);
    }
    
    #memory-help h2 {
        font-size: var(--font-size-md);
    }
    
    #memory-help h3 {
        font-size: var(--font-size-sm);
    }
    
    .ai-thinking {
        padding: var(--spacing-md);
    }
}

/* ===== 可访问性增强 ===== */

@media (prefers-reduced-motion: reduce) {
    #feedback,
    #message,
    .emoji,
    .flower,
    .ai-thinking .robot-icon,
    .loading-dots span {
        animation: none;
    }
    
    .encouragement-message,
    .warning-message {
        animation: none;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    #feedback.correct {
        background: var(--color-success);
        color: white;
        border-color: var(--color-success-dark);
    }
    
    #feedback.incorrect {
        background: var(--color-error);
        color: white;
        border-color: var(--color-error-dark);
    }
    
    #feedback.near-correct {
        background: var(--color-warning);
        color: black;
        border-color: var(--color-warning-dark);
    }
    
    #memory-help {
        border: 2px solid var(--color-text-primary);
    }
}

/* ===== 打印样式 ===== */

@media print {
    #feedback,
    #message,
    #memory-help,
    #report-message {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #000 !important;
        background: white !important;
        color: black !important;
    }
    
    .ai-thinking,
    .loading-dots {
        display: none;
    }
    
    .emoji,
    .flower {
        filter: grayscale(100%);
    }
}