/**
 * 侧边栏组件样式
 * 作者: AI-Alpha 前端架构师
 * 创建时间: 2025-07-10 17:48
 * 目的: 左侧栏今日计划、积分显示、星级筛选、导出按钮等组件样式
 */

/* 导入基础变量 */
@import url('../utils/variables.css');

/* ===== 左侧栏整体布局 ===== */

.left-column {
    flex: 0 0 280px;
    background: var(--color-bg-overlay);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-border-secondary);
    height: fit-content;
    max-height: calc(100vh - var(--spacing-xl));
    overflow-y: auto;
}

/* ===== 返回按钮 ===== */

.back-to-dashboard {
    display: inline-block;
    background: var(--color-bg-secondary);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border-primary);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
    margin-bottom: var(--spacing-md);
    align-self: flex-start;
}

.back-to-dashboard:hover {
    background: var(--color-bg-tertiary);
    border-color: var(--color-border-secondary);
    transform: translateY(-1px);
    text-decoration: none;
    box-shadow: var(--shadow-sm);
}

.back-to-dashboard:active {
    transform: translateY(0);
}

/* ===== 头部容器 ===== */

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--color-border-secondary);
}

.header-container h1 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    flex: 1;
}

/* ===== 积分显示区域 ===== */

#score-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: var(--spacing-sm);
}

#score {
    margin: 0;
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-primary);
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    flex: 0 0 auto;
    background: transparent;
    border: none;
}

#score:hover {
    background: var(--color-bg-secondary);
    transform: scale(1.05);
}

/* ===== 购物券显示区域 ===== */

.voucher-display {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.voucher-display:hover {
    background: var(--color-bg-secondary);
}

#voucher-count {
    color: var(--color-warning);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-md);
    animation: voucherPulse 2s ease-in-out infinite;
}

/* 购物券悬浮提示 */
.voucher-tooltip {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--color-text-inverse);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    min-width: 280px;
    z-index: var(--z-index-tooltip);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
}

.voucher-tooltip.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
}

.voucher-tooltip::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #667eea;
}

/* ===== 复选框组 ===== */

.checkbox-group {
    display: flex;
    justify-content: flex-start;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.checkbox-group label {
    font-size: var(--font-size-sm);
    color: var(--color-text-primary);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
    user-select: none;
}

.checkbox-group label:hover {
    background: var(--color-bg-secondary);
}

.checkbox-group input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 0;
    padding: 0;
    accent-color: var(--color-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.checkbox-group input[type="checkbox"]:checked {
    transform: scale(1.1);
}

/* ===== 今日计划区域 ===== */

#daily-plan {
    font-size: var(--font-size-sm);
    text-align: left;
    margin-bottom: var(--spacing-md);
    background: var(--color-bg-primary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-border-secondary);
}

#daily-plan h2 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    color: var(--color-text-primary);
    border-bottom: 1px solid var(--color-border-secondary);
    padding-bottom: var(--spacing-xs);
}

#daily-plan p {
    margin: var(--spacing-xs) 0;
    line-height: var(--line-height-normal);
    color: var(--color-text-primary);
    font-size: var(--font-size-sm);
}

#daily-plan a {
    font-size: var(--font-size-xs);
    color: var(--color-primary);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
}

#daily-plan a:hover {
    text-decoration: underline;
    color: var(--color-primary-dark);
}

/* ===== 星级筛选容器 ===== */

.star-filter-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    margin: var(--spacing-sm) 0;
    background: var(--color-bg-secondary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--color-border-secondary);
}

.star-filter-item {
    display: flex;
    align-items: center;
}

.star-filter-item label {
    font-size: var(--font-size-xs);
    color: var(--color-text-primary);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-xs);
    width: 100%;
    transition: all var(--transition-fast);
    user-select: none;
}

.star-filter-item label:hover {
    background: var(--color-bg-tertiary);
}

.star-filter-item input[type="checkbox"] {
    width: 14px;
    height: 14px;
    margin: 0;
    padding: 0;
    accent-color: var(--color-primary);
    cursor: pointer;
}

/* ===== 导出按钮 ===== */

#low-correct-rate-btn {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    background: var(--color-bg-secondary);
    color: var(--color-text-primary);
    border: 1px solid var(--color-border-primary);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    line-height: var(--line-height-tight);
    text-align: center;
    white-space: normal;
    height: auto;
    min-height: 36px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

#low-correct-rate-btn:hover {
    background: var(--color-bg-tertiary);
    border-color: var(--color-border-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

#low-correct-rate-btn:active {
    transform: translateY(0);
}

#low-correct-rate-btn .sub-text {
    font-size: var(--font-size-xs);
    color: var(--color-text-muted);
    font-weight: var(--font-weight-normal);
    margin-top: var(--spacing-xs);
}

/* ===== 5星完成状态显示 ===== */

.five-star-completion {
    background: var(--color-success-alpha);
    border: 1px solid var(--color-success);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-top: var(--spacing-sm);
    color: var(--color-success-dark);
}

.five-star-completion label {
    font-size: var(--font-size-sm);
    color: var(--color-success-dark);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    cursor: pointer;
    margin-top: var(--spacing-sm);
}

#include-five-stars {
    margin-right: var(--spacing-xs);
    accent-color: var(--color-success);
}

/* ===== 动画效果 ===== */

@keyframes voucherPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

@keyframes planUpdate {
    0% {
        transform: scale(1);
        background: var(--color-bg-primary);
    }
    50% {
        transform: scale(1.01);
        background: var(--color-info-alpha);
    }
    100% {
        transform: scale(1);
        background: var(--color-bg-primary);
    }
}

/* ===== 响应式设计 ===== */

@media (max-width: 767.98px) {
    .left-column {
        flex: none;
        order: 1;
        padding: var(--spacing-md);
        margin-bottom: 0;
        width: 100%;
        max-width: 100%;
    }
    
    .header-container {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .mobile-controls {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: var(--spacing-lg);
        width: 100%;
    }
    
    #score-container {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: stretch;
    }
    
    #score {
        font-size: var(--font-size-sm);
        text-align: center;
        background: none;
        border: none;
        border-radius: 0;
        box-shadow: none;
    }
    
    .checkbox-group {
        justify-content: center;
        margin-bottom: 0;
        flex: 1;
    }
    
    .checkbox-group label {
        font-size: var(--font-size-xs);
        gap: var(--spacing-xs);
    }
    
    .checkbox-group input[type="checkbox"] {
        width: 14px;
        height: 14px;
    }
    
    #daily-plan {
        font-size: var(--font-size-xs);
        margin-bottom: var(--spacing-sm);
        padding: var(--spacing-sm);
    }
    
    #daily-plan h2 {
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-xs);
    }
    
    #daily-plan p {
        margin: 2px 0;
        font-size: var(--font-size-xs);
    }
    
    #low-correct-rate-btn {
        font-size: var(--font-size-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
        min-height: 32px;
    }
    
    .voucher-tooltip {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        min-width: 260px;
        max-width: 90vw;
    }
}

@media (max-width: 575.98px) {
    .left-column {
        padding: var(--spacing-sm);
    }
    
    .header-container h1 {
        font-size: var(--font-size-sm);
    }
    
    #score {
        font-size: var(--font-size-xs);
    }
    
    .checkbox-group label {
        font-size: var(--font-size-xs);
        gap: var(--spacing-xs);
    }
    
    .checkbox-group input[type="checkbox"] {
        width: 12px;
        height: 12px;
    }
    
    #daily-plan {
        font-size: var(--font-size-xs);
    }
    
    #daily-plan h2 {
        font-size: var(--font-size-xs);
    }
    
    .voucher-tooltip {
        min-width: 240px;
        font-size: var(--font-size-xs);
        padding: var(--spacing-sm);
    }
}

/* ===== 可访问性增强 ===== */

@media (prefers-reduced-motion: reduce) {
    #voucher-count,
    .voucher-tooltip,
    .checkbox-group input[type="checkbox"],
    #score,
    .back-to-dashboard,
    #low-correct-rate-btn {
        animation: none;
        transition: none;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .left-column {
        border: 2px solid var(--color-text-primary);
    }
    
    .checkbox-group input[type="checkbox"] {
        border: 2px solid var(--color-text-primary);
    }
    
    #daily-plan {
        border: 2px solid var(--color-text-primary);
    }
    
    #low-correct-rate-btn {
        border: 2px solid var(--color-text-primary);
        font-weight: var(--font-weight-bold);
    }
}

/* ===== 打印样式 ===== */

@media print {
    .left-column {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
        background: white;
    }
    
    .back-to-dashboard,
    #low-correct-rate-btn,
    .checkbox-group {
        display: none;
    }
    
    .voucher-tooltip {
        display: none;
    }
    
    #daily-plan {
        background: white;
        border: 1px solid #000;
    }
}