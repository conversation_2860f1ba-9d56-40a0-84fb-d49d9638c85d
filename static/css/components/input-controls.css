/**
 * 输入控制组件样式
 * 作者: AI-Alpha 前端架构师
 * 创建时间: 2025-07-10 17:42
 * 目的: 传统模式输入框、拼写模式、检查按钮等输入相关控件样式
 */

/* 导入基础变量 */
@import url('../utils/variables.css');

/* ===== 传统模式输入区域 ===== */

#traditional-mode {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    width: 100%;
}

#traditional-mode input[type="text"] {
    width: 100%;
    height: var(--form-control-height);
    padding: var(--form-control-padding-y) var(--form-control-padding-x);
    font-size: var(--font-size-md);
    font-family: var(--font-family-primary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border-primary);
    background: var(--color-bg-primary);
    color: var(--color-text-primary);
    transition: all var(--transition-fast);
    box-sizing: border-box;
}

#traditional-mode input[type="text"]:focus {
    outline: none;
    border-color: var(--color-border-focus);
    box-shadow: var(--input-focus-shadow);
    background: var(--color-bg-primary);
}

#traditional-mode input[type="text"]:disabled {
    background-color: var(--color-bg-secondary);
    border-color: var(--color-border-secondary);
    opacity: 0.6;
    cursor: not-allowed;
}

#traditional-mode button {
    width: 100%;
    height: var(--form-control-height);
    padding: var(--btn-padding-y) var(--btn-padding-x);
    font-size: var(--font-size-md);
    font-weight: var(--btn-font-weight);
    font-family: var(--font-family-primary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border-primary);
    background: var(--color-bg-secondary);
    color: var(--color-text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-sizing: border-box;
}

#traditional-mode button:hover {
    background: var(--color-bg-tertiary);
    border-color: var(--color-border-secondary);
    transform: translateY(-1px);
}

#traditional-mode button:active {
    transform: translateY(0);
    background: var(--color-bg-secondary);
}

#traditional-mode button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* ===== 拼写模式容器 ===== */

#spelling-mode {
    width: 100%;
    margin: var(--spacing-lg) 0;
    display: none; /* 默认隐藏，JavaScript控制显示 */
}

#spelling-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

/* ===== 拼写输入容器 ===== */

#spelling-input-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

#spelling-input {
    flex: 1;
    height: var(--form-control-height);
    padding: var(--form-control-padding-y) var(--form-control-padding-x);
    font-size: var(--font-size-lg);
    font-family: var(--font-family-monospace);
    font-weight: var(--font-weight-medium);
    border: 2px solid var(--color-border-primary);
    border-radius: var(--border-radius-md);
    background: var(--color-bg-primary);
    color: var(--color-text-primary);
    transition: all var(--transition-fast);
    text-align: center;
    letter-spacing: 1px;
}

#spelling-input:focus {
    outline: none;
    border-color: var(--color-border-focus);
    box-shadow: var(--input-focus-shadow);
}

#spelling-input:invalid {
    border-color: var(--input-invalid-border);
}

#spelling-input:valid {
    border-color: var(--input-valid-border);
}

#play-audio-spelling {
    flex-shrink: 0;
    width: var(--form-control-height);
    height: var(--form-control-height);
    padding: var(--spacing-sm);
    background: var(--color-success);
    color: var(--color-text-inverse);
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-size: var(--font-size-md);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

#play-audio-spelling:hover {
    background: var(--color-success-dark);
    transform: scale(1.05);
}

#play-audio-spelling:active {
    transform: scale(0.95);
}

/* ===== 拼写反馈区域 ===== */

#spelling-feedback {
    margin-bottom: var(--spacing-md);
}

#letter-feedback {
    font-size: var(--font-size-xl);
    font-family: var(--font-family-monospace);
    text-align: center;
    margin-bottom: var(--spacing-sm);
    min-height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    background: var(--color-bg-secondary);
}

/* 字母反馈样式 */
.letter-correct {
    color: var(--color-success);
    background: var(--feedback-correct-bg);
    border: 1px solid var(--feedback-correct-border);
    padding: var(--spacing-xs) var(--spacing-sm);
    margin: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    font-weight: var(--font-weight-bold);
    animation: letterCorrect 0.3s ease-out;
}

.letter-incorrect {
    color: var(--color-error);
    background: var(--feedback-incorrect-bg);
    border: 1px solid var(--feedback-incorrect-border);
    padding: var(--spacing-xs) var(--spacing-sm);
    margin: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    font-weight: var(--font-weight-bold);
    animation: letterIncorrect 0.3s ease-in-out;
}

.letter-pending {
    color: var(--color-text-muted);
    padding: var(--spacing-xs) var(--spacing-sm);
    margin: var(--spacing-xs);
    border-bottom: 2px solid var(--color-border-primary);
    min-width: 20px;
    text-align: center;
}

/* ===== 进度条 ===== */

#progress-bar {
    width: 100%;
    height: var(--progress-bar-height);
    background: var(--progress-bar-bg);
    border-radius: var(--progress-bar-border-radius);
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
    box-shadow: var(--shadow-inner);
}

#progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--color-success), var(--color-success-light));
    width: 0%;
    transition: width var(--transition-normal);
    border-radius: var(--progress-bar-border-radius);
    position: relative;
}

#progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%
    );
    animation: progressShine 2s infinite;
}

/* ===== 拼写提示区域 ===== */

#spelling-hints {
    text-align: center;
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    background: var(--color-bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-border-secondary);
}

#syllable-hint,
#length-hint {
    margin: var(--spacing-xs) 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

/* ===== 输入验证状态 ===== */

.input-success {
    border-color: var(--input-valid-border) !important;
    box-shadow: 0 0 0 2px var(--color-success-alpha) !important;
}

.input-error {
    border-color: var(--input-invalid-border) !important;
    box-shadow: 0 0 0 2px var(--color-error-alpha) !important;
}

.input-warning {
    border-color: var(--color-warning) !important;
    box-shadow: 0 0 0 2px var(--color-warning-alpha) !important;
}

/* ===== 动画效果 ===== */

@keyframes letterCorrect {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes letterIncorrect {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

@keyframes progressShine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes inputFocus {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.01);
    }
    100% {
        transform: scale(1);
    }
}

/* ===== 响应式设计 ===== */

@media (max-width: 767.98px) {
    #traditional-mode input[type="text"],
    #traditional-mode button {
        height: var(--form-control-height);
        font-size: var(--font-size-md);
    }
    
    #spelling-input {
        font-size: var(--font-size-md);
    }
    
    #letter-feedback {
        font-size: var(--font-size-lg);
        min-height: 32px;
    }
    
    .letter-correct,
    .letter-incorrect,
    .letter-pending {
        padding: var(--spacing-xs) var(--spacing-sm);
        margin: 2px;
    }
}

@media (max-width: 575.98px) {
    #traditional-mode input[type="text"],
    #traditional-mode button,
    #spelling-input {
        height: 48px;
        font-size: var(--font-size-md);
    }
    
    #spelling-input-container {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    #play-audio-spelling {
        width: 100%;
        height: 44px;
    }
    
    #letter-feedback {
        font-size: var(--font-size-md);
        padding: var(--spacing-sm);
    }
}

/* ===== 可访问性增强 ===== */

@media (prefers-reduced-motion: reduce) {
    #traditional-mode input[type="text"],
    #traditional-mode button,
    #spelling-input,
    #play-audio-spelling {
        transition: none;
    }
    
    .letter-correct,
    .letter-incorrect {
        animation: none;
    }
    
    #progress-fill::after {
        animation: none;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    #traditional-mode input[type="text"],
    #spelling-input {
        border-width: 2px;
    }
    
    .letter-correct {
        border-width: 2px;
        background: var(--color-success);
        color: white;
    }
    
    .letter-incorrect {
        border-width: 2px;
        background: var(--color-error);
        color: white;
    }
}

/* ===== 打印样式 ===== */

@media print {
    #traditional-mode,
    #spelling-mode {
        break-inside: avoid;
    }
    
    #traditional-mode button,
    #play-audio-spelling {
        display: none;
    }
    
    #letter-feedback {
        border: 1px solid #000;
    }
    
    .letter-correct,
    .letter-incorrect {
        background: none !important;
        border: 1px solid #000 !important;
        color: #000 !important;
    }
}