/**
 * 操作按钮组件样式
 * 作者: AI-Alpha 前端架构师
 * 创建时间: 2025-07-10 17:44
 * 目的: 提示、AI帮帮记、发音、生词库等功能按钮样式
 */

/* 导入基础变量 */
@import url('../utils/variables.css');

/* ===== 按钮容器 ===== */

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
    width: 100%;
}

/* ===== 通用按钮样式 ===== */

.action-buttons button {
    width: 100%;
    height: var(--form-control-height);
    padding: var(--btn-padding-y) var(--btn-padding-x);
    font-size: var(--font-size-sm);
    font-weight: var(--btn-font-weight);
    font-family: var(--font-family-primary);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--color-border-primary);
    background: var(--color-bg-secondary);
    color: var(--color-text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.action-buttons button:hover {
    background: var(--color-bg-tertiary);
    border-color: var(--color-border-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.action-buttons button:active {
    transform: translateY(0);
    background: var(--color-bg-secondary);
    box-shadow: var(--shadow-xs);
}

.action-buttons button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: var(--color-bg-secondary);
    border-color: var(--color-border-secondary);
}

/* ===== 特定按钮样式 ===== */

/* 提示按钮 */
#hint-button {
    background: linear-gradient(135deg, var(--color-info) 0%, var(--color-info-dark) 100%);
    color: var(--color-text-inverse);
    border-color: var(--color-info);
}

#hint-button:hover {
    background: linear-gradient(135deg, var(--color-info-dark) 0%, var(--color-info) 100%);
    border-color: var(--color-info-dark);
}

#hint-button:disabled {
    background: var(--color-bg-secondary);
    color: var(--color-text-muted);
    border-color: var(--color-border-secondary);
}

/* AI帮帮记按钮 */
#memory-help-button {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    color: var(--color-text-inverse);
    border-color: var(--color-primary);
    position: relative;
    overflow: hidden;
}

#memory-help-button:hover {
    background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
    border-color: var(--color-primary-dark);
}

#memory-help-button:disabled {
    background: var(--color-bg-secondary);
    color: var(--color-text-muted);
    border-color: var(--color-border-secondary);
}

/* AI按钮加载状态 */
#memory-help-button.loading {
    background: linear-gradient(
        90deg,
        var(--color-primary) 0%,
        var(--color-primary-light) 50%,
        var(--color-primary) 100%
    );
    background-size: 200% 100%;
    animation: aiThinking 1.5s ease-in-out infinite;
}

/* 发音按钮 */
#audio-button {
    background: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-dark) 100%);
    color: var(--color-text-inverse);
    border-color: var(--color-success);
}

#audio-button:hover {
    background: linear-gradient(135deg, var(--color-success-dark) 0%, var(--color-success) 100%);
    border-color: var(--color-success-dark);
}

#audio-button:disabled {
    background: var(--color-bg-secondary);
    color: var(--color-text-muted);
    border-color: var(--color-border-secondary);
}

/* 发音按钮播放状态 */
#audio-button.playing {
    background: linear-gradient(
        45deg,
        var(--color-success) 0%,
        var(--color-success-light) 50%,
        var(--color-success) 100%
    );
    animation: audioPlaying 0.8s ease-in-out infinite alternate;
}

/* 生词库按钮 */
button[onclick="addToVocabulary()"] {
    background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-dark) 100%);
    color: var(--color-text-primary);
    border-color: var(--color-warning);
}

button[onclick="addToVocabulary()"]:hover {
    background: linear-gradient(135deg, var(--color-warning-dark) 0%, var(--color-warning) 100%);
    border-color: var(--color-warning-dark);
}

/* ===== 按钮图标 ===== */

.action-buttons button .icon {
    font-size: var(--font-size-md);
    margin-right: var(--spacing-xs);
    flex-shrink: 0;
}

.action-buttons button .text {
    flex: 1;
    text-align: center;
}

/* ===== 按钮状态指示器 ===== */

.button-indicator {
    position: absolute;
    top: var(--spacing-xs);
    right: var(--spacing-xs);
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color-error);
    animation: pulse 1s infinite;
}

.button-indicator.success {
    background: var(--color-success);
}

.button-indicator.warning {
    background: var(--color-warning);
}

.button-indicator.info {
    background: var(--color-info);
}

/* ===== 按钮组合样式 ===== */

.button-group {
    display: flex;
    gap: var(--spacing-sm);
    width: 100%;
}

.button-group button {
    flex: 1;
    min-width: 0;
}

.button-group.horizontal {
    flex-direction: row;
}

.button-group.vertical {
    flex-direction: column;
}

/* ===== 特殊状态按钮 ===== */

.btn-success {
    background: linear-gradient(135deg, var(--color-success) 0%, var(--color-success-dark) 100%);
    color: var(--color-text-inverse);
    border-color: var(--color-success);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--color-success-dark) 0%, var(--color-success) 100%);
}

.btn-error {
    background: linear-gradient(135deg, var(--color-error) 0%, var(--color-error-dark) 100%);
    color: var(--color-text-inverse);
    border-color: var(--color-error);
}

.btn-error:hover {
    background: linear-gradient(135deg, var(--color-error-dark) 0%, var(--color-error) 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--color-warning) 0%, var(--color-warning-dark) 100%);
    color: var(--color-text-primary);
    border-color: var(--color-warning);
}

.btn-warning:hover {
    background: linear-gradient(135deg, var(--color-warning-dark) 0%, var(--color-warning) 100%);
}

/* ===== 按钮尺寸变体 ===== */

.btn-sm {
    height: 36px;
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-size-xs);
}

.btn-lg {
    height: 52px;
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

/* ===== 动画效果 ===== */

@keyframes aiThinking {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes audioPlaying {
    0% {
        transform: scale(1);
        filter: brightness(1);
    }
    100% {
        transform: scale(1.02);
        filter: brightness(1.1);
    }
}

@keyframes buttonPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: var(--shadow-sm);
    }
    50% {
        transform: scale(1.02);
        box-shadow: var(--shadow-md);
    }
}

/* ===== 响应式设计 ===== */

@media (max-width: 767.98px) {
    .action-buttons {
        gap: var(--spacing-sm);
    }
    
    .action-buttons button {
        height: var(--form-control-height);
        font-size: var(--font-size-sm);
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .button-group.horizontal {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    
    .button-group button {
        width: 100%;
    }
}

@media (max-width: 575.98px) {
    .action-buttons button {
        height: 48px;
        font-size: var(--font-size-sm);
    }
    
    .btn-sm {
        height: 40px;
    }
    
    .btn-lg {
        height: 56px;
    }
}

/* ===== 可访问性增强 ===== */

@media (prefers-reduced-motion: reduce) {
    .action-buttons button {
        transition: none;
    }
    
    .button-indicator,
    #memory-help-button.loading,
    #audio-button.playing {
        animation: none;
    }
}

/* 聚焦样式 */
.action-buttons button:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .action-buttons button {
        border-width: 2px;
        font-weight: var(--font-weight-bold);
    }
    
    #hint-button,
    #memory-help-button,
    #audio-button {
        background: var(--color-text-primary);
        color: var(--color-bg-primary);
        border-color: var(--color-text-primary);
    }
}

/* ===== 触摸设备优化 ===== */

@media (hover: none) and (pointer: coarse) {
    .action-buttons button {
        height: 48px;
        font-size: var(--font-size-md);
    }
    
    .action-buttons button:hover {
        transform: none;
    }
    
    .action-buttons button:active {
        transform: scale(0.98);
        background: var(--color-bg-tertiary);
    }
}

/* ===== 打印样式 ===== */

@media print {
    .action-buttons {
        display: none;
    }
}