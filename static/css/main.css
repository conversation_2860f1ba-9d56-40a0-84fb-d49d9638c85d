/**
 * 主要CSS入口文件
 * 作者: AI-Alpha 前端架构师
 * 创建时间: 2025-07-10 17:52
 * 目的: 整合所有模块化CSS，提供完整的样式系统
 * 
 * 导入顺序:
 * 1. 工具类和变量 (最低层)
 * 2. 基础样式和重置 (基础层)
 * 3. 布局组件 (布局层)
 * 4. UI组件 (组件层)
 * 5. 主题和覆盖 (主题层)
 */

/* ===== 第一层: 工具类和变量 ===== */
@import url('./utils/variables.css');

/* ===== 第二层: 基础样式和重置 ===== */
@import url('./base.css');

/* ===== 第三层: 布局组件 ===== */
@import url('./components/layout.css');

/* ===== 第四层: UI组件 ===== */
@import url('./components/word-display.css');
@import url('./components/input-controls.css');
@import url('./components/action-buttons.css');
@import url('./components/feedback-messages.css');
@import url('./components/sidebar.css');

/* ===== 主应用特定样式覆盖 ===== */

/**
 * 这里可以添加特定于学习页面的样式覆盖
 * 优先级高于组件样式，用于处理特殊情况
 */

/* 学习页面特定的覆盖样式 */
.learning-page {
    /* 特定于学习页面的样式 */
}

/* 针对特定浏览器的兼容性修复 */
@supports (-webkit-backdrop-filter: blur(10px)) {
    .dashboard-container {
        backdrop-filter: blur(10px);
        background-color: rgba(255, 255, 255, 0.9);
    }
}

/* iOS Safari 特定修复 */
@supports (-webkit-touch-callout: none) {
    .action-buttons button {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
    }
    
    input[type="text"],
    input[type="email"],
    input[type="password"] {
        -webkit-appearance: none;
        border-radius: var(--border-radius-md);
    }
}

/* Firefox 特定修复 */
@-moz-document url-prefix() {
    .dashboard-container {
        scrollbar-width: thin;
        scrollbar-color: var(--color-border-primary) var(--color-bg-secondary);
    }
}

/* Chrome/Edge 滚动条样式 */
.dashboard-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.dashboard-container::-webkit-scrollbar-track {
    background: var(--color-bg-secondary);
    border-radius: var(--border-radius-sm);
}

.dashboard-container::-webkit-scrollbar-thumb {
    background: var(--color-border-primary);
    border-radius: var(--border-radius-sm);
    transition: background var(--transition-fast);
}

.dashboard-container::-webkit-scrollbar-thumb:hover {
    background: var(--color-border-secondary);
}

/* ===== 深色模式支持预留 ===== */

@media (prefers-color-scheme: dark) {
    /* 
     * 深色模式变量覆盖
     * 可以在这里定义深色模式的颜色变量
     * 或者创建独立的 dark-theme.css 文件
     */
    
    /* 示例深色模式变量（未来扩展用）
    :root {
        --color-bg-primary: #1a1a1a;
        --color-bg-secondary: #2d2d2d;
        --color-text-primary: #ffffff;
        --color-text-secondary: #cccccc;
    }
    */
}

/* ===== 性能优化 ===== */

/* 启用硬件加速的元素 */
.action-buttons button,
.word-card,
.dashboard-container,
#word-image {
    will-change: transform;
    transform: translateZ(0);
}

/* 减少重绘的元素 */
#feedback,
#memory-help,
.ai-thinking {
    contain: layout style paint;
}

/* ===== 调试工具 (仅开发环境) ===== */

/* 
 * 开发时可以启用这些样式来调试布局
 * 生产环境应该移除或注释掉
 */

/*
.debug * {
    outline: 1px solid red;
}

.debug .dashboard-container {
    outline: 2px solid blue;
}

.debug .left-column {
    outline: 2px solid green;
}

.debug .right-column {
    outline: 2px solid orange;
}
*/

/* ===== 版本信息 ===== */

/*
 * CSS架构版本: v1.0.0
 * 最后更新: 2025-07-10
 * 兼容性: 
 * - Chrome 90+
 * - Firefox 88+
 * - Safari 14+
 * - Edge 90+
 * 
 * 总体积: ~45KB (压缩前)
 * 组件数量: 5个主要组件
 * 工具类数量: 50+ 工具类
 * CSS变量数量: 100+ 变量
 */