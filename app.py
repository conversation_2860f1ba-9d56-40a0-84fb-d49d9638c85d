#!/usr/bin/env python3
"""
Word Learning App - 主应用文件
重构后的模块化架构 v2.0 - 支持前端无感升级和后端扩展性
"""

import os
import logging
import uuid
from flask import Flask, request, g, session
from datetime import datetime

# 从src包导入所有核心模块
from src.config import Config, apply_env_config
from src.models import init_db
from src.routes import register_all_routes, validate_routes
from src.core import (
    setup_logging,
    register_enhanced_error_handlers,
    get_logger
)
from src.core.performance import (
    memory_cache,
    performance_monitor,
    request_tracker,
    get_system_performance
)
# 导入新的反馈API
from src.api.pattern_feedback import pattern_feedback_bp


def create_app(config_name='default'):
    """
    应用工厂函数 - 增强版
    支持多环境配置和扩展性

    Args:
        config_name: 配置环境名称 ('development', 'production', 'testing')
    """
    app = Flask(__name__)

    # 加载配置
    app.config.from_object(Config)

    # 支持环境变量覆盖配置
    _load_env_config(app)

    # 暂时禁用自定义日志设置，使用Flask默认日志
    # setup_logging(app.config)

    # 应用环境变量配置（静默）
    apply_env_config(app)

    # 初始化数据库
    init_db(app)

    # 注册中间件
    _register_middleware(app)

    # 注册路由 - 使用新的模块化路由系统
    try:
        register_all_routes(app)

        # 注册新的反馈API Blueprint
        app.register_blueprint(pattern_feedback_bp)

        print(f"✅ 路由注册成功，共注册 {len(list(app.url_map.iter_rules()))} 个路由")

        # 检查关键路由是否存在
        routes = [rule.rule for rule in app.url_map.iter_rules()]
        if '/' in routes:
            print("✅ 根路径 '/' 路由已注册")
        else:
            print("❌ 根路径 '/' 路由未找到")

    except Exception as e:
        print(f"❌ 路由注册失败: {e}")
        raise

    # 验证路由配置
    _validate_app_routes(app)

    # 注册增强的错误处理器
    register_enhanced_error_handlers(app)

    # 注册性能监控端点
    _register_performance_endpoints(app)

    # 注册简单健康检查端点（用于Docker健康检查）
    _register_simple_health_check(app)

    # 应用启动后的钩子
    _register_app_hooks(app)

    return app



def _load_env_config(app):
    """加载环境变量配置"""
    # Flask基础配置
    app.config['SECRET_KEY'] = os.environ.get('FLASK_SECRET_KEY', app.config.get('SECRET_KEY'))
    app.config['DEBUG'] = os.environ.get('FLASK_DEBUG', 'false').lower() == 'true'

    # 数据库配置
    if os.environ.get('DATABASE_URL'):
        app.config['DATABASE_PATH'] = os.environ.get('DATABASE_URL')

    # 日志配置
    app.config['LOG_LEVEL'] = os.environ.get('LOG_LEVEL', 'INFO')
    app.config['LOG_FILE'] = os.environ.get('LOG_FILE', 'app.log')


def _setup_logging(app):
    """设置日志系统"""
    log_level = getattr(logging, app.config.get('LOG_LEVEL', 'INFO'))
    log_file = app.config.get('LOG_FILE', 'app.log')

    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 文件处理器
    if log_file and not app.config.get('TESTING'):
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        file_handler.setLevel(log_level)
        app.logger.addHandler(file_handler)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(log_level)
    app.logger.addHandler(console_handler)

    app.logger.setLevel(log_level)


def _register_middleware(app):
    """注册中间件"""

    @app.before_request
    def before_request():
        """请求前处理"""
        g.start_time = datetime.now()
        g.request_id = str(uuid.uuid4())[:8]

        # 开始请求跟踪
        user_id = session.get('user_id') if 'session' in globals() else None
        request_tracker.start_request(
            g.request_id,
            request.method,
            request.path,
            user_id
        )

    @app.after_request
    def after_request(response):
        """请求后处理 - 增强客户视角响应日志"""
        # 计算请求处理时间
        if hasattr(g, 'start_time'):
            duration = datetime.now() - g.start_time
            response.headers['X-Response-Time'] = str(duration.total_seconds())
            
            # 简化的响应日志 - 静默处理
            pass

            # 结束请求跟踪
            if hasattr(g, 'request_id'):
                try:
                    # 安全地获取响应大小
                    response_size = 0
                    if hasattr(response, 'content_length') and response.content_length:
                        response_size = response.content_length

                    request_tracker.end_request(
                        g.request_id,
                        response.status_code,
                        response_size
                    )
                except Exception as e:
                    # 如果获取响应大小失败，记录错误但不影响请求处理
                    app.logger.debug(f"Failed to track response size: {e}")
                    request_tracker.end_request(
                        g.request_id,
                        response.status_code,
                        0
                    )

        # 添加安全头
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'

        return response


def _register_performance_endpoints(app):
    """注册性能监控端点"""

    @app.route('/api/system/health')
    def system_health():
        """系统健康检查"""
        from flask import jsonify

        try:
            # 检查数据库连接
            from src.models import db
            db.execute_query("SELECT 1")
            db_status = 'healthy'
        except:
            db_status = 'unhealthy'

        # 获取系统性能数据
        performance_data = get_system_performance()

        health_status = {
            'status': 'healthy' if db_status == 'healthy' else 'unhealthy',
            'timestamp': datetime.now().isoformat(),
            'database': db_status,
            'cache': {
                'status': 'healthy',
                'stats': performance_data['cache_stats']
            },
            'requests': {
                'active': performance_data['active_requests'],
                'stats': performance_data['request_stats']
            }
        }

        return jsonify(health_status)

    @app.route('/api/system/performance')
    def system_performance():
        """系统性能指标"""
        from flask import jsonify

        # 检查管理员权限
        if 'user_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        from src.models import User
        user = User.get_by_id(session['user_id'])
        if not user or user['username'] != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        performance_data = get_system_performance()
        return jsonify(performance_data)

    @app.route('/api/system/cache/clear', methods=['POST'])
    def clear_cache():
        """清空缓存"""
        from flask import jsonify

        # 检查管理员权限
        if 'user_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        from src.models import User
        user = User.get_by_id(session['user_id'])
        if not user or user['username'] != 'admin':
            return jsonify({'error': 'Admin access required'}), 403

        memory_cache.clear()
        return jsonify({'success': True, 'message': 'Cache cleared'})

    # 处理Chrome开发者工具的请求，避免404错误日志
    @app.route('/.well-known/appspecific/com.chrome.devtools.json')
    def chrome_devtools():
        return '', 204


def _register_simple_health_check(app):
    """注册简单健康检查端点（用于Docker健康检查）"""

    @app.route('/health')
    def simple_health_check():
        """简单健康检查端点 - 用于Docker健康检查"""
        try:
            # 简单的数据库连接测试
            from src.models import db
            db.execute_query("SELECT 1")
            return 'OK', 200
        except Exception as e:
            # 返回500状态码，Docker会认为不健康
            return f'UNHEALTHY: {str(e)}', 500


def _validate_app_routes(app):
    """验证应用路由配置（静默）"""
    try:
        validate_routes(app)
    except Exception as e:
        # 只在出错时记录
        app.logger.error(f"Route validation failed: {e}")


def _register_error_handlers(app):
    """注册错误处理器"""

    @app.errorhandler(404)
    def not_found_error(error):
        app.logger.warning(f"404 Error: {request.url}")
        return "页面未找到", 404

    @app.errorhandler(500)
    def internal_error(error):
        app.logger.error(f"500 Error: {error}")
        return "服务器内部错误", 500

    @app.errorhandler(Exception)
    def handle_exception(e):
        app.logger.error(f"Unhandled exception: {e}", exc_info=True)
        return "服务器错误", 500


def _register_app_hooks(app):
    """注册应用钩子"""

    @app.teardown_appcontext
    def close_db(error):
        """关闭数据库连接"""
        # 这里可以添加数据库连接清理逻辑
        pass


if __name__ == '__main__':
    # 创建应用实例
    app = create_app()

    # 获取启动配置
    host = os.environ.get('FLASK_HOST', '0.0.0.0')
    port = int(os.environ.get('FLASK_PORT', 5005))
    debug = os.environ.get('FLASK_DEBUG', 'true').lower() == 'true'

    # 简洁的启动信息 - 只显示数据库状态，让Flask处理其他信息
    print("Database connection successful, all required tables found.")

    app.run(
        host=host,
        port=port,
        debug=debug
    )