# 使用国内镜像源的 Dockerfile
FROM python:3.10-slim

# 禁用代理
ENV HTTP_PROXY=""
ENV HTTPS_PROXY=""
ENV http_proxy=""
ENV https_proxy=""
ENV NO_PROXY="*"

WORKDIR /app

# 设置环境变量
ENV FLASK_APP=app.py \
    FLASK_RUN_HOST=0.0.0.0 \
    FLASK_RUN_PORT=5003

# 复制 requirements.txt
COPY requirements.txt .

# 使用阿里云镜像源安装依赖
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com && \
    pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p instance static/exports static/cache

EXPOSE 5003

CMD ["python", "app.py"]