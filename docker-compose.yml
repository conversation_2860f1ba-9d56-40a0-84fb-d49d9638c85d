# =============================================================================
# Word Learning App v5 - Docker Compose 配置
# =============================================================================

version: '3.8'

services:
  word-learning-app:
    build: .
    container_name: word_learning_app_v5
    ports:
      - "5005:5005"
    environment:
      - FLASK_ENV=production
      - FLASK_APP=app.py
    volumes:
      # 持久化数据库
      - ./instance:/app/instance
      # 持久化完整静态资源目录
      - ./static:/app/static
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# 可选：添加 Redis 缓存（如果需要）
  # redis:
  #   image: redis:7-alpine
  #   container_name: word_learning_redis
  #   ports:
  #     - "6379:6379"
  #   restart: unless-stopped
  #   volumes:
  #     - redis_data:/data

# volumes:
#   redis_data: 