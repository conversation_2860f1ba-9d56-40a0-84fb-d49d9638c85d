"""
Pattern模型模块
提供单词Pattern推荐系统的数据模型
"""

from typing import Optional, List, Dict, Any
import sqlite3
from datetime import datetime

from .base import BaseModel, db


class WordPattern(BaseModel):
    """单词Pattern定义模型 - 支持四维度认知框架"""
    
    table_name = "word_patterns"
    required_fields = ["pattern_type", "pattern_value", "pattern_name"]
    
    # 新增：四维度分类体系枚举
    DIMENSION_CATEGORIES = {
        'orthography': '拼写与发音',
        'semantic': '词义关联', 
        'morphology': '构词与变形',
        'collocation': '搭配与用法'
    }
    
    # 新增：认知层次枚举
    COGNITIVE_LEVELS = {
        'basic': '基础感知层',
        'intermediate': '语义关联层',
        'advanced': '高级应用层'
    }
    
    # 扩展：支持新四维度分类的pattern_type
    PATTERN_TYPES = {
        # 拼写与发音维度 (Orthography & Phonetics)
        'letter_combo': '字母组合',
        'grapheme_phoneme': '字母-发音对应',
        'rhyme': '押韵模式',
        'phonetic': '发音规律',
        'silent_letter': '不发音字母',
        'syllable': '音节结构',
        
        # 词义关联维度 (Semantic Association)
        'theme': '主题分组',
        'semantic': '语义关联',
        'synonym': '同义词',
        'antonym': '反义词',
        
        # 构词与变形维度 (Morphology & Inflection)
        'prefix': '前缀',
        'suffix': '后缀',
        'root': '词根',
        'morphology': '构词法',
        'adjective_forms': '形容词变形',
        'plural_forms': '名词复数',
        'verb_forms': '动词变形',
        
        # 搭配与用法维度 (Collocation & Usage)
        'collocation': '搭配模式',
        'contextual': '语境关联',
        'usage_pattern': '用法模式',
        
        # 兼容性支持（原有类型）
        'vowel_pattern': '元音模式',
        'similar_spelling': '拼写相似'
    }
    
    @classmethod
    def create(cls, pattern_type: str, pattern_value: str, pattern_name: str,
               description: str = "", word_count: int = 0, priority_level: int = 3,
               is_active: bool = True, cognitive_level: str = 'basic',
               concept_group: str = None, dimension_category: str = None,
               educational_value: float = 0.5) -> int:
        """创建新的Pattern定义 - 支持认知层次和概念组"""
        
        # 自动推断dimension_category如果未指定
        if dimension_category is None:
            dimension_category = cls._infer_dimension_category(pattern_type)
        
        data = {
            'pattern_type': pattern_type,
            'pattern_value': pattern_value,
            'pattern_name': pattern_name,
            'description': description,
            'word_count': word_count,
            'priority_level': priority_level,
            'is_active': is_active,
            'cognitive_level': cognitive_level,
            'concept_group': concept_group,
            'dimension_category': dimension_category,
            'educational_value': educational_value
        }
        
        # 验证必需字段
        cls.validate_required_fields(data)
        
        # 验证字段类型
        cls.validate_field_types(data, {
            'pattern_type': str,
            'pattern_value': str,
            'pattern_name': str,
            'word_count': int,
            'priority_level': int,
            'is_active': bool,
            'cognitive_level': str,
            'dimension_category': str,
            'educational_value': float
        })
        
        # 验证枚举值
        if cognitive_level not in cls.COGNITIVE_LEVELS:
            raise ValueError(f"无效的认知层次: {cognitive_level}")
        
        if dimension_category not in cls.DIMENSION_CATEGORIES:
            raise ValueError(f"无效的维度分类: {dimension_category}")
        
        if pattern_type not in cls.PATTERN_TYPES:
            raise ValueError(f"无效的pattern类型: {pattern_type}")
        
        if not (0.0 <= educational_value <= 1.0):
            raise ValueError("educational_value必须在0.0-1.0范围内")
        
        # 检查是否已存在相同的pattern
        existing = cls.get_by_type_and_value(pattern_type, pattern_value)
        if existing:
            raise ValueError(f"Pattern {pattern_type}:{pattern_value} 已存在")
        
        query = """
        INSERT INTO word_patterns 
        (pattern_type, pattern_value, pattern_name, description, word_count, 
         priority_level, is_active, cognitive_level, concept_group, 
         dimension_category, educational_value, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        """
        
        return db.execute_insert(query, (
            pattern_type, pattern_value, pattern_name, description,
            word_count, priority_level, is_active, cognitive_level,
            concept_group, dimension_category, educational_value
        ))
    
    @classmethod
    def _infer_dimension_category(cls, pattern_type: str) -> str:
        """根据pattern_type自动推断维度分类"""
        orthography_types = {'letter_combo', 'grapheme_phoneme', 'rhyme', 'phonetic', 
                           'silent_letter', 'syllable', 'vowel_pattern', 'similar_spelling'}
        semantic_types = {'theme', 'semantic', 'synonym', 'antonym'}
        morphology_types = {'prefix', 'suffix', 'root', 'morphology', 'adjective_forms', 
                          'plural_forms', 'verb_forms'}
        collocation_types = {'collocation', 'contextual', 'usage_pattern'}
        
        if pattern_type in orthography_types:
            return 'orthography'
        elif pattern_type in semantic_types:
            return 'semantic'
        elif pattern_type in morphology_types:
            return 'morphology'
        elif pattern_type in collocation_types:
            return 'collocation'
        else:
            return 'semantic'  # 默认分类
    
    @classmethod
    def get_by_type_and_value(cls, pattern_type: str, pattern_value: str) -> Optional[sqlite3.Row]:
        """根据类型和值获取Pattern"""
        query = "SELECT * FROM word_patterns WHERE pattern_type = ? AND pattern_value = ?"
        results = db.execute_query(query, (pattern_type, pattern_value))
        return results[0] if results else None
    
    @classmethod
    def get_by_type(cls, pattern_type: str, active_only: bool = True) -> List[sqlite3.Row]:
        """根据类型获取Pattern列表"""
        query = "SELECT * FROM word_patterns WHERE pattern_type = ?"
        params = [pattern_type]
        
        if active_only:
            query += " AND is_active = 1"
        
        query += " ORDER BY priority_level DESC, word_count DESC"
        
        return db.execute_query(query, tuple(params))
    
    @classmethod
    def get_active_patterns(cls, limit: int = None) -> List[sqlite3.Row]:
        """获取所有活跃的Pattern"""
        query = """
        SELECT * FROM word_patterns 
        WHERE is_active = 1 
        ORDER BY priority_level DESC, word_count DESC
        """
        
        if limit:
            query += f" LIMIT {limit}"
        
        return db.execute_query(query)
    
    @classmethod
    def update_word_count(cls, pattern_id: int, word_count: int) -> bool:
        """更新Pattern的关联单词数"""
        query = """
        UPDATE word_patterns 
        SET word_count = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
        """
        affected = db.execute_update(query, (word_count, pattern_id))
        return affected > 0
    
    @classmethod
    def deactivate(cls, pattern_id: int) -> bool:
        """停用Pattern"""
        query = """
        UPDATE word_patterns 
        SET is_active = 0, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
        """
        affected = db.execute_update(query, (pattern_id,))
        return affected > 0
    
    @classmethod
    def get_by_cognitive_level(cls, cognitive_level: str, active_only: bool = True) -> List[sqlite3.Row]:
        """根据认知层次获取Pattern列表"""
        query = """
        SELECT * FROM word_patterns 
        WHERE cognitive_level = ?
        """
        params = [cognitive_level]
        
        if active_only:
            query += " AND is_active = 1"
        
        query += " ORDER BY educational_value DESC, priority_level DESC"
        
        return db.execute_query(query, tuple(params))
    
    @classmethod
    def get_by_dimension(cls, dimension_category: str, cognitive_level: str = None, 
                        active_only: bool = True) -> List[sqlite3.Row]:
        """根据维度分类获取Pattern列表"""
        query = """
        SELECT * FROM word_patterns 
        WHERE dimension_category = ?
        """
        params = [dimension_category]
        
        if cognitive_level:
            query += " AND cognitive_level = ?"
            params.append(cognitive_level)
        
        if active_only:
            query += " AND is_active = 1"
        
        query += " ORDER BY cognitive_level, educational_value DESC"
        
        return db.execute_query(query, tuple(params))
    
    @classmethod
    def get_by_concept_group(cls, concept_group: str, active_only: bool = True) -> List[sqlite3.Row]:
        """根据概念组获取Pattern列表"""
        query = """
        SELECT * FROM word_patterns 
        WHERE concept_group = ?
        """
        params = [concept_group]
        
        if active_only:
            query += " AND is_active = 1"
        
        query += " ORDER BY educational_value DESC, priority_level DESC"
        
        return db.execute_query(query, tuple(params))
    
    @classmethod
    def get_concept_groups(cls, active_only: bool = True) -> List[sqlite3.Row]:
        """获取所有概念组及其统计信息"""
        query = """
        SELECT 
            concept_group,
            COUNT(*) as pattern_count,
            AVG(educational_value) as avg_educational_value,
            MAX(priority_level) as max_priority,
            SUM(word_count) as total_word_count
        FROM word_patterns 
        WHERE concept_group IS NOT NULL
        """
        
        if active_only:
            query += " AND is_active = 1"
        
        query += """
        GROUP BY concept_group
        ORDER BY avg_educational_value DESC, total_word_count DESC
        """
        
        return db.execute_query(query)
    
    @classmethod
    def get_optimized_for_user_level(cls, avg_proficiency: float, limit: int = None) -> List[sqlite3.Row]:
        """根据用户水平获取优化的Pattern推荐列表"""
        # 根据用户熟练度确定认知层次偏好
        if avg_proficiency < 50:
            cognitive_preference = "basic"
            level_order = "1"  # basic优先
        elif avg_proficiency < 80:
            cognitive_preference = "intermediate" 
            level_order = "2"  # intermediate优先
        else:
            cognitive_preference = "advanced"
            level_order = "3"  # advanced优先
        
        query = f"""
        SELECT *, 
               CASE cognitive_level 
                   WHEN '{cognitive_preference}' THEN 1
                   WHEN 'basic' THEN 2
                   WHEN 'intermediate' THEN 3
                   WHEN 'advanced' THEN 4
               END as preference_order
        FROM word_patterns 
        WHERE is_active = 1
        ORDER BY 
            preference_order,
            educational_value DESC, 
            priority_level DESC,
            word_count DESC
        """
        
        if limit:
            query += f" LIMIT {limit}"
        
        return db.execute_query(query)


class WordPatternRelation(BaseModel):
    """单词-Pattern关联关系模型"""
    
    table_name = "word_pattern_relations"
    required_fields = ["pattern_id", "word_id"]
    
    @classmethod
    def create(cls, pattern_id: int, word_id: int, match_strength: float = 0.5,
               match_reason: str = "", is_primary: bool = False) -> int:
        """创建单词-Pattern关联"""
        
        data = {
            'pattern_id': pattern_id,
            'word_id': word_id,
            'match_strength': match_strength,
            'match_reason': match_reason,
            'is_primary': is_primary
        }
        
        # 验证必需字段
        cls.validate_required_fields(data)
        
        # 验证字段类型
        cls.validate_field_types(data, {
            'pattern_id': int,
            'word_id': int,
            'match_strength': float,
            'match_reason': str,
            'is_primary': bool
        })
        
        # 验证match_strength范围
        if not (0.0 <= match_strength <= 1.0):
            raise ValueError("match_strength必须在0.0-1.0范围内")
        
        # 检查是否已存在关联
        existing = cls.get_by_pattern_and_word(pattern_id, word_id)
        if existing:
            raise ValueError(f"Pattern {pattern_id} 与单词 {word_id} 的关联已存在")
        
        query = """
        INSERT INTO word_pattern_relations 
        (pattern_id, word_id, match_strength, match_reason, is_primary, created_at)
        VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        """
        
        return db.execute_insert(query, (
            pattern_id, word_id, match_strength, match_reason, is_primary
        ))
    
    @classmethod
    def get_by_pattern_and_word(cls, pattern_id: int, word_id: int) -> Optional[sqlite3.Row]:
        """根据Pattern和单词ID获取关联"""
        query = "SELECT * FROM word_pattern_relations WHERE pattern_id = ? AND word_id = ?"
        results = db.execute_query(query, (pattern_id, word_id))
        return results[0] if results else None
    
    @classmethod
    def get_by_word(cls, word_id: int, primary_only: bool = False) -> List[sqlite3.Row]:
        """获取单词的所有Pattern关联"""
        query = """
        SELECT wpr.*, wp.pattern_type, wp.pattern_name, wp.pattern_value
        FROM word_pattern_relations wpr
        JOIN word_patterns wp ON wpr.pattern_id = wp.id
        WHERE wpr.word_id = ? AND wp.is_active = 1
        """
        
        if primary_only:
            query += " AND wpr.is_primary = 1"
        
        query += " ORDER BY wpr.is_primary DESC, wpr.match_strength DESC"
        
        return db.execute_query(query, (word_id,))
    
    @classmethod
    def get_by_pattern(cls, pattern_id: int, limit: int = None) -> List[sqlite3.Row]:
        """获取Pattern的所有单词关联"""
        query = """
        SELECT wpr.*, w.english_word, w.chinese_meaning
        FROM word_pattern_relations wpr
        JOIN word w ON wpr.word_id = w.id
        WHERE wpr.pattern_id = ?
        ORDER BY wpr.match_strength DESC
        """
        
        if limit:
            query += f" LIMIT {limit}"
        
        return db.execute_query(query, (pattern_id,))
    
    @classmethod
    def update_match_strength(cls, pattern_id: int, word_id: int, match_strength: float) -> bool:
        """更新匹配强度"""
        if not (0.0 <= match_strength <= 1.0):
            raise ValueError("match_strength必须在0.0-1.0范围内")
        
        query = """
        UPDATE word_pattern_relations 
        SET match_strength = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE pattern_id = ? AND word_id = ?
        """
        affected = db.execute_update(query, (match_strength, pattern_id, word_id))
        return affected > 0
    
    @classmethod
    def set_primary(cls, pattern_id: int, word_id: int, is_primary: bool = True) -> bool:
        """设置为主要Pattern"""
        query = """
        UPDATE word_pattern_relations 
        SET is_primary = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE pattern_id = ? AND word_id = ?
        """
        affected = db.execute_update(query, (is_primary, pattern_id, word_id))
        return affected > 0
    
    @classmethod
    def delete_by_pattern_and_word(cls, pattern_id: int, word_id: int) -> bool:
        """删除Pattern-单词关联"""
        query = "DELETE FROM word_pattern_relations WHERE pattern_id = ? AND word_id = ?"
        affected = db.execute_update(query, (pattern_id, word_id))
        return affected > 0


class UserPatternInteraction(BaseModel):
    """用户Pattern交互记录模型"""
    
    table_name = "user_pattern_interactions"
    required_fields = ["user_id", "pattern_id", "word_id", "interaction_type"]
    
    # 交互类型枚举
    INTERACTION_TYPES = {
        'view': '查看Pattern提示',
        'click': '点击相似单词',
        'helpful': '标记为有帮助',
        'not_helpful': '标记为没帮助',
        'ignore': '忽略提示',
        'open_panel': '打开Pattern面板',
        'close_panel': '关闭Pattern面板'
    }
    
    @classmethod
    def create(cls, user_id: int, pattern_id: int, word_id: int, 
               interaction_type: str, session_id: str = None) -> int:
        """记录用户Pattern交互"""
        
        data = {
            'user_id': user_id,
            'pattern_id': pattern_id,
            'word_id': word_id,
            'interaction_type': interaction_type,
            'session_id': session_id
        }
        
        # 验证必需字段
        cls.validate_required_fields(data)
        
        # 验证字段类型
        cls.validate_field_types(data, {
            'user_id': int,
            'pattern_id': int,
            'word_id': int,
            'interaction_type': str
        })
        
        # 验证交互类型
        if interaction_type not in cls.INTERACTION_TYPES:
            raise ValueError(f"无效的交互类型: {interaction_type}")
        
        query = """
        INSERT INTO user_pattern_interactions 
        (user_id, pattern_id, word_id, interaction_type, session_id, created_at)
        VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        """
        
        return db.execute_insert(query, (
            user_id, pattern_id, word_id, interaction_type, session_id
        ))
    
    @classmethod
    def get_user_interactions(cls, user_id: int, pattern_id: int = None, 
                            limit: int = None) -> List[sqlite3.Row]:
        """获取用户的交互记录"""
        query = """
        SELECT upi.*, wp.pattern_name, w.english_word, w.chinese_meaning
        FROM user_pattern_interactions upi
        JOIN word_patterns wp ON upi.pattern_id = wp.id
        JOIN word w ON upi.word_id = w.id
        WHERE upi.user_id = ?
        """
        params = [user_id]
        
        if pattern_id:
            query += " AND upi.pattern_id = ?"
            params.append(pattern_id)
        
        query += " ORDER BY upi.created_at DESC"
        
        if limit:
            query += f" LIMIT {limit}"
        
        return db.execute_query(query, tuple(params))
    
    @classmethod
    def get_pattern_effectiveness(cls, pattern_id: int) -> Dict[str, Any]:
        """获取Pattern的效果统计"""
        query = """
        SELECT 
            interaction_type,
            COUNT(*) as count
        FROM user_pattern_interactions
        WHERE pattern_id = ?
        GROUP BY interaction_type
        """
        
        results = db.execute_query(query, (pattern_id,))
        
        # 转换为字典格式
        interactions = {row['interaction_type']: row['count'] for row in results}
        
        # 计算效果指标
        total_views = interactions.get('view', 0)
        helpful_count = interactions.get('helpful', 0)
        not_helpful_count = interactions.get('not_helpful', 0)
        
        effectiveness = 0.0
        if total_views > 0:
            effectiveness = (helpful_count - not_helpful_count) / total_views
        
        return {
            'pattern_id': pattern_id,
            'total_views': total_views,
            'helpful_count': helpful_count,
            'not_helpful_count': not_helpful_count,
            'effectiveness_score': round(effectiveness, 2),
            'interactions': interactions
        }
    
    @classmethod
    def get_user_pattern_preferences(cls, user_id: int) -> Dict[str, Any]:
        """获取用户的Pattern偏好统计"""
        query = """
        SELECT 
            wp.pattern_type,
            upi.interaction_type,
            COUNT(*) as count
        FROM user_pattern_interactions upi
        JOIN word_patterns wp ON upi.pattern_id = wp.id
        WHERE upi.user_id = ?
        GROUP BY wp.pattern_type, upi.interaction_type
        ORDER BY wp.pattern_type, count DESC
        """
        
        results = db.execute_query(query, (user_id,))
        
        # 按pattern_type组织数据
        preferences = {}
        for row in results:
            pattern_type = row['pattern_type']
            interaction_type = row['interaction_type']
            count = row['count']
            
            if pattern_type not in preferences:
                preferences[pattern_type] = {}
            
            preferences[pattern_type][interaction_type] = count
        
        # 计算每个pattern_type的偏好分数
        pattern_scores = {}
        for pattern_type, interactions in preferences.items():
            helpful = interactions.get('helpful', 0)
            not_helpful = interactions.get('not_helpful', 0)
            total = sum(interactions.values())
            
            if total > 0:
                score = (helpful - not_helpful) / total
                pattern_scores[pattern_type] = {
                    'score': round(score, 2),
                    'total_interactions': total,
                    'helpful': helpful,
                    'not_helpful': not_helpful
                }
        
        return {
            'user_id': user_id,
            'pattern_preferences': preferences,
            'pattern_scores': pattern_scores
        }
    
    @classmethod
    def cleanup_old_interactions(cls, days: int = 30) -> int:
        """清理旧的交互记录"""
        query = """
        DELETE FROM user_pattern_interactions 
        WHERE created_at < datetime('now', '-{} days')
        """.format(days)
        
        return db.execute_update(query)


# 导出的公共接口
__all__ = [
    'WordPattern',
    'WordPatternRelation', 
    'UserPatternInteraction'
]