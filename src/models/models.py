"""
数据库模型
基于data_model.md的表结构定义
"""
import sqlite3
import os
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from ..config import Config

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.db_path = Config.DATABASE_PATH
        self._ensure_db_exists()
    
    def _ensure_db_exists(self):
        """确保数据库文件存在"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def execute_query(self, query: str, params: tuple = None) -> List[sqlite3.Row]:
        """执行查询并返回结果"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.fetchall()
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新操作并返回影响的行数"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            conn.commit()
            return cursor.rowcount
    
    def execute_insert(self, query: str, params: tuple = None) -> int:
        """执行插入操作并返回新记录的ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            conn.commit()
            return cursor.lastrowid

# 全局数据库管理器实例
db = DatabaseManager()

class User:
    """用户模型"""
    
    @staticmethod
    def create(username: str, password: str) -> int:
        """创建用户"""
        query = """
        INSERT INTO user (username, password, registration_date, points, vouchers, created_at, updated_at)
        VALUES (?, ?, ?, 0, 0, ?, ?)
        """
        now = datetime.now()
        return db.execute_insert(query, (username, password, date.today(), now, now))
    
    @staticmethod
    def get_by_username(username: str) -> Optional[sqlite3.Row]:
        """通过用户名获取用户"""
        query = "SELECT * FROM user WHERE username = ?"
        results = db.execute_query(query, (username,))
        return results[0] if results else None
    
    @staticmethod
    def get_by_id(user_id: int) -> Optional[sqlite3.Row]:
        """通过ID获取用户"""
        query = "SELECT * FROM user WHERE id = ?"
        results = db.execute_query(query, (user_id,))
        return results[0] if results else None
    
    @staticmethod
    def update_points(user_id: int, points_change: int):
        """更新用户积分"""
        query = "UPDATE user SET points = points + ?, updated_at = ? WHERE id = ?"
        db.execute_update(query, (points_change, datetime.now(), user_id))
    
    @staticmethod
    def update_vouchers(user_id: int, vouchers_change: int):
        """更新用户购物券"""
        query = "UPDATE user SET vouchers = vouchers + ?, updated_at = ? WHERE id = ?"
        db.execute_update(query, (vouchers_change, datetime.now(), user_id))

class Word:
    """单词模型"""
    
    @staticmethod
    def get_all() -> List[sqlite3.Row]:
        """获取所有单词"""
        query = "SELECT * FROM word ORDER BY id"
        return db.execute_query(query)
    
    @staticmethod
    def get_by_id(word_id: int) -> Optional[sqlite3.Row]:
        """通过ID获取单词"""
        query = "SELECT * FROM word WHERE id = ?"
        results = db.execute_query(query, (word_id,))
        return results[0] if results else None
    
    @staticmethod
    def get_by_section(section: str) -> List[sqlite3.Row]:
        """按章节获取单词"""
        query = "SELECT * FROM word WHERE section = ? ORDER BY id"
        return db.execute_query(query, (section,))
    
    @staticmethod
    def search(keyword: str) -> List[sqlite3.Row]:
        """搜索单词"""
        query = """
        SELECT * FROM word 
        WHERE english_word LIKE ? OR chinese_meaning LIKE ? 
        ORDER BY id
        """
        pattern = f"%{keyword}%"
        return db.execute_query(query, (pattern, pattern))
    
    @staticmethod
    def get_total_count() -> int:
        """获取数据库中单词总数"""
        query = "SELECT COUNT(*) as count FROM word"
        results = db.execute_query(query)
        return results[0]['count'] if results else 0
    
    @staticmethod
    def create(english_word: str, chinese_meaning: str, section: str = '新增') -> int:
        """创建新单词"""
        query = """
        INSERT INTO word (english_word, chinese_meaning, section)
        VALUES (?, ?, ?)
        """
        return db.execute_insert(query, (english_word, chinese_meaning, section))

class UserWord:
    """用户单词关系模型"""
    
    @staticmethod
    def get_or_create(user_id: int, word_id: int) -> sqlite3.Row:
        """获取或创建用户单词关系"""
        # 先尝试获取
        query = "SELECT * FROM user_word WHERE user_id = ? AND word_id = ?"
        results = db.execute_query(query, (user_id, word_id))
        
        if results:
            return results[0]
        
        # 如果不存在，创建新记录
        insert_query = """
        INSERT INTO user_word (user_id, word_id, learning_count, correct_count, 
                              status, proficiency)
        VALUES (?, ?, 0, 0, 'new', 0.0)
        """
        new_id = db.execute_insert(insert_query, (user_id, word_id))
        
        # 返回新创建的记录
        return UserWord.get_by_id(new_id)
    
    @staticmethod
    def get_by_id(record_id: int) -> Optional[sqlite3.Row]:
        """通过ID获取记录"""
        query = "SELECT * FROM user_word WHERE id = ?"
        results = db.execute_query(query, (record_id,))
        return results[0] if results else None
    
    @staticmethod
    def get_by_user_and_word(user_id: int, word_id: int) -> Optional[sqlite3.Row]:
        """获取用户单词关系"""
        query = "SELECT * FROM user_word WHERE user_id = ? AND word_id = ?"
        results = db.execute_query(query, (user_id, word_id))
        return results[0] if results else None
    
    @staticmethod
    def update_learning_stats(user_id: int, word_id: int, 
                            learning_count: int, correct_count: int, 
                            proficiency: float):
        """更新学习统计数据"""
        query = """
        UPDATE user_word 
        SET learning_count = ?, correct_count = ?, proficiency = ?, 
            last_learning_date = ?
        WHERE user_id = ? AND word_id = ?
        """
        db.execute_update(query, (learning_count, correct_count, proficiency, 
                                datetime.now(), user_id, word_id))
    
    @staticmethod
    def update_status(user_id: int, word_id: int, status: str):
        """更新单词状态"""
        query = "UPDATE user_word SET status = ? WHERE user_id = ? AND word_id = ?"
        db.execute_update(query, (status, user_id, word_id))
    
    @staticmethod
    def get_new_words(user_id: int, limit: int = 7) -> List[sqlite3.Row]:
        """获取新单词用于学习计划"""
        query = """
        SELECT w.* FROM word w
        LEFT JOIN user_word uw ON w.id = uw.word_id AND uw.user_id = ?
        WHERE uw.id IS NULL OR uw.status = 'new'
        ORDER BY w.id
        LIMIT ?
        """
        return db.execute_query(query, (user_id, limit))
    
    @staticmethod
    def get_review_words(user_id: int, limit: int = 18) -> List[sqlite3.Row]:
        """获取复习单词，按熟练度倒序"""
        query = """
        SELECT w.*, uw.proficiency FROM word w
        JOIN user_word uw ON w.id = uw.word_id
        WHERE uw.user_id = ? AND uw.status IN ('review', 'attention')
          AND (uw.skip_review_until IS NULL OR uw.skip_review_until <= date('now'))
        ORDER BY uw.proficiency ASC, w.id
        LIMIT ?
        """
        return db.execute_query(query, (user_id, limit))
    
    @staticmethod
    def get_attention_words(user_id: int) -> List[sqlite3.Row]:
        """获取生词本单词"""
        query = """
        SELECT w.*, uw.* FROM word w
        JOIN user_word uw ON w.id = uw.word_id
        WHERE uw.user_id = ? AND uw.status = 'attention'
        ORDER BY uw.proficiency ASC
        """
        return db.execute_query(query, (user_id,))
    
    @staticmethod
    def get_user_words_with_proficiency(user_id: int) -> List[sqlite3.Row]:
        """获取用户所有学习过的单词及其熟练度"""
        query = """
        SELECT w.*, uw.proficiency, uw.learning_count, uw.correct_count
        FROM word w
        JOIN user_word uw ON w.id = uw.word_id
        WHERE uw.user_id = ? AND uw.learning_count > 0
        ORDER BY uw.proficiency DESC
        """
        return db.execute_query(query, (user_id,))

class WordRecord:
    """学习记录模型"""
    
    @staticmethod
    def create(user_id: int, word_id: int, duration_seconds: float,
               user_input: str, answer: str, is_correct: bool) -> int:
        """创建学习记录"""
        query = """
        INSERT INTO word_record (user_id, word_id, date, duration_seconds,
                               user_input, answer, is_correct)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        return db.execute_insert(query, (user_id, word_id, date.today(),
                                       duration_seconds, user_input, answer, is_correct))
    
    @staticmethod
    def get_user_word_stats(user_id: int, word_id: int) -> Dict[str, Any]:
        """获取用户单词的学习统计"""
        query = """
        SELECT 
            COUNT(*) as learning_count,
            SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
            AVG(duration_seconds) as avg_duration,
            MAX(date) as last_learning_date
        FROM word_record
        WHERE user_id = ? AND word_id = ?
        """
        results = db.execute_query(query, (user_id, word_id))
        if results:
            row = results[0]
            return {
                'learning_count': row['learning_count'] or 0,
                'correct_count': row['correct_count'] or 0,
                'avg_duration': row['avg_duration'] or 0.0,
                'last_learning_date': row['last_learning_date']
            }
        return {'learning_count': 0, 'correct_count': 0, 'avg_duration': 0.0, 'last_learning_date': None}
    
    @staticmethod
    def get_today_records(user_id: int) -> List[sqlite3.Row]:
        """获取今日学习记录"""
        query = """
        SELECT * FROM word_record
        WHERE user_id = ? AND date = ?
        ORDER BY id
        """
        return db.execute_query(query, (user_id, date.today()))
    
    @staticmethod
    def get_learned_words_count(user_id: int) -> int:
        """获取用户已学习的单词数量（有学习记录的单词）"""
        query = """
        SELECT COUNT(DISTINCT word_id) as count
        FROM word_record
        WHERE user_id = ?
        """
        results = db.execute_query(query, (user_id,))
        return results[0]['count'] if results else 0

class LearningPlan:
    """学习计划模型"""
    
    @staticmethod
    def create(user_id: int, word_id: int, planned_date: date,
               item_type: str, star_level: int = 3) -> int:
        """创建学习计划"""
        query = """
        INSERT INTO learning_plan (user_id, word_id, planned_date, item_type, star_level)
        VALUES (?, ?, ?, ?, ?)
        """
        return db.execute_insert(query, (user_id, word_id, planned_date, item_type, star_level))
    
    @staticmethod
    def get_daily_plan(user_id: int, plan_date: date = None) -> List[sqlite3.Row]:
        """获取每日学习计划"""
        if plan_date is None:
            plan_date = date.today()
        
        query = """
        SELECT lp.*, w.english_word, w.chinese_meaning, w.section
        FROM learning_plan lp
        JOIN word w ON lp.word_id = w.id
        WHERE lp.user_id = ? AND lp.planned_date = ?
        ORDER BY lp.item_type, lp.id
        """
        return db.execute_query(query, (user_id, plan_date))
    
    @staticmethod
    def update_star_level(user_id: int, word_id: int, plan_date: date, star_level: int):
        """更新星级"""
        query = """
        UPDATE learning_plan 
        SET star_level = ?
        WHERE user_id = ? AND word_id = ? AND planned_date = ?
        """
        db.execute_update(query, (star_level, user_id, word_id, plan_date))
    
    @staticmethod
    def check_daily_completion(user_id: int, plan_date: date = None) -> bool:
        """检查当日计划是否全部完成（所有单词达到5星）"""
        if plan_date is None:
            plan_date = date.today()
        
        query = """
        SELECT COUNT(*) as total_count,
               SUM(CASE WHEN star_level = 5 THEN 1 ELSE 0 END) as completed_count
        FROM learning_plan
        WHERE user_id = ? AND planned_date = ?
        """
        results = db.execute_query(query, (user_id, plan_date))
        if results:
            row = results[0]
            total = row['total_count'] or 0
            completed = row['completed_count'] or 0
            return total > 0 and total == completed
        return False
    
    @staticmethod
    def exists_for_date(user_id: int, plan_date: date) -> bool:
        """检查指定日期是否已有学习计划"""
        query = """
        SELECT COUNT(*) as count FROM learning_plan
        WHERE user_id = ? AND planned_date = ?
        """
        results = db.execute_query(query, (user_id, plan_date))
        return results[0]['count'] > 0 if results else False
    
    @staticmethod
    def clear_date_plan(user_id: int, plan_date: date):
        """清除指定日期的学习计划"""
        query = "DELETE FROM learning_plan WHERE user_id = ? AND planned_date = ?"
        db.execute_update(query, (user_id, plan_date))

def init_db(app):
    """初始化数据库"""
    # 这里可以添加数据库初始化逻辑
    # 由于数据库已经存在，这里主要是验证连接
    try:
        with db.get_connection() as conn:
            # 验证主要表是否存在
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            required_tables = ['user', 'word', 'user_word', 'word_record', 'learning_plan']
            
            for table in required_tables:
                if table not in tables:
                    raise Exception(f"Required table '{table}' not found in database")
            
            print("Database connection successful, all required tables found.")
    except Exception as e:
        print(f"Database initialization error: {e}")
        raise