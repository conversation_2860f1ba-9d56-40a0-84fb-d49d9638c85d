"""
Models package - 数据模型
重构后的模块化结构，保持向后兼容性
"""

# 从新的模块化文件导入
from .base import DatabaseManager, db, enhanced_db, init_db, get_db_stats
from .user import User
from .word import Word, UserWord
from .learning import WordRecord
from .planning import LearningPlan
from .pattern import WordPattern, WordPatternRelation, UserPatternInteraction

# 向后兼容性：保持原有的导入方式
# 现有代码可以继续使用 from src.models import User, Word 等

__all__ = [
    # 核心基础设施
    'DatabaseManager', 'db', 'enhanced_db', 'init_db', 'get_db_stats',

    # 模型类 - 保持原有导入接口
    'User', 'Word', 'UserWord', 'WordRecord', 'LearningPlan',
    
    # Pattern系统模型
    'WordPattern', 'WordPatternRelation', 'UserPatternInteraction'
]

# 版本信息
__version__ = '2.0.0'
__refactor_date__ = '2025-01-08'

# 模块信息
__modules__ = {
    'base': 'DatabaseManager, db, enhanced_db, init_db, get_db_stats',
    'user': 'User',
    'word': 'Word, UserWord',
    'learning': 'WordRecord',
    'planning': 'LearningPlan',
    'pattern': 'WordPattern, WordPatternRelation, UserPatternInteraction'
}
