"""
用户模型模块
提供用户管理、认证、积分等功能
"""
import sqlite3
import hashlib
from datetime import datetime, date
from typing import Optional, List, Dict, Any

from .base import BaseModel, db
from ..core.exceptions import ValidationError, AuthenticationError, DuplicateResourceError
from ..core.decorators import validate_args, validate_string_length, timing


class User(BaseModel):
    """用户模型"""
    
    table_name = 'user'
    required_fields = ['username', 'password']
    
    @staticmethod
    @timing()
    @validate_args(
        username=validate_string_length(3, 50),
        password=validate_string_length(6, 100)
    )
    def create(username: str, password: str) -> int:
        """
        创建用户
        
        Args:
            username: 用户名 (3-50字符)
            password: 密码 (6-100字符)
            
        Returns:
            int: 新用户的ID
            
        Raises:
            ValidationError: 参数验证失败
            DuplicateResourceError: 用户名已存在
        """
        # 检查用户名是否已存在
        if User.get_by_username(username):
            raise DuplicateResourceError("用户", field="username", value=username)
        
        # 密码加密（简单示例，实际应用应使用更安全的方法）
        hashed_password = User._hash_password(password)
        
        query = """
        INSERT INTO user (username, password, registration_date, points, vouchers, created_at, updated_at)
        VALUES (?, ?, ?, 0, 0, ?, ?)
        """
        now = datetime.now()
        return db.execute_insert(query, (username, hashed_password, date.today(), now, now))
    
    @staticmethod
    @timing()
    def get_by_username(username: str) -> Optional[sqlite3.Row]:
        """
        通过用户名获取用户
        
        Args:
            username: 用户名
            
        Returns:
            Optional[sqlite3.Row]: 用户记录，不存在则返回None
        """
        query = "SELECT * FROM user WHERE username = ?"
        results = db.execute_query(query, (username,))
        return results[0] if results else None
    
    @staticmethod
    @timing()
    def get_by_id(user_id: int) -> Optional[sqlite3.Row]:
        """
        通过ID获取用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[sqlite3.Row]: 用户记录，不存在则返回None
        """
        query = "SELECT * FROM user WHERE id = ?"
        results = db.execute_query(query, (user_id,))
        return results[0] if results else None
    
    @staticmethod
    @timing()
    def authenticate(username: str, password: str) -> Optional[sqlite3.Row]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Optional[sqlite3.Row]: 认证成功返回用户记录，失败返回None
            
        Raises:
            AuthenticationError: 认证失败
        """
        user = User.get_by_username(username)
        if not user:
            raise AuthenticationError("用户名或密码错误")
        
        hashed_password = User._hash_password(password)
        if user['password'] != hashed_password:
            raise AuthenticationError("用户名或密码错误")
        
        # 更新最后登录时间
        User._update_last_login(user['id'])
        
        return user
    
    @staticmethod
    @timing()
    def update_points(user_id: int, points_change: int) -> bool:
        """
        更新用户积分
        
        Args:
            user_id: 用户ID
            points_change: 积分变化量（可为负数）
            
        Returns:
            bool: 更新是否成功
            
        Raises:
            ValidationError: 用户不存在或积分不足
        """
        # 检查用户是否存在
        user = User.get_by_id(user_id)
        if not user:
            raise ValidationError("用户不存在", field="user_id", value=user_id)
        
        # 检查积分是否足够（如果是扣除操作）
        if points_change < 0 and user['points'] + points_change < 0:
            raise ValidationError(
                "积分不足",
                details={
                    'current_points': user['points'],
                    'required_points': abs(points_change),
                    'shortage': abs(user['points'] + points_change)
                }
            )
        
        query = "UPDATE user SET points = points + ?, updated_at = ? WHERE id = ?"
        affected_rows = db.execute_update(query, (points_change, datetime.now(), user_id))

        # 🔧 修复缓存问题：积分更新后清理缓存
        if affected_rows > 0:
            User._clear_user_cache(user_id)

        return affected_rows > 0

    @staticmethod
    def _clear_user_cache(user_id: int):
        """清理用户相关缓存"""
        from ..core.performance import memory_cache

        # 清理所有缓存，确保数据一致性
        memory_cache.clear()
        print(f"🧹 已清理用户 {user_id} 的相关缓存")

    @staticmethod
    @timing()
    def update_vouchers(user_id: int, vouchers_change: int) -> bool:
        """
        更新用户购物券
        
        Args:
            user_id: 用户ID
            vouchers_change: 购物券变化量（可为负数）
            
        Returns:
            bool: 更新是否成功
            
        Raises:
            ValidationError: 用户不存在或购物券不足
        """
        # 检查用户是否存在
        user = User.get_by_id(user_id)
        if not user:
            raise ValidationError("用户不存在", field="user_id", value=user_id)
        
        # 检查购物券是否足够（如果是扣除操作）
        if vouchers_change < 0 and user['vouchers'] + vouchers_change < 0:
            raise ValidationError(
                "购物券不足",
                details={
                    'current_vouchers': user['vouchers'],
                    'required_vouchers': abs(vouchers_change),
                    'shortage': abs(user['vouchers'] + vouchers_change)
                }
            )
        
        query = "UPDATE user SET vouchers = vouchers + ?, updated_at = ? WHERE id = ?"
        affected_rows = db.execute_update(query, (vouchers_change, datetime.now(), user_id))
        return affected_rows > 0
    
    @staticmethod
    def get_user_profile(user_id: int) -> Optional[Dict[str, Any]]:
        """
        获取用户完整档案信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[Dict[str, Any]]: 用户档案信息
        """
        user = User.get_by_id(user_id)
        if not user:
            return None
        
        # 获取学习统计
        learning_stats = User._get_learning_stats(user_id)
        
        return {
            'id': user['id'],
            'username': user['username'],
            'registration_date': user['registration_date'],
            'points': user['points'],
            'vouchers': user['vouchers'],
            'created_at': user['created_at'],
            'updated_at': user['updated_at'],
            'learning_stats': learning_stats
        }
    
    @staticmethod
    def update_profile(user_id: int, **kwargs) -> bool:
        """
        更新用户档案
        
        Args:
            user_id: 用户ID
            **kwargs: 要更新的字段
            
        Returns:
            bool: 更新是否成功
        """
        if not kwargs:
            return True
        
        # 过滤允许更新的字段
        allowed_fields = ['username', 'password']
        update_data = {k: v for k, v in kwargs.items() if k in allowed_fields}
        
        if not update_data:
            return True
        
        # 如果更新密码，需要加密
        if 'password' in update_data:
            update_data['password'] = User._hash_password(update_data['password'])
        
        # 构建更新查询
        set_clause = ', '.join([f"{field} = ?" for field in update_data.keys()])
        query = f"UPDATE user SET {set_clause}, updated_at = ? WHERE id = ?"
        
        params = list(update_data.values()) + [datetime.now(), user_id]
        affected_rows = db.execute_update(query, tuple(params))
        return affected_rows > 0
    
    @staticmethod
    def _hash_password(password: str) -> str:
        """密码加密（简单示例）"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    @staticmethod
    def _update_last_login(user_id: int):
        """更新最后登录时间"""
        query = "UPDATE user SET updated_at = ? WHERE id = ?"
        db.execute_update(query, (datetime.now(), user_id))
    
    @staticmethod
    def _get_learning_stats(user_id: int) -> Dict[str, Any]:
        """获取用户学习统计"""
        try:
            # 获取学习过的单词数
            query1 = """
            SELECT COUNT(DISTINCT word_id) as learned_words_count
            FROM word_record
            WHERE user_id = ?
            """
            result1 = db.execute_query(query1, (user_id,))
            learned_words = result1[0]['learned_words_count'] if result1 else 0
            
            # 获取今日学习记录数
            query2 = """
            SELECT COUNT(*) as today_records_count
            FROM word_record
            WHERE user_id = ? AND date = ?
            """
            result2 = db.execute_query(query2, (user_id, date.today()))
            today_records = result2[0]['today_records_count'] if result2 else 0
            
            # 获取总学习时长（秒）
            query3 = """
            SELECT SUM(duration_seconds) as total_duration
            FROM word_record
            WHERE user_id = ?
            """
            result3 = db.execute_query(query3, (user_id,))
            total_duration = result3[0]['total_duration'] if result3 and result3[0]['total_duration'] else 0
            
            return {
                'learned_words_count': learned_words,
                'today_records_count': today_records,
                'total_duration_seconds': total_duration,
                'total_duration_minutes': round(total_duration / 60, 1)
            }
        except Exception:
            return {
                'learned_words_count': 0,
                'today_records_count': 0,
                'total_duration_seconds': 0,
                'total_duration_minutes': 0
            }
    
    @staticmethod
    def get_all_users(limit: int = 100, offset: int = 0) -> List[sqlite3.Row]:
        """
        获取所有用户列表（分页）
        
        Args:
            limit: 每页数量
            offset: 偏移量
            
        Returns:
            List[sqlite3.Row]: 用户列表
        """
        query = """
        SELECT id, username, registration_date, points, vouchers, created_at
        FROM user
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
        """
        return db.execute_query(query, (limit, offset))
    
    @staticmethod
    def search_users(keyword: str, limit: int = 50) -> List[sqlite3.Row]:
        """
        搜索用户
        
        Args:
            keyword: 搜索关键词
            limit: 结果数量限制
            
        Returns:
            List[sqlite3.Row]: 匹配的用户列表
        """
        query = """
        SELECT id, username, registration_date, points, vouchers
        FROM user
        WHERE username LIKE ?
        ORDER BY username
        LIMIT ?
        """
        pattern = f"%{keyword}%"
        return db.execute_query(query, (pattern, limit))
