"""
学习计划模型模块
提供学习计划管理、智能调度、计划生成等功能
专注于未来学习计划的制定和执行
"""
import sqlite3
from datetime import datetime, date, timedelta
from typing import Optional, List, Dict, Any

from .base import BaseModel, db
from ..core.exceptions import ValidationError
from ..core.decorators import validate_args, timing, cache


class LearningPlan(BaseModel):
    """学习计划模型 - 专注于日级别的学习计划管理"""
    
    table_name = 'learning_plan'
    required_fields = ['user_id', 'word_id', 'planned_date']
    
    @staticmethod
    @timing()
    @validate_args(
        user_id=int,
        word_id=int,
        star_level=int
    )
    def create(user_id: int, word_id: int, planned_date: date,
               item_type: str, star_level: int = 3) -> int:
        """
        创建学习计划
        
        Args:
            user_id: 用户ID
            word_id: 单词ID
            planned_date: 计划日期
            item_type: 项目类型 ('new', 'review')
            star_level: 星级 (1-5)
            
        Returns:
            int: 新计划的ID
            
        Raises:
            ValidationError: 参数验证失败
        """
        # 验证用户和单词是否存在
        from .user import User
        from .word import Word
        
        if not User.get_by_id(user_id):
            raise ValidationError("用户不存在", field="user_id", value=user_id)
        
        if not Word.get_by_id(word_id):
            raise ValidationError("单词不存在", field="word_id", value=word_id)
        
        # 验证星级范围
        if not 1 <= star_level <= 5:
            raise ValidationError(
                "星级必须在1-5之间",
                field="star_level",
                value=star_level
            )
        
        # 验证项目类型
        valid_types = ['new', 'review']
        if item_type not in valid_types:
            raise ValidationError(
                f"无效的项目类型: {item_type}",
                field="item_type",
                details={'valid_types': valid_types}
            )
        
        query = """
        INSERT INTO learning_plan (user_id, word_id, planned_date, item_type, star_level)
        VALUES (?, ?, ?, ?, ?)
        """
        return db.execute_insert(query, (
            user_id, word_id, planned_date, item_type, star_level
        ))
    
    @staticmethod
    @timing()
    # 🔧 修复：移除缓存，因为星级更新频繁，缓存会导致数据不一致
    def get_daily_plan(user_id: int, plan_date: date = None) -> List[sqlite3.Row]:
        """
        获取每日学习计划
        
        Args:
            user_id: 用户ID
            plan_date: 计划日期，默认为今天
            
        Returns:
            List[sqlite3.Row]: 每日学习计划列表
        """
        if plan_date is None:
            plan_date = date.today()
        
        query = """
        SELECT lp.*, w.english_word, w.chinese_meaning, w.section, w.learning_requirement
        FROM learning_plan lp
        JOIN word w ON lp.word_id = w.id
        WHERE lp.user_id = ? AND lp.planned_date = ?
        ORDER BY lp.item_type, lp.star_level ASC, lp.id
        """
        return db.execute_query(query, (user_id, plan_date))
    
    @staticmethod
    @timing()
    def update_star_level(user_id: int, word_id: int, plan_date: date, star_level: int) -> bool:
        """
        更新星级 - 计划执行过程中的实时更新
        
        Args:
            user_id: 用户ID
            word_id: 单词ID
            plan_date: 计划日期
            star_level: 新的星级 (1-5)
            
        Returns:
            bool: 更新是否成功
            
        Raises:
            ValidationError: 星级范围错误
        """
        # 验证星级范围
        if not 1 <= star_level <= 5:
            raise ValidationError(
                "星级必须在1-5之间",
                field="star_level",
                value=star_level
            )
        
        query = """
        UPDATE learning_plan 
        SET star_level = ?
        WHERE user_id = ? AND word_id = ? AND planned_date = ?
        """
        affected_rows = db.execute_update(query, (
            star_level, user_id, word_id, plan_date
        ))

        # 🔧 调试：验证数据库更新
        if affected_rows > 0:
            print(f"✅ 数据库更新成功: user_id={user_id}, word_id={word_id}, star_level={star_level}, affected_rows={affected_rows}")

            # 🔧 修复缓存问题：清理相关缓存
            LearningPlan._clear_user_cache(user_id)
        else:
            print(f"❌ 数据库更新失败: user_id={user_id}, word_id={word_id}, star_level={star_level}, affected_rows={affected_rows}")

        return affected_rows > 0

    @staticmethod
    def _clear_user_cache(user_id: int):
        """清理用户相关缓存"""
        from ..core.performance import memory_cache

        # 清理可能的缓存键
        cache_patterns = [
            f"*user_{user_id}*",
            f"*daily_plan*{user_id}*",
            f"*learning_plan*{user_id}*",
            f"*user_word_stats*{user_id}*"
        ]

        # 由于我们的缓存系统比较简单，直接清空所有缓存
        memory_cache.clear()
        print(f"🧹 已清理用户 {user_id} 的相关缓存")

    @staticmethod
    @timing()
    # 🔧 修复：移除缓存，学习过程中需要实时数据
    def check_daily_completion(user_id: int, plan_date: date = None) -> Dict[str, Any]:
        """
        检查当日计划完成情况
        
        Args:
            user_id: 用户ID
            plan_date: 计划日期，默认为今天
            
        Returns:
            Dict[str, Any]: 完成情况统计
        """
        if plan_date is None:
            plan_date = date.today()
        
        query = """
        SELECT 
            COUNT(*) as total_count,
            SUM(CASE WHEN star_level = 5 THEN 1 ELSE 0 END) as completed_count,
            SUM(CASE WHEN item_type = 'new' THEN 1 ELSE 0 END) as new_count,
            SUM(CASE WHEN item_type = 'review' THEN 1 ELSE 0 END) as review_count,
            AVG(star_level) as avg_star_level
        FROM learning_plan
        WHERE user_id = ? AND planned_date = ?
        """
        results = db.execute_query(query, (user_id, plan_date))
        
        if results:
            row = results[0]
            total = row['total_count'] or 0
            completed = row['completed_count'] or 0
            
            return {
                'total_count': total,
                'completed_count': completed,
                'new_count': row['new_count'] or 0,
                'review_count': row['review_count'] or 0,
                'completion_rate': round(completed / max(total, 1), 3),
                'avg_star_level': round(row['avg_star_level'] or 0.0, 2),
                'is_completed': total > 0 and total == completed
            }
        
        return {
            'total_count': 0, 'completed_count': 0, 'new_count': 0,
            'review_count': 0, 'completion_rate': 0.0, 'avg_star_level': 0.0,
            'is_completed': False
        }
    
    @staticmethod
    @timing()
    def exists_for_date(user_id: int, plan_date: date) -> bool:
        """
        检查指定日期是否已有学习计划
        
        Args:
            user_id: 用户ID
            plan_date: 计划日期
            
        Returns:
            bool: 是否存在计划
        """
        query = """
        SELECT COUNT(*) as count FROM learning_plan
        WHERE user_id = ? AND planned_date = ?
        """
        results = db.execute_query(query, (user_id, plan_date))
        return results[0]['count'] > 0 if results else False
    
    @staticmethod
    @timing()
    def clear_date_plan(user_id: int, plan_date: date) -> int:
        """
        清除指定日期的学习计划
        
        Args:
            user_id: 用户ID
            plan_date: 计划日期
            
        Returns:
            int: 删除的记录数
        """
        query = "DELETE FROM learning_plan WHERE user_id = ? AND planned_date = ?"
        return db.execute_update(query, (user_id, plan_date))
    
    @staticmethod
    @timing()
    def get_plan_history(user_id: int, days: int = 7) -> List[Dict[str, Any]]:
        """
        获取计划历史统计

        Args:
            user_id: 用户ID
            days: 查询天数

        Returns:
            List[Dict[str, Any]]: 历史计划统计
        """
        start_date = date.today() - timedelta(days=days-1)

        query = """
        SELECT
            planned_date,
            COUNT(*) as total_count,
            SUM(CASE WHEN star_level = 5 THEN 1 ELSE 0 END) as completed_count,
            SUM(CASE WHEN item_type = 'new' THEN 1 ELSE 0 END) as new_count,
            SUM(CASE WHEN item_type = 'review' THEN 1 ELSE 0 END) as review_count,
            AVG(star_level) as avg_star_level
        FROM learning_plan
        WHERE user_id = ? AND planned_date >= ?
        GROUP BY planned_date
        ORDER BY planned_date DESC
        """
        results = db.execute_query(query, (user_id, start_date))

    @staticmethod
    @timing()
    def get_word_with_current_star(user_id: int, word_id: int, plan_date: date) -> Optional[sqlite3.Row]:
        """
        获取单词信息和当前星级 - 性能优化版本
        单次查询获取单词基本信息和学习计划中的星级

        Args:
            user_id: 用户ID
            word_id: 单词ID
            plan_date: 计划日期

        Returns:
            Optional[sqlite3.Row]: 包含单词信息和星级的记录
        """
        query = """
        SELECT w.id, w.english_word, w.chinese_meaning, w.section,
               lp.star_level, lp.item_type
        FROM word w
        LEFT JOIN learning_plan lp ON w.id = lp.word_id
                                   AND lp.user_id = ?
                                   AND lp.planned_date = ?
        WHERE w.id = ?
        """
        results = db.execute_query(query, (user_id, plan_date, word_id))
        return results[0] if results else None
        
        history = []
        for row in results:
            total = row['total_count']
            completed = row['completed_count']
            
            history.append({
                'planned_date': row['planned_date'],
                'total_count': total,
                'completed_count': completed,
                'new_count': row['new_count'],
                'review_count': row['review_count'],
                'completion_rate': round(completed / max(total, 1), 3),
                'avg_star_level': round(row['avg_star_level'] or 0.0, 2),
                'is_completed': total > 0 and total == completed
            })
        
        return history
    
    @staticmethod
    @timing()
    def generate_smart_plan(user_id: int, plan_date: date, 
                          new_words_count: int = None, review_words_count: int = None) -> List[int]:
        """
        智能生成学习计划 - 核心调度算法
        
        Args:
            user_id: 用户ID
            plan_date: 计划日期
            new_words_count: 新单词数量，默认使用配置
            review_words_count: 复习单词数量，默认使用配置
            
        Returns:
            List[int]: 创建的计划ID列表
        """
        from .word import UserWord
        from ..config.config import Config
        
        # 使用配置默认值
        if new_words_count is None:
            new_words_count = Config.NEW_WORDS_COUNT
        if review_words_count is None:
            review_words_count = Config.REVIEW_WORDS_COUNT
        
        # 清除已有计划
        LearningPlan.clear_date_plan(user_id, plan_date)
        
        plan_ids = []
        
        # 添加新单词
        new_words = UserWord.get_new_words(user_id, new_words_count)
        for word in new_words:
            plan_id = LearningPlan.create(
                user_id, word['id'], plan_date, 'new', 3
            )
            plan_ids.append(plan_id)
        
        # 添加复习单词
        review_words = UserWord.get_review_words(user_id, review_words_count)
        for word in review_words:
            # 根据熟练度设置初始星级
            proficiency = word.get('proficiency', 0.0)
            if proficiency < 0.3:
                initial_star = 1
            elif proficiency < 0.6:
                initial_star = 2
            else:
                initial_star = 3
            
            plan_id = LearningPlan.create(
                user_id, word['id'], plan_date, 'review', initial_star
            )
            plan_ids.append(plan_id)
        
        return plan_ids
