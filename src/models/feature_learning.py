"""
特征学习相关数据模型
支持用户输入特征提取、模式学习和个性化推荐
"""
import json
from datetime import datetime, date
from typing import Dict, List, Any, Optional
from ..models.models import db


class UserInputFeatures:
    """用户输入特征模型"""
    
    @staticmethod
    def create(user_id: int, word_id: int, record_id: int, 
              feature_data: Dict[str, Any], feature_version: str = "1.0") -> int:
        """创建特征记录"""
        query = """
        INSERT INTO user_input_features 
        (user_id, word_id, record_id, feature_data, feature_version)
        VALUES (?, ?, ?, ?, ?)
        """
        feature_json = json.dumps(feature_data, ensure_ascii=False)
        return db.execute_insert(query, (user_id, word_id, record_id, feature_json, feature_version))
    
    @staticmethod
    def get_user_features(user_id: int, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取用户的特征数据"""
        query = """
        SELECT uif.*, w.english_word, w.chinese_meaning
        FROM user_input_features uif
        JOIN word w ON uif.word_id = w.id
        WHERE uif.user_id = ?
        ORDER BY uif.extracted_at DESC
        LIMIT ?
        """
        results = db.execute_query(query, (user_id, limit))
        
        features = []
        for row in results:
            feature_dict = dict(row)
            feature_dict['feature_data'] = json.loads(row['feature_data'])
            features.append(feature_dict)
        
        return features
    
    @staticmethod
    def get_word_features(user_id: int, word_id: int) -> List[Dict[str, Any]]:
        """获取用户对特定单词的特征历史"""
        query = """
        SELECT * FROM user_input_features
        WHERE user_id = ? AND word_id = ?
        ORDER BY extracted_at DESC
        """
        results = db.execute_query(query, (user_id, word_id))
        
        features = []
        for row in results:
            feature_dict = dict(row)
            feature_dict['feature_data'] = json.loads(row['feature_data'])
            features.append(feature_dict)
        
        return features


class UserLearningPatterns:
    """用户学习模式模型"""
    
    @staticmethod
    def create_or_update(user_id: int, pattern_type: str, 
                        pattern_data: Dict[str, Any], 
                        confidence_score: float = 0.0,
                        sample_count: int = 1) -> int:
        """创建或更新学习模式"""
        pattern_json = json.dumps(pattern_data, ensure_ascii=False)
        
        # 先尝试更新
        update_query = """
        UPDATE user_learning_patterns 
        SET pattern_data = ?, confidence_score = ?, sample_count = ?, 
            last_updated = CURRENT_TIMESTAMP
        WHERE user_id = ? AND pattern_type = ?
        """
        rows_affected = db.execute_update(update_query, 
                                        (pattern_json, confidence_score, sample_count, user_id, pattern_type))
        
        if rows_affected == 0:
            # 如果没有更新任何行，则插入新记录
            insert_query = """
            INSERT INTO user_learning_patterns 
            (user_id, pattern_type, pattern_data, confidence_score, sample_count)
            VALUES (?, ?, ?, ?, ?)
            """
            return db.execute_insert(insert_query, 
                                   (user_id, pattern_type, pattern_json, confidence_score, sample_count))
        
        return rows_affected
    
    @staticmethod
    def get_user_patterns(user_id: int) -> List[Dict[str, Any]]:
        """获取用户的所有学习模式"""
        query = """
        SELECT * FROM user_learning_patterns
        WHERE user_id = ?
        ORDER BY confidence_score DESC, last_updated DESC
        """
        results = db.execute_query(query, (user_id,))
        
        patterns = []
        for row in results:
            pattern_dict = dict(row)
            pattern_dict['pattern_data'] = json.loads(row['pattern_data'])
            patterns.append(pattern_dict)
        
        return patterns
    
    @staticmethod
    def get_pattern(user_id: int, pattern_type: str) -> Optional[Dict[str, Any]]:
        """获取特定类型的学习模式"""
        query = """
        SELECT * FROM user_learning_patterns
        WHERE user_id = ? AND pattern_type = ?
        """
        results = db.execute_query(query, (user_id, pattern_type))
        
        if results:
            pattern_dict = dict(results[0])
            pattern_dict['pattern_data'] = json.loads(results[0]['pattern_data'])
            return pattern_dict
        
        return None
    
    @staticmethod
    def delete_pattern(user_id: int, pattern_type: str) -> bool:
        """删除特定的学习模式"""
        query = "DELETE FROM user_learning_patterns WHERE user_id = ? AND pattern_type = ?"
        rows_affected = db.execute_update(query, (user_id, pattern_type))
        return rows_affected > 0


class WordFeaturesStats:
    """单词特征统计模型（群体数据）"""
    
    @staticmethod
    def create_or_update(word_id: int, feature_type: str, 
                        feature_value: Dict[str, Any], 
                        sample_count: int = 1) -> int:
        """创建或更新单词特征统计"""
        feature_json = json.dumps(feature_value, ensure_ascii=False)
        
        # 先尝试更新
        update_query = """
        UPDATE word_features_stats 
        SET feature_value = ?, sample_count = ?, last_updated = CURRENT_TIMESTAMP
        WHERE word_id = ? AND feature_type = ?
        """
        rows_affected = db.execute_update(update_query, 
                                        (feature_json, sample_count, word_id, feature_type))
        
        if rows_affected == 0:
            # 如果没有更新任何行，则插入新记录
            insert_query = """
            INSERT INTO word_features_stats 
            (word_id, feature_type, feature_value, sample_count)
            VALUES (?, ?, ?, ?)
            """
            return db.execute_insert(insert_query, 
                                   (word_id, feature_type, feature_json, sample_count))
        
        return rows_affected
    
    @staticmethod
    def get_word_stats(word_id: int) -> List[Dict[str, Any]]:
        """获取单词的所有特征统计"""
        query = """
        SELECT wfs.*, w.english_word, w.chinese_meaning
        FROM word_features_stats wfs
        JOIN word w ON wfs.word_id = w.id
        WHERE wfs.word_id = ?
        ORDER BY wfs.last_updated DESC
        """
        results = db.execute_query(query, (word_id,))
        
        stats = []
        for row in results:
            stat_dict = dict(row)
            stat_dict['feature_value'] = json.loads(row['feature_value'])
            stats.append(stat_dict)
        
        return stats
    
    @staticmethod
    def get_feature_stat(word_id: int, feature_type: str) -> Optional[Dict[str, Any]]:
        """获取单词的特定特征统计"""
        query = """
        SELECT * FROM word_features_stats
        WHERE word_id = ? AND feature_type = ?
        """
        results = db.execute_query(query, (word_id, feature_type))
        
        if results:
            stat_dict = dict(results[0])
            stat_dict['feature_value'] = json.loads(results[0]['feature_value'])
            return stat_dict
        
        return None


class FeatureLearningConfig:
    """特征学习配置模型"""
    
    @staticmethod
    def get_config(config_key: str) -> Optional[Dict[str, Any]]:
        """获取配置值"""
        query = "SELECT * FROM feature_learning_config WHERE config_key = ?"
        results = db.execute_query(query, (config_key,))
        
        if results:
            config_dict = dict(results[0])
            config_dict['config_value'] = json.loads(results[0]['config_value'])
            return config_dict
        
        return None
    
    @staticmethod
    def set_config(config_key: str, config_value: Dict[str, Any], 
                  description: str = "") -> int:
        """设置配置值"""
        config_json = json.dumps(config_value, ensure_ascii=False)
        
        # 先尝试更新
        update_query = """
        UPDATE feature_learning_config 
        SET config_value = ?, description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE config_key = ?
        """
        rows_affected = db.execute_update(update_query, (config_json, description, config_key))
        
        if rows_affected == 0:
            # 如果没有更新任何行，则插入新记录
            insert_query = """
            INSERT INTO feature_learning_config 
            (config_key, config_value, description)
            VALUES (?, ?, ?)
            """
            return db.execute_insert(insert_query, (config_key, config_json, description))
        
        return rows_affected
    
    @staticmethod
    def get_all_configs() -> List[Dict[str, Any]]:
        """获取所有配置"""
        query = "SELECT * FROM feature_learning_config ORDER BY config_key"
        results = db.execute_query(query)
        
        configs = []
        for row in results:
            config_dict = dict(row)
            config_dict['config_value'] = json.loads(row['config_value'])
            configs.append(config_dict)
        
        return configs
    
    @staticmethod
    def is_feature_enabled(feature_name: str) -> bool:
        """检查特定特征是否启用"""
        config = FeatureLearningConfig.get_config('feature_extraction_enabled')
        if config and config['config_value'].get('enabled', False):
            # 可以添加更细粒度的特征开关检查
            return True
        return False