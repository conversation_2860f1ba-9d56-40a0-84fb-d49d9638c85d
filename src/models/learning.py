"""
学习记录模型模块
提供学习记录管理、学习数据分析、学习统计等功能
专注于历史学习数据的存储和分析
"""
import sqlite3
from datetime import datetime, date, timedelta
from typing import Optional, List, Dict, Any

from .base import BaseModel, db
from ..core.exceptions import ValidationError, ResourceNotFoundError
from ..core.decorators import validate_args, timing, cache


class WordRecord(BaseModel):
    """学习记录模型"""
    
    table_name = 'word_record'
    required_fields = ['user_id', 'word_id', 'is_correct']
    
    @staticmethod
    @timing()
    @validate_args(
        user_id=int,
        word_id=int,
        duration_seconds=float,
        is_correct=bool
    )
    def create(user_id: int, word_id: int, duration_seconds: float,
               user_input: str, answer: str, is_correct: bool) -> int:
        """
        创建学习记录
        
        Args:
            user_id: 用户ID
            word_id: 单词ID
            duration_seconds: 学习时长（秒）
            user_input: 用户输入
            answer: 正确答案
            is_correct: 是否正确
            
        Returns:
            int: 新记录的ID
            
        Raises:
            ValidationError: 参数验证失败
        """
        # 验证用户和单词是否存在
        from .user import User
        from .word import Word
        
        if not User.get_by_id(user_id):
            raise ValidationError("用户不存在", field="user_id", value=user_id)
        
        if not Word.get_by_id(word_id):
            raise ValidationError("单词不存在", field="word_id", value=word_id)
        
        # 验证时长合理性
        if duration_seconds < 0 or duration_seconds > 3600:  # 最多1小时
            raise ValidationError(
                "学习时长不合理",
                field="duration_seconds",
                value=duration_seconds,
                details={'min': 0, 'max': 3600}
            )
        
        query = """
        INSERT INTO word_record (user_id, word_id, date, duration_seconds,
                               user_input, answer, is_correct)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        return db.execute_insert(query, (
            user_id, word_id, date.today(), duration_seconds,
            user_input, answer, is_correct
        ))
    
    @staticmethod
    @timing()
    # 🔧 修复：移除缓存，学习过程中统计数据实时变化
    def get_user_word_stats(user_id: int, word_id: int) -> Dict[str, Any]:
        """
        获取用户单词的学习统计
        
        Args:
            user_id: 用户ID
            word_id: 单词ID
            
        Returns:
            Dict[str, Any]: 学习统计信息
        """
        query = """
        SELECT 
            COUNT(*) as learning_count,
            SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
            AVG(duration_seconds) as avg_duration,
            MIN(duration_seconds) as min_duration,
            MAX(duration_seconds) as max_duration,
            MAX(date) as last_learning_date,
            MIN(date) as first_learning_date
        FROM word_record
        WHERE user_id = ? AND word_id = ?
        """
        results = db.execute_query(query, (user_id, word_id))
        
        if results:
            row = results[0]
            learning_count = row['learning_count'] or 0
            correct_count = row['correct_count'] or 0
            
            return {
                'learning_count': learning_count,
                'correct_count': correct_count,
                'accuracy_rate': round(correct_count / max(learning_count, 1), 3),
                'avg_duration': round(row['avg_duration'] or 0.0, 2),
                'min_duration': row['min_duration'] or 0.0,
                'max_duration': row['max_duration'] or 0.0,
                'last_learning_date': row['last_learning_date'],
                'first_learning_date': row['first_learning_date']
            }
        
        return {
            'learning_count': 0, 'correct_count': 0, 'accuracy_rate': 0.0,
            'avg_duration': 0.0, 'min_duration': 0.0, 'max_duration': 0.0,
            'last_learning_date': None, 'first_learning_date': None
        }
    
    @staticmethod
    @timing()
    @cache(ttl=60)  # 缓存1分钟
    def get_today_records(user_id: int, target_date: date = None) -> List[sqlite3.Row]:
        """
        获取指定日期的学习记录
        
        Args:
            user_id: 用户ID
            target_date: 目标日期，默认为今天
            
        Returns:
            List[sqlite3.Row]: 指定日期的学习记录列表
        """
        if target_date is None:
            target_date = date.today()
            
        query = """
        SELECT wr.*, w.english_word, w.chinese_meaning
        FROM word_record wr
        JOIN word w ON wr.word_id = w.id
        WHERE wr.user_id = ? AND wr.date = ?
        ORDER BY wr.id DESC
        """
        return db.execute_query(query, (user_id, target_date))

    @staticmethod
    @timing()
    def get_records_by_date(user_id: int, target_date: date) -> List[sqlite3.Row]:
        """
        获取指定日期的学习记录（别名方法，兼容新API）

        Args:
            user_id: 用户ID
            target_date: 目标日期

        Returns:
            List[sqlite3.Row]: 学习记录列表
        """
        return WordRecord.get_today_records(user_id, target_date)

    @staticmethod
    @timing()
    def get_records_by_date_range(user_id: int, start_date: date, end_date: date) -> List[sqlite3.Row]:
        """
        获取日期范围内的学习记录

        Args:
            user_id: 用户ID
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            List[sqlite3.Row]: 学习记录列表
        """
        query = """
            SELECT wr.user_id, wr.word_id, wr.date, wr.duration_seconds,
                   wr.user_input, wr.answer, wr.is_correct,
                   w.english_word, w.chinese_meaning
            FROM word_record wr
            LEFT JOIN word w ON wr.word_id = w.id
            WHERE wr.user_id = ? AND wr.date BETWEEN ? AND ?
            ORDER BY wr.date DESC, wr.id DESC
        """
        return db.execute_query(query, (user_id, start_date, end_date))

    @staticmethod
    @timing()
    @cache(ttl=300)  # 缓存5分钟
    def get_learned_words_count(user_id: int) -> int:
        """
        获取用户已学习的单词数量（有学习记录的单词）
        
        Args:
            user_id: 用户ID
            
        Returns:
            int: 已学习的单词数量
        """
        query = """
        SELECT COUNT(DISTINCT word_id) as count
        FROM word_record
        WHERE user_id = ?
        """
        results = db.execute_query(query, (user_id,))
        return results[0]['count'] if results else 0
    
    @staticmethod
    @timing()
    def get_learning_history(user_id: int, days: int = 30, limit: int = 100) -> List[sqlite3.Row]:
        """
        获取学习历史记录
        
        Args:
            user_id: 用户ID
            days: 查询天数
            limit: 记录数量限制
            
        Returns:
            List[sqlite3.Row]: 学习历史记录
        """
        start_date = date.today() - timedelta(days=days)
        
        query = """
        SELECT wr.*, w.english_word, w.chinese_meaning, w.section
        FROM word_record wr
        JOIN word w ON wr.word_id = w.id
        WHERE wr.user_id = ? AND wr.date >= ?
        ORDER BY wr.created_at DESC
        LIMIT ?
        """
        return db.execute_query(query, (user_id, start_date, limit))
    
    @staticmethod
    @timing()
    def get_daily_stats(user_id: int, days: int = 7) -> List[Dict[str, Any]]:
        """
        获取每日学习统计
        
        Args:
            user_id: 用户ID
            days: 统计天数
            
        Returns:
            List[Dict[str, Any]]: 每日统计数据
        """
        start_date = date.today() - timedelta(days=days-1)
        
        query = """
        SELECT 
            date,
            COUNT(*) as total_records,
            COUNT(DISTINCT word_id) as unique_words,
            SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
            AVG(duration_seconds) as avg_duration,
            SUM(duration_seconds) as total_duration
        FROM word_record
        WHERE user_id = ? AND date >= ?
        GROUP BY date
        ORDER BY date DESC
        """
        results = db.execute_query(query, (user_id, start_date))
        
        stats = []
        for row in results:
            total_records = row['total_records']
            correct_count = row['correct_count']
            
            stats.append({
                'date': row['date'],
                'total_records': total_records,
                'unique_words': row['unique_words'],
                'correct_count': correct_count,
                'accuracy_rate': round(correct_count / max(total_records, 1), 3),
                'avg_duration': round(row['avg_duration'] or 0.0, 2),
                'total_duration': round(row['total_duration'] or 0.0, 2),
                'total_duration_minutes': round((row['total_duration'] or 0.0) / 60, 1)
            })
        
        return stats
    
    @staticmethod
    @timing()
    def get_word_difficulty_analysis(user_id: int, limit: int = 20) -> List[Dict[str, Any]]:
        """
        获取单词难度分析（错误率最高的单词）
        
        Args:
            user_id: 用户ID
            limit: 结果数量限制
            
        Returns:
            List[Dict[str, Any]]: 难词分析结果
        """
        query = """
        SELECT 
            w.id, w.english_word, w.chinese_meaning, w.section,
            COUNT(*) as total_attempts,
            SUM(CASE WHEN wr.is_correct = 1 THEN 1 ELSE 0 END) as correct_attempts,
            AVG(wr.duration_seconds) as avg_duration
        FROM word_record wr
        JOIN word w ON wr.word_id = w.id
        WHERE wr.user_id = ?
        GROUP BY w.id, w.english_word, w.chinese_meaning, w.section
        HAVING total_attempts >= 3
        ORDER BY (correct_attempts * 1.0 / total_attempts) ASC, total_attempts DESC
        LIMIT ?
        """
        results = db.execute_query(query, (user_id, limit))
        
        analysis = []
        for row in results:
            total_attempts = row['total_attempts']
            correct_attempts = row['correct_attempts']
            error_rate = 1 - (correct_attempts / total_attempts)
            
            analysis.append({
                'word_id': row['id'],
                'english_word': row['english_word'],
                'chinese_meaning': row['chinese_meaning'],
                'section': row['section'],
                'total_attempts': total_attempts,
                'correct_attempts': correct_attempts,
                'error_rate': round(error_rate, 3),
                'avg_duration': round(row['avg_duration'] or 0.0, 2),
                'difficulty_score': round(error_rate * 100 + (row['avg_duration'] or 0) / 10, 1)
            })
        
        return analysis
    
    @staticmethod
    @timing()
    def get_learning_streaks(user_id: int) -> Dict[str, Any]:
        """
        获取学习连续天数统计
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 连续学习统计
        """
        # 获取最近30天的学习日期
        query = """
        SELECT DISTINCT date
        FROM word_record
        WHERE user_id = ? AND date >= ?
        ORDER BY date DESC
        """
        start_date = date.today() - timedelta(days=30)
        results = db.execute_query(query, (user_id, start_date))
        
        if not results:
            return {'current_streak': 0, 'longest_streak': 0, 'total_days': 0}
        
        learning_dates = []
        for row in results:
            date_str = row['date']
            try:
                # 尝试不同的日期格式
                if ' ' in date_str:
                    # 包含时间的格式
                    learning_date = datetime.strptime(date_str.split()[0], '%Y-%m-%d').date()
                else:
                    # 只有日期的格式
                    learning_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                learning_dates.append(learning_date)
            except ValueError:
                # 如果解析失败，跳过这个日期
                continue
        
        # 计算当前连续天数
        current_streak = 0
        today = date.today()
        check_date = today
        
        while check_date in learning_dates:
            current_streak += 1
            check_date -= timedelta(days=1)
        
        # 如果今天没学习，检查昨天
        if current_streak == 0 and (today - timedelta(days=1)) in learning_dates:
            check_date = today - timedelta(days=1)
            while check_date in learning_dates:
                current_streak += 1
                check_date -= timedelta(days=1)
        
        # 计算最长连续天数
        longest_streak = 0
        temp_streak = 0
        
        learning_dates.sort()
        for i, learning_date in enumerate(learning_dates):
            if i == 0 or learning_date == learning_dates[i-1] + timedelta(days=1):
                temp_streak += 1
                longest_streak = max(longest_streak, temp_streak)
            else:
                temp_streak = 1
        
        return {
            'current_streak': current_streak,
            'longest_streak': longest_streak,
            'total_days': len(learning_dates)
        }

    @staticmethod
    def get_difficult_words(user_id: int, threshold: float = 0.6, limit: int = 20) -> List[Dict[str, Any]]:
        """
        获取低正确率的困难单词
        
        Args:
            user_id: 用户ID
            threshold: 正确率阈值（默认0.6，即60%）
            limit: 返回结果数量限制
            
        Returns:
            List[Dict[str, Any]]: 困难单词列表
        """
        query = """
        SELECT 
            w.id,
            w.english_word,
            w.chinese_meaning,
            w.section,
            COUNT(*) as total_count,
            SUM(CASE WHEN wr.is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
            ROUND(
                CAST(SUM(CASE WHEN wr.is_correct = 1 THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*), 
                3
            ) as correct_rate,
            AVG(wr.duration_seconds) as avg_duration,
            MAX(wr.date) as last_learned
        FROM word_record wr
        JOIN word w ON wr.word_id = w.id
        WHERE wr.user_id = ?
        GROUP BY w.id, w.english_word, w.chinese_meaning, w.section
        HAVING COUNT(*) >= 3  -- 至少学习3次
        AND correct_rate < ?  -- 正确率低于阈值
        ORDER BY correct_rate ASC, total_count DESC
        LIMIT ?
        """
        
        results = db.execute_query(query, (user_id, threshold, limit))
        
        difficult_words = []
        for row in results:
            difficult_words.append({
                'id': row['id'],
                'word': row['english_word'],
                'answer': row['chinese_meaning'],
                'section': row['section'],
                'total_count': row['total_count'],
                'correct_count': row['correct_count'],
                'correct_rate': float(row['correct_rate']),
                'avg_duration': float(row['avg_duration']) if row['avg_duration'] else 0,
                'last_learned': row['last_learned'],
                'difficulty_score': round((1 - float(row['correct_rate'])) * 100, 1)  # 困难度评分
            })
        
        return difficult_words
