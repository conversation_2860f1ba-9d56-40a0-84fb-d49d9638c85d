"""
基础模型模块
提供数据库管理和基础模型类
"""
import sqlite3
import os
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from abc import ABC, abstractmethod

from ..config import Config
from ..core.database import EnhancedDatabaseManager
from ..core.exceptions import DatabaseError, ValidationError


class DatabaseManager:
    """
    原有数据库管理器 - 保持向后兼容
    推荐使用 EnhancedDatabaseManager 获得更好的性能和功能
    """
    
    def __init__(self):
        self.db_path = Config.DATABASE_PATH
        self._ensure_db_exists()
    
    def _ensure_db_exists(self):
        """确保数据库文件存在"""
        if self.db_path != ":memory:":
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def execute_query(self, query: str, params: tuple = None) -> List[sqlite3.Row]:
        """执行查询并返回结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                return cursor.fetchall()
        except Exception as e:
            raise DatabaseError(f"查询执行失败: {query[:50]}...", operation="SELECT", cause=e)
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新操作并返回影响的行数"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            raise DatabaseError(f"更新执行失败: {query[:50]}...", operation="UPDATE", cause=e)
    
    def execute_insert(self, query: str, params: tuple = None) -> int:
        """执行插入操作并返回新记录的ID"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            raise DatabaseError(f"插入执行失败: {query[:50]}...", operation="INSERT", cause=e)


class BaseModel(ABC):
    """
    基础模型类
    提供通用的数据库操作方法和验证功能
    """
    
    # 子类需要定义的属性
    table_name: str = None
    required_fields: List[str] = []
    
    def __init__(self, db_manager: Union[DatabaseManager, EnhancedDatabaseManager] = None):
        self.db = db_manager or db
    
    @classmethod
    def validate_required_fields(cls, data: Dict[str, Any]) -> None:
        """验证必需字段"""
        if not cls.required_fields:
            return
            
        missing_fields = []
        for field in cls.required_fields:
            if field not in data or data[field] is None or data[field] == '':
                missing_fields.append(field)
        
        if missing_fields:
            raise ValidationError(
                f"缺少必需字段: {', '.join(missing_fields)}",
                details={'missing_fields': missing_fields, 'table': cls.table_name}
            )
    
    @classmethod
    def validate_field_types(cls, data: Dict[str, Any], field_types: Dict[str, type]) -> None:
        """验证字段类型"""
        for field, expected_type in field_types.items():
            if field in data and data[field] is not None:
                if not isinstance(data[field], expected_type):
                    raise ValidationError(
                        f"字段 {field} 类型错误",
                        field=field,
                        details={
                            'expected_type': expected_type.__name__,
                            'actual_type': type(data[field]).__name__,
                            'table': cls.table_name
                        }
                    )
    
    def execute_query(self, query: str, params: tuple = None) -> List[sqlite3.Row]:
        """执行查询"""
        return self.db.execute_query(query, params)
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新"""
        return self.db.execute_update(query, params)
    
    def execute_insert(self, query: str, params: tuple = None) -> int:
        """执行插入"""
        return self.db.execute_insert(query, params)
    
    @classmethod
    def get_by_id(cls, record_id: int, db_manager=None) -> Optional[sqlite3.Row]:
        """通过ID获取记录 - 通用实现"""
        if not cls.table_name:
            raise NotImplementedError("子类必须定义 table_name")
        
        db = db_manager or globals().get('db')
        query = f"SELECT * FROM {cls.table_name} WHERE id = ?"
        results = db.execute_query(query, (record_id,))
        return results[0] if results else None
    
    @classmethod
    def exists(cls, record_id: int, db_manager=None) -> bool:
        """检查记录是否存在"""
        return cls.get_by_id(record_id, db_manager) is not None
    
    @classmethod
    def count(cls, where_clause: str = "", params: tuple = None, db_manager=None) -> int:
        """获取记录数量"""
        if not cls.table_name:
            raise NotImplementedError("子类必须定义 table_name")
        
        db = db_manager or globals().get('db')
        query = f"SELECT COUNT(*) as count FROM {cls.table_name}"
        if where_clause:
            query += f" WHERE {where_clause}"
        
        results = db.execute_query(query, params)
        return results[0]['count'] if results else 0
    
    @classmethod
    def delete_by_id(cls, record_id: int, db_manager=None) -> bool:
        """通过ID删除记录"""
        if not cls.table_name:
            raise NotImplementedError("子类必须定义 table_name")
        
        db = db_manager or globals().get('db')
        query = f"DELETE FROM {cls.table_name} WHERE id = ?"
        affected_rows = db.execute_update(query, (record_id,))
        return affected_rows > 0
    
    def add_timestamps(self, data: Dict[str, Any], update: bool = False) -> Dict[str, Any]:
        """添加时间戳字段"""
        now = datetime.now()
        
        if not update and 'created_at' not in data:
            data['created_at'] = now
        
        data['updated_at'] = now
        return data


# 全局数据库管理器实例 - 保持向后兼容
db = DatabaseManager()

# 增强数据库管理器实例 - 推荐使用
enhanced_db = EnhancedDatabaseManager()


def init_db(app=None):
    """
    初始化数据库
    兼容原有接口，同时支持增强功能，包括Pattern系统表
    """
    try:
        # 使用原有数据库管理器验证连接
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 基础必需表
            required_tables = ['user', 'word', 'user_word', 'word_record', 'learning_plan']
            
            for table in required_tables:
                if table not in tables:
                    raise Exception(f"Required table '{table}' not found in database")
            
            # 检查Pattern系统表
            pattern_tables = ['word_patterns', 'word_pattern_relations', 'user_pattern_interactions']
            missing_pattern_tables = [table for table in pattern_tables if table not in tables]
            
            if missing_pattern_tables:
                print(f"Pattern tables missing: {missing_pattern_tables}, creating...")
                _create_pattern_tables(conn)
                print("Pattern tables created successfully")
            
            # 数据库连接成功，静默验证
            
    except Exception as e:
        print(f"Database initialization error: {e}")
        raise


def _create_pattern_tables(conn):
    """创建Pattern系统相关表"""
    import os
    from pathlib import Path
    
    # 获取SQL脚本路径
    project_root = Path(__file__).parent.parent.parent
    sql_script_path = project_root / "scripts" / "create_pattern_tables.sql"
    
    if not sql_script_path.exists():
        raise Exception(f"Pattern tables SQL script not found: {sql_script_path}")
    
    # 读取并执行SQL脚本
    with open(sql_script_path, 'r', encoding='utf-8') as f:
        sql_content = f.read()
    
    # 分割并执行SQL语句（处理多语句）
    cursor = conn.cursor()
    cursor.executescript(sql_content)
    conn.commit()


def get_db_stats() -> Dict[str, Any]:
    """获取数据库统计信息"""
    try:
        # 基础统计
        basic_stats = {}
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取各表记录数
            tables = ['user', 'word', 'user_word', 'word_record', 'learning_plan']
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                basic_stats[f"{table}_count"] = cursor.fetchone()['count']
        
        # 增强统计
        enhanced_stats = enhanced_db.get_stats()
        
        return {
            'basic_stats': basic_stats,
            'enhanced_stats': enhanced_stats,
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        return {
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }


# 导出的公共接口
__all__ = [
    'DatabaseManager',
    'BaseModel', 
    'db',
    'enhanced_db',
    'init_db',
    'get_db_stats'
]
