"""
性能优化模块
提供缓存、连接池、性能监控等功能
"""
import time
import threading
from typing import Dict, Any, Optional, Callable
from functools import wraps
from datetime import datetime, timedelta

from .logging import get_logger


class MemoryCache:
    """内存缓存"""
    
    def __init__(self, default_ttl: int = 300):
        self.cache = {}
        self.ttl_data = {}
        self.default_ttl = default_ttl
        self.lock = threading.RLock()
        self.logger = get_logger('cache')
    
    def get(self, key: str) -> Any:
        """获取缓存值"""
        with self.lock:
            if key not in self.cache:
                return None
            
            # 检查是否过期
            if key in self.ttl_data:
                if datetime.now() > self.ttl_data[key]:
                    self._remove(key)
                    return None
            
            return self.cache[key]
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        with self.lock:
            self.cache[key] = value
            
            if ttl is None:
                ttl = self.default_ttl
            
            if ttl > 0:
                self.ttl_data[key] = datetime.now() + timedelta(seconds=ttl)
            elif key in self.ttl_data:
                del self.ttl_data[key]
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        with self.lock:
            if key in self.cache:
                self._remove(key)
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.ttl_data.clear()
    
    def _remove(self, key: str) -> None:
        """内部删除方法"""
        if key in self.cache:
            del self.cache[key]
        if key in self.ttl_data:
            del self.ttl_data[key]
    
    def cleanup_expired(self) -> int:
        """清理过期缓存"""
        with self.lock:
            now = datetime.now()
            expired_keys = [
                key for key, expire_time in self.ttl_data.items()
                if now > expire_time
            ]
            
            for key in expired_keys:
                self._remove(key)
            
            return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            return {
                'total_keys': len(self.cache),
                'ttl_keys': len(self.ttl_data),
                'memory_usage': sum(len(str(v)) for v in self.cache.values())
            }


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.lock = threading.RLock()
        self.logger = get_logger('performance')
    
    def record_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """记录性能指标"""
        with self.lock:
            if name not in self.metrics:
                self.metrics[name] = []
            
            metric_data = {
                'value': value,
                'timestamp': datetime.now(),
                'tags': tags or {}
            }
            
            self.metrics[name].append(metric_data)
            
            # 保留最近1000个数据点
            if len(self.metrics[name]) > 1000:
                self.metrics[name] = self.metrics[name][-1000:]
    
    def get_metric_stats(self, name: str, minutes: int = 60) -> Dict[str, Any]:
        """获取指标统计"""
        with self.lock:
            if name not in self.metrics:
                return {}
            
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            recent_data = [
                m for m in self.metrics[name]
                if m['timestamp'] > cutoff_time
            ]
            
            if not recent_data:
                return {}
            
            values = [m['value'] for m in recent_data]
            
            return {
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'avg': sum(values) / len(values),
                'latest': values[-1] if values else 0,
                'time_range_minutes': minutes
            }
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有指标统计"""
        stats = {}
        for metric_name in self.metrics.keys():
            stats[metric_name] = self.get_metric_stats(metric_name)
        return stats


class RequestTracker:
    """请求跟踪器"""
    
    def __init__(self):
        self.active_requests = {}
        self.completed_requests = []
        self.lock = threading.RLock()
        self.logger = get_logger('request_tracker')
    
    def start_request(self, request_id: str, method: str, path: str, user_id: Optional[int] = None):
        """开始跟踪请求"""
        with self.lock:
            self.active_requests[request_id] = {
                'method': method,
                'path': path,
                'user_id': user_id,
                'start_time': datetime.now(),
                'status': 'active'
            }
    
    def end_request(self, request_id: str, status_code: int, response_size: int = 0):
        """结束请求跟踪"""
        with self.lock:
            if request_id not in self.active_requests:
                return
            
            request_data = self.active_requests.pop(request_id)
            end_time = datetime.now()
            duration = (end_time - request_data['start_time']).total_seconds()
            
            completed_request = {
                **request_data,
                'end_time': end_time,
                'duration': duration,
                'status_code': status_code,
                'response_size': response_size,
                'status': 'completed'
            }
            
            self.completed_requests.append(completed_request)
            
            # 保留最近1000个请求
            if len(self.completed_requests) > 1000:
                self.completed_requests = self.completed_requests[-1000:]
            
            # 记录性能指标
            performance_monitor.record_metric(
                'request_duration',
                duration,
                {
                    'method': request_data['method'],
                    'path': request_data['path'],
                    'status_code': str(status_code)
                }
            )
    
    def get_active_requests(self) -> Dict[str, Dict]:
        """获取活跃请求"""
        with self.lock:
            return self.active_requests.copy()
    
    def get_request_stats(self, minutes: int = 60) -> Dict[str, Any]:
        """获取请求统计"""
        with self.lock:
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            recent_requests = [
                r for r in self.completed_requests
                if r['end_time'] > cutoff_time
            ]
            
            if not recent_requests:
                return {
                    'total_requests': 0,
                    'avg_duration': 0,
                    'status_codes': {},
                    'slowest_requests': []
                }
            
            durations = [r['duration'] for r in recent_requests]
            status_codes = {}
            
            for request in recent_requests:
                code = request['status_code']
                status_codes[code] = status_codes.get(code, 0) + 1
            
            # 最慢的5个请求
            slowest = sorted(recent_requests, key=lambda x: x['duration'], reverse=True)[:5]
            
            return {
                'total_requests': len(recent_requests),
                'avg_duration': sum(durations) / len(durations),
                'max_duration': max(durations),
                'min_duration': min(durations),
                'status_codes': status_codes,
                'slowest_requests': [
                    {
                        'method': r['method'],
                        'path': r['path'],
                        'duration': r['duration'],
                        'status_code': r['status_code']
                    }
                    for r in slowest
                ]
            }


# 全局实例
memory_cache = MemoryCache()
performance_monitor = PerformanceMonitor()
request_tracker = RequestTracker()


def cache_result(ttl: int = 300, key_func: Optional[Callable] = None):
    """缓存结果装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__module__}.{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            cached_result = memory_cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            start_time = time.time()
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            
            # 记录性能指标
            performance_monitor.record_metric(
                f"function_duration.{func.__name__}",
                duration
            )
            
            # 缓存结果
            memory_cache.set(cache_key, result, ttl)
            
            return result
        
        return wrapper
    return decorator


def monitor_performance(metric_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            name = metric_name or f"{func.__module__}.{func.__name__}"
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                performance_monitor.record_metric(
                    f"function_duration.{name}",
                    duration,
                    {'status': 'success'}
                )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                
                performance_monitor.record_metric(
                    f"function_duration.{name}",
                    duration,
                    {'status': 'error', 'error_type': type(e).__name__}
                )
                
                raise
        
        return wrapper
    return decorator


def get_system_performance() -> Dict[str, Any]:
    """获取系统性能概览"""
    return {
        'cache_stats': memory_cache.get_stats(),
        'performance_metrics': performance_monitor.get_all_stats(),
        'request_stats': request_tracker.get_request_stats(),
        'active_requests': len(request_tracker.get_active_requests())
    }
