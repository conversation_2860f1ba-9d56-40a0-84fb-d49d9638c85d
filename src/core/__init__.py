"""
Core package - 核心基础设施
提供数据库管理、异常处理、装饰器等基础功能
"""

from .database import (
    EnhancedDatabaseManager,
    DatabaseConnectionPool,
    CompatibilityDatabaseManager,
    enhanced_db,
    db,
    transactional
)

from .exceptions import (
    BaseAppException,
    BusinessLogicError,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    DatabaseError,
    ExternalServiceError,
    ResourceNotFoundError,
    DuplicateResourceError,
    RateLimitError,
    handle_exceptions,
    validate_required_fields,
    validate_field_type,
    validate_field_length,
    register_error_handlers,
    raise_if_none,
    raise_if_empty
)

from .decorators import (
    timing,
    retry,
    cache,
    validate_args,
    require_auth,
    log_execution,
    rate_limit,
    api_endpoint,
    clear_cache,
    get_cache_stats,
    validate_string_length,
    validate_positive_integer,
    validate_email
)

# 日志系统
from .logging import (
    setup_logging,
    get_logger,
    LoggerManager,
    StructuredLogger,
    logger_manager,
    PerformanceLogger
)

# 错误处理
from .error_handlers import (
    register_error_handlers as register_enhanced_error_handlers,
    <PERSON>rrorHandler,
    APIErrorHandler,
    WebErrorHandler,
    ErrorReporter,
    error_reporter
)

__all__ = [
    # Database components
    'EnhancedDatabaseManager',
    'DatabaseConnectionPool',
    'CompatibilityDatabaseManager',
    'enhanced_db',
    'db',
    'transactional',

    # Exception classes
    'BaseAppException',
    'BusinessLogicError',
    'ValidationError',
    'AuthenticationError',
    'AuthorizationError',
    'DatabaseError',
    'ExternalServiceError',
    'ResourceNotFoundError',
    'DuplicateResourceError',
    'RateLimitError',

    # Exception utilities
    'handle_exceptions',
    'validate_required_fields',
    'validate_field_type',
    'validate_field_length',
    'register_error_handlers',
    'raise_if_none',
    'raise_if_empty',

    # Decorators
    'timing',
    'retry',
    'cache',
    'validate_args',
    'require_auth',
    'log_execution',
    'rate_limit',
    'api_endpoint',
    'clear_cache',
    'get_cache_stats',
    'validate_string_length',
    'validate_positive_integer',
    'validate_email',

    # 日志系统
    'setup_logging',
    'get_logger',
    'LoggerManager',
    'StructuredLogger',
    'logger_manager',
    'PerformanceLogger',

    # 错误处理
    'register_enhanced_error_handlers',
    'ErrorHandler',
    'APIErrorHandler',
    'WebErrorHandler',
    'ErrorReporter',
    'error_reporter'
]
