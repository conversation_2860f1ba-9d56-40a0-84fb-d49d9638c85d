"""
日志系统
提供结构化日志、性能监控和错误跟踪功能
"""
import os
import sys
import json
import logging
import logging.handlers
from datetime import datetime
from typing import Dict, Any, Optional
from functools import wraps


class StructuredFormatter(logging.Formatter):
    """结构化日志格式器"""
    
    def format(self, record):
        """格式化日志记录"""
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # 添加自定义字段
        if hasattr(record, 'user_id'):
            log_data['user_id'] = record.user_id
        
        if hasattr(record, 'request_id'):
            log_data['request_id'] = record.request_id
        
        if hasattr(record, 'duration'):
            log_data['duration'] = record.duration
        
        if hasattr(record, 'extra_data'):
            log_data.update(record.extra_data)
        
        return json.dumps(log_data, ensure_ascii=False)


class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self, logger_name='performance'):
        self.logger = logging.getLogger(logger_name)
    
    def log_request(self, method: str, path: str, duration: float, 
                   status_code: int, user_id: Optional[int] = None):
        """记录请求性能"""
        extra_data = {
            'method': method,
            'path': path,
            'status_code': status_code,
            'duration': duration
        }
        
        if user_id:
            extra_data['user_id'] = user_id
        
        self.logger.info(
            f"{method} {path} - {status_code} - {duration:.3f}s",
            extra={'extra_data': extra_data}
        )
    
    def log_database_query(self, query: str, duration: float, 
                          rows_affected: Optional[int] = None):
        """记录数据库查询性能"""
        extra_data = {
            'query_type': 'database',
            'duration': duration
        }
        
        if rows_affected is not None:
            extra_data['rows_affected'] = rows_affected
        
        # 截断长查询
        query_preview = query[:100] + '...' if len(query) > 100 else query
        
        self.logger.info(
            f"DB Query: {query_preview} - {duration:.3f}s",
            extra={'extra_data': extra_data}
        )
    
    def log_service_call(self, service: str, method: str, duration: float,
                        success: bool = True):
        """记录服务调用性能"""
        extra_data = {
            'service': service,
            'method': method,
            'duration': duration,
            'success': success
        }
        
        level = logging.INFO if success else logging.WARNING
        status = 'SUCCESS' if success else 'FAILED'
        
        self.logger.log(
            level,
            f"Service Call: {service}.{method} - {status} - {duration:.3f}s",
            extra={'extra_data': extra_data}
        )


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        self.loggers = {}
        self.handlers = {}
        self.configured = False
    
    def setup(self, app_config):
        """设置日志系统"""
        if self.configured:
            return
        
        # 获取配置
        log_level = getattr(app_config, 'LOG_LEVEL', 'INFO')
        log_file = getattr(app_config, 'LOG_FILE', None)
        debug = getattr(app_config, 'DEBUG', False)
        
        # 设置根日志级别
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level))
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 控制台处理器 - 始终使用简单格式
        console_handler = logging.StreamHandler(sys.stdout)
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        console_handler.setFormatter(console_formatter)
        console_handler.setLevel(getattr(logging, log_level))
        root_logger.addHandler(console_handler)
        self.handlers['console'] = console_handler
        
        # 文件处理器
        if log_file:
            # 确保日志目录存在
            log_dir = os.path.dirname(log_file)
            if log_dir:
                os.makedirs(log_dir, exist_ok=True)
            
            # 使用轮转文件处理器
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setFormatter(StructuredFormatter())
            file_handler.setLevel(getattr(logging, log_level))
            root_logger.addHandler(file_handler)
            self.handlers['file'] = file_handler
        
        # 错误文件处理器
        if log_file:
            error_file = log_file.replace('.log', '_error.log')
            error_handler = logging.handlers.RotatingFileHandler(
                error_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            error_handler.setFormatter(StructuredFormatter())
            error_handler.setLevel(logging.ERROR)
            root_logger.addHandler(error_handler)
            self.handlers['error'] = error_handler
        
        # 设置第三方库日志级别
        logging.getLogger('werkzeug').setLevel(logging.WARNING)
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        
        self.configured = True
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取日志记录器"""
        if name not in self.loggers:
            self.loggers[name] = logging.getLogger(name)
        return self.loggers[name]
    
    def add_request_context(self, logger: logging.Logger, 
                           request_id: str, user_id: Optional[int] = None):
        """为日志记录器添加请求上下文"""
        # 创建适配器来添加上下文信息
        class ContextAdapter(logging.LoggerAdapter):
            def process(self, msg, kwargs):
                extra = kwargs.get('extra', {})
                extra['request_id'] = request_id
                if user_id:
                    extra['user_id'] = user_id
                kwargs['extra'] = extra
                return msg, kwargs
        
        return ContextAdapter(logger, {})


class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.performance = PerformanceLogger(f"{name}.performance")
    
    def info(self, message: str, **kwargs):
        """记录信息日志"""
        self.logger.info(message, extra={'extra_data': kwargs})
    
    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        self.logger.warning(message, extra={'extra_data': kwargs})
    
    def error(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """记录错误日志"""
        if exception:
            self.logger.error(message, exc_info=exception, extra={'extra_data': kwargs})
        else:
            self.logger.error(message, extra={'extra_data': kwargs})
    
    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        self.logger.debug(message, extra={'extra_data': kwargs})
    
    def log_user_action(self, user_id: int, action: str, details: Dict[str, Any] = None):
        """记录用户行为"""
        extra_data = {
            'user_id': user_id,
            'action': action,
            'action_type': 'user_action'
        }
        
        if details:
            extra_data.update(details)
        
        self.logger.info(f"User Action: {action}", extra={'extra_data': extra_data})
    
    def log_system_event(self, event: str, details: Dict[str, Any] = None):
        """记录系统事件"""
        extra_data = {
            'event': event,
            'event_type': 'system_event'
        }
        
        if details:
            extra_data.update(details)
        
        self.logger.info(f"System Event: {event}", extra={'extra_data': extra_data})


# 全局日志管理器
logger_manager = LoggerManager()


def setup_logging(app_config):
    """设置日志系统的便捷函数"""
    logger_manager.setup(app_config)


def get_logger(name: str) -> StructuredLogger:
    """获取结构化日志记录器的便捷函数"""
    return StructuredLogger(name)


def log_performance(func_name: str = None):
    """性能日志装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = datetime.now()
            function_name = func_name or f"{func.__module__}.{func.__name__}"
            
            try:
                result = func(*args, **kwargs)
                duration = (datetime.now() - start_time).total_seconds()
                
                performance_logger = PerformanceLogger()
                performance_logger.log_service_call(
                    service=func.__module__,
                    method=func.__name__,
                    duration=duration,
                    success=True
                )
                
                return result
            except Exception as e:
                duration = (datetime.now() - start_time).total_seconds()
                
                performance_logger = PerformanceLogger()
                performance_logger.log_service_call(
                    service=func.__module__,
                    method=func.__name__,
                    duration=duration,
                    success=False
                )
                
                raise
        
        return wrapper
    return decorator
