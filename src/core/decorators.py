"""
通用装饰器库
提供常用的装饰器功能，包括性能监控、重试机制、缓存、参数验证等
"""
import time
import logging
import hashlib
import json
from functools import wraps, lru_cache
from typing import Any, Callable, Dict, Optional, Union, List
from datetime import datetime, timedelta
from flask import session, request, jsonify

from .exceptions import AuthenticationError, ValidationError, RateLimitError

logger = logging.getLogger(__name__)

# 全局缓存存储
_cache_storage = {}
_rate_limit_storage = {}

def timing(log_slow_queries: bool = True, slow_threshold: float = 1.0):
    """
    函数执行时间统计装饰器
    
    Args:
        log_slow_queries: 是否记录慢查询
        slow_threshold: 慢查询阈值（秒）
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 记录执行时间
                logger.debug(f"{func.__name__} executed in {execution_time:.4f}s")
                
                # 记录慢查询
                if log_slow_queries and execution_time > slow_threshold:
                    logger.warning(f"Slow execution detected: {func.__name__} took {execution_time:.4f}s")
                
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"{func.__name__} failed after {execution_time:.4f}s: {e}")
                raise
        
        return wrapper
    return decorator

def retry(max_attempts: int = 3, delay: float = 1.0, backoff: float = 2.0, exceptions: tuple = (Exception,)):
    """
    失败重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 延迟倍数
        exceptions: 需要重试的异常类型
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            current_delay = delay
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_attempts - 1:
                        logger.error(f"{func.__name__} failed after {max_attempts} attempts: {e}")
                        raise
                    
                    logger.warning(f"{func.__name__} attempt {attempt + 1} failed: {e}, retrying in {current_delay}s")
                    time.sleep(current_delay)
                    current_delay *= backoff
            
            raise last_exception
        
        return wrapper
    return decorator

def cache(ttl: int = 300, max_size: int = 128, key_func: Callable = None):
    """
    简单缓存装饰器
    
    Args:
        ttl: 缓存生存时间（秒）
        max_size: 最大缓存条目数
        key_func: 自定义键生成函数
    """
    def decorator(func: Callable) -> Callable:
        cache_key_prefix = f"{func.__module__}.{func.__name__}"
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = f"{cache_key_prefix}:{key_func(*args, **kwargs)}"
            else:
                # 默认键生成
                key_data = {
                    'args': args,
                    'kwargs': kwargs
                }
                key_str = json.dumps(key_data, sort_keys=True, default=str)
                cache_key = f"{cache_key_prefix}:{hashlib.md5(key_str.encode()).hexdigest()}"
            
            # 检查缓存
            now = time.time()
            if cache_key in _cache_storage:
                cached_data = _cache_storage[cache_key]
                if now - cached_data['timestamp'] < ttl:
                    logger.debug(f"Cache hit for {func.__name__}")
                    return cached_data['value']
                else:
                    # 缓存过期
                    del _cache_storage[cache_key]
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 存储到缓存
            if len(_cache_storage) >= max_size:
                # 清理最旧的缓存项
                oldest_key = min(_cache_storage.keys(), key=lambda k: _cache_storage[k]['timestamp'])
                del _cache_storage[oldest_key]
            
            _cache_storage[cache_key] = {
                'value': result,
                'timestamp': now
            }
            
            logger.debug(f"Cache miss for {func.__name__}, result cached")
            return result
        
        return wrapper
    return decorator

def validate_args(**validators):
    """
    参数验证装饰器
    
    Args:
        **validators: 参数验证器字典，格式为 {参数名: 验证函数}
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取函数参数名
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # 验证参数
            for param_name, validator in validators.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    try:
                        if callable(validator):
                            validator(value)
                        elif isinstance(validator, type):
                            if not isinstance(value, validator):
                                raise ValidationError(f"参数 {param_name} 类型错误", field=param_name)
                    except ValidationError:
                        raise
                    except Exception as e:
                        raise ValidationError(f"参数 {param_name} 验证失败: {e}", field=param_name)
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator

def require_auth(check_permissions: List[str] = None):
    """
    认证要求装饰器
    
    Args:
        check_permissions: 需要检查的权限列表
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 检查用户是否已登录
            if 'user_id' not in session:
                raise AuthenticationError("用户未登录")
            
            user_id = session['user_id']
            
            # 验证用户是否存在
            from ..models import User
            user = User.get_by_id(user_id)
            if not user:
                session.clear()  # 清除无效session
                raise AuthenticationError("用户不存在")
            
            # 检查权限（如果需要）
            if check_permissions:
                # 这里可以扩展权限检查逻辑
                # 目前简化处理，所有登录用户都有基本权限
                pass
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator

def log_execution(log_args: bool = False, log_result: bool = False, log_level: str = 'INFO'):
    """
    执行日志记录装饰器
    
    Args:
        log_args: 是否记录参数
        log_result: 是否记录返回值
        log_level: 日志级别
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            log_func = getattr(logger, log_level.lower())
            
            # 记录函数调用
            log_msg = f"Calling {func.__name__}"
            if log_args:
                log_msg += f" with args={args}, kwargs={kwargs}"
            log_func(log_msg)
            
            try:
                result = func(*args, **kwargs)
                
                # 记录成功结果
                success_msg = f"{func.__name__} completed successfully"
                if log_result:
                    success_msg += f" with result={result}"
                log_func(success_msg)
                
                return result
            except Exception as e:
                # 记录异常
                logger.error(f"{func.__name__} failed with exception: {e}")
                raise
        
        return wrapper
    return decorator

def rate_limit(max_requests: int = 100, window_seconds: int = 3600, key_func: Callable = None):
    """
    频率限制装饰器
    
    Args:
        max_requests: 时间窗口内最大请求数
        window_seconds: 时间窗口大小（秒）
        key_func: 自定义键生成函数（用于区分不同用户/IP）
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成限制键
            if key_func:
                limit_key = key_func(*args, **kwargs)
            else:
                # 默认使用用户ID或IP地址
                if 'user_id' in session:
                    limit_key = f"user:{session['user_id']}"
                else:
                    limit_key = f"ip:{request.remote_addr}"
            
            limit_key = f"{func.__name__}:{limit_key}"
            
            now = time.time()
            window_start = now - window_seconds
            
            # 清理过期记录
            if limit_key in _rate_limit_storage:
                _rate_limit_storage[limit_key] = [
                    timestamp for timestamp in _rate_limit_storage[limit_key]
                    if timestamp > window_start
                ]
            else:
                _rate_limit_storage[limit_key] = []
            
            # 检查是否超过限制
            if len(_rate_limit_storage[limit_key]) >= max_requests:
                raise RateLimitError(
                    message=f"请求过于频繁，请稍后再试",
                    limit=max_requests,
                    window=window_seconds
                )
            
            # 记录当前请求
            _rate_limit_storage[limit_key].append(now)
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator

# 组合装饰器
def api_endpoint(
    require_login: bool = True,
    log_execution: bool = True,
    rate_limit_requests: int = None,
    cache_ttl: int = None
):
    """
    API端点组合装饰器
    
    Args:
        require_login: 是否需要登录
        log_execution: 是否记录执行日志
        rate_limit_requests: 频率限制（每小时请求数）
        cache_ttl: 缓存时间（秒）
    """
    def decorator(func: Callable) -> Callable:
        # 应用装饰器（注意顺序）
        decorated_func = func
        
        if cache_ttl:
            decorated_func = cache(ttl=cache_ttl)(decorated_func)
        
        if rate_limit_requests:
            decorated_func = rate_limit(max_requests=rate_limit_requests)(decorated_func)
        
        if require_login:
            decorated_func = require_auth()(decorated_func)
        
        if log_execution:
            decorated_func = globals()['log_execution']()(decorated_func)
        
        return decorated_func

    return decorator


# 缓存清理工具
def clear_cache(pattern: str = None):
    """
    清理缓存

    Args:
        pattern: 缓存键模式，如果为None则清理所有缓存
    """
    global _cache_storage

    if pattern is None:
        _cache_storage.clear()
        logger.info("All cache cleared")
    else:
        keys_to_remove = [
            key for key in _cache_storage.keys() if pattern in key
        ]
        for key in keys_to_remove:
            del _cache_storage[key]
        logger.info(
            f"Cache cleared for pattern: {pattern}, "
            f"removed {len(keys_to_remove)} items"
        )


def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计信息"""
    now = time.time()
    active_items = 0
    expired_items = 0

    for cache_data in _cache_storage.values():
        if now - cache_data['timestamp'] < 300:  # 假设默认TTL为300秒
            active_items += 1
        else:
            expired_items += 1

    return {
        'total_items': len(_cache_storage),
        'active_items': active_items,
        'expired_items': expired_items,
        'memory_usage_estimate': len(str(_cache_storage))
    }

# 验证器函数
def validate_string_length(
    min_length: int = 0,
    max_length: Optional[int] = None
):
    """字符串长度验证器"""
    def validator(value):
        if not isinstance(value, str):
            raise ValidationError("值必须是字符串")
        if len(value) < min_length:
            raise ValidationError(f"字符串长度不能少于{min_length}个字符")
        if max_length and len(value) > max_length:
            raise ValidationError(f"字符串长度不能超过{max_length}个字符")
    return validator


def validate_positive_integer(value):
    """正整数验证器"""
    if not isinstance(value, int) or value <= 0:
        raise ValidationError("值必须是正整数")


def validate_email(value):
    """邮箱验证器"""
    import re
    if not isinstance(value, str):
        raise ValidationError("邮箱必须是字符串")

    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, value):
        raise ValidationError("邮箱格式不正确")
