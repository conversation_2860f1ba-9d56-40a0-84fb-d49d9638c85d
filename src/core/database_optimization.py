"""
数据库性能优化工具
包含索引创建、查询优化等功能
"""
import sqlite3
from typing import List, Dict, Any
from datetime import datetime

from .database import DatabaseManager


class DatabaseOptimizer:
    """数据库优化器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def create_performance_indexes(self) -> Dict[str, bool]:
        """
        创建性能优化索引
        基于LEGACY_CODE_REFACTORING_PLAN.md中的索引策略
        
        Returns:
            Dict[str, bool]: 索引创建结果
        """
        indexes = {
            # 用户单词关系表索引
            'idx_user_word_user_proficiency': """
                CREATE INDEX IF NOT EXISTS idx_user_word_user_proficiency 
                ON user_word(user_id, proficiency DESC)
            """,
            
            'idx_user_word_user_status': """
                CREATE INDEX IF NOT EXISTS idx_user_word_user_status 
                ON user_word(user_id, status)
            """,
            
            'idx_user_word_learning_date': """
                CREATE INDEX IF NOT EXISTS idx_user_word_learning_date 
                ON user_word(user_id, last_learning_date, status)
            """,
            
            # 学习记录表索引
            'idx_word_record_user_date_correct': """
                CREATE INDEX IF NOT EXISTS idx_word_record_user_date_correct 
                ON word_record(user_id, date, is_correct)
            """,
            
            'idx_word_record_user_word_date': """
                CREATE INDEX IF NOT EXISTS idx_word_record_user_word_date 
                ON word_record(user_id, word_id, date)
            """,
            
            'idx_word_record_word_correct': """
                CREATE INDEX IF NOT EXISTS idx_word_record_word_correct 
                ON word_record(word_id, is_correct)
            """,
            
            # 学习计划表索引
            'idx_learning_plan_user_date': """
                CREATE INDEX IF NOT EXISTS idx_learning_plan_user_date 
                ON learning_plan(user_id, planned_date)
            """,
            
            'idx_learning_plan_user_word_date': """
                CREATE INDEX IF NOT EXISTS idx_learning_plan_user_word_date 
                ON learning_plan(user_id, word_id, planned_date)
            """,
            
            'idx_learning_plan_star_level': """
                CREATE INDEX IF NOT EXISTS idx_learning_plan_star_level 
                ON learning_plan(user_id, star_level, planned_date)
            """,
            
            # 单词表索引
            'idx_word_section': """
                CREATE INDEX IF NOT EXISTS idx_word_section 
                ON word(section)
            """,
            
            'idx_word_english': """
                CREATE INDEX IF NOT EXISTS idx_word_english 
                ON word(english_word)
            """
        }
        
        results = {}
        
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                for index_name, index_sql in indexes.items():
                    try:
                        cursor.execute(index_sql)
                        results[index_name] = True
                        print(f"✅ 索引创建成功: {index_name}")
                    except Exception as e:
                        results[index_name] = False
                        print(f"❌ 索引创建失败: {index_name} - {e}")
                
                conn.commit()
                
        except Exception as e:
            print(f"数据库连接失败: {e}")
            
        return results
    
    def analyze_query_performance(self) -> Dict[str, Any]:
        """
        分析查询性能
        
        Returns:
            Dict[str, Any]: 性能分析结果
        """
        performance_tests = [
            {
                'name': '用户学习计划查询',
                'query': """
                    SELECT lp.*, w.english_word, w.chinese_meaning 
                    FROM learning_plan lp
                    JOIN word w ON lp.word_id = w.id
                    WHERE lp.user_id = 1 AND lp.planned_date = date('now')
                """,
                'expected_time': 0.01  # 10ms
            },
            {
                'name': '用户单词熟练度查询',
                'query': """
                    SELECT w.*, uw.proficiency 
                    FROM word w
                    JOIN user_word uw ON w.id = uw.word_id
                    WHERE uw.user_id = 1 AND uw.status IN ('review', 'attention')
                    ORDER BY uw.proficiency ASC
                    LIMIT 18
                """,
                'expected_time': 0.02  # 20ms
            },
            {
                'name': '今日学习记录查询',
                'query': """
                    SELECT COUNT(*) as count, 
                           SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct
                    FROM word_record 
                    WHERE user_id = 1 AND date = date('now')
                """,
                'expected_time': 0.005  # 5ms
            },
            {
                'name': '熟练度分布统计',
                'query': """
                    SELECT proficiency, COUNT(*) as count
                    FROM user_word 
                    WHERE user_id = 1 AND learning_count > 0
                    GROUP BY CASE 
                        WHEN proficiency >= 95 THEN 'expert'
                        WHEN proficiency >= 80 THEN 'proficient'
                        WHEN proficiency >= 65 THEN 'intermediate'
                        WHEN proficiency >= 50 THEN 'basic'
                        WHEN proficiency >= 25 THEN 'beginner'
                        ELSE 'unfamiliar'
                    END
                """,
                'expected_time': 0.01  # 10ms
            }
        ]
        
        results = []
        
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                for test in performance_tests:
                    start_time = datetime.now()
                    
                    try:
                        cursor.execute(test['query'])
                        cursor.fetchall()
                        
                        end_time = datetime.now()
                        execution_time = (end_time - start_time).total_seconds()
                        
                        status = "✅ PASS" if execution_time <= test['expected_time'] else "⚠️ SLOW"
                        
                        results.append({
                            'name': test['name'],
                            'execution_time': execution_time,
                            'expected_time': test['expected_time'],
                            'status': status,
                            'performance_ratio': execution_time / test['expected_time']
                        })
                        
                        print(f"{status} {test['name']}: {execution_time:.3f}s (期望: {test['expected_time']:.3f}s)")
                        
                    except Exception as e:
                        results.append({
                            'name': test['name'],
                            'execution_time': -1,
                            'expected_time': test['expected_time'],
                            'status': "❌ ERROR",
                            'error': str(e)
                        })
                        print(f"❌ ERROR {test['name']}: {e}")
                        
        except Exception as e:
            print(f"性能测试失败: {e}")
            
        return {
            'tests': results,
            'timestamp': datetime.now().isoformat(),
            'total_tests': len(performance_tests),
            'passed_tests': len([r for r in results if r['status'] == "✅ PASS"])
        }
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        获取数据库统计信息
        
        Returns:
            Dict[str, Any]: 数据库统计
        """
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                # 表记录数统计
                tables = ['user', 'word', 'user_word', 'word_record', 'learning_plan']
                table_stats = {}
                
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                    result = cursor.fetchone()
                    table_stats[table] = result[0] if result else 0
                
                # 索引统计
                cursor.execute("""
                    SELECT name, sql FROM sqlite_master 
                    WHERE type = 'index' AND name NOT LIKE 'sqlite_%'
                    ORDER BY name
                """)
                indexes = cursor.fetchall()
                
                # 数据库文件大小
                cursor.execute("PRAGMA page_count")
                page_count = cursor.fetchone()[0]
                cursor.execute("PRAGMA page_size")
                page_size = cursor.fetchone()[0]
                db_size_bytes = page_count * page_size
                
                return {
                    'table_stats': table_stats,
                    'total_records': sum(table_stats.values()),
                    'indexes': [{'name': idx[0], 'sql': idx[1]} for idx in indexes],
                    'index_count': len(indexes),
                    'database_size_bytes': db_size_bytes,
                    'database_size_mb': round(db_size_bytes / (1024 * 1024), 2),
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            print(f"获取数据库统计失败: {e}")
            return {'error': str(e)}


# 全局优化器实例
from .database import enhanced_db
db_optimizer = DatabaseOptimizer(enhanced_db)
