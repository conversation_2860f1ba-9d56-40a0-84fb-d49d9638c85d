"""
异常处理体系
定义项目特定的异常类层次结构和异常处理工具
"""
import logging
from typing import Dict, Any, Optional, Union
from functools import wraps
from flask import jsonify

logger = logging.getLogger(__name__)

class BaseAppException(Exception):
    """应用基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: str = None,
        details: Dict[str, Any] = None,
        http_status: int = 500,
        cause: Exception = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.http_status = http_status
        self.cause = cause
        
        # 记录异常日志
        logger.error(f"{self.error_code}: {message}", extra={
            'error_code': self.error_code,
            'details': self.details,
            'http_status': self.http_status
        })
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            'success': False,
            'error_code': self.error_code,
            'message': self.message,
            'http_status': self.http_status
        }
        
        if self.details:
            result['details'] = self.details
            
        return result
    
    def to_json_response(self):
        """转换为Flask JSON响应"""
        return jsonify(self.to_dict()), self.http_status

class BusinessLogicError(BaseAppException):
    """业务逻辑异常"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code=error_code or "BUSINESS_LOGIC_ERROR",
            details=details,
            http_status=400
        )

class ValidationError(BaseAppException):
    """数据验证异常"""
    
    def __init__(self, message: str, field: str = None, value: Any = None, details: Dict[str, Any] = None):
        validation_details = details or {}
        if field:
            validation_details['field'] = field
        if value is not None:
            validation_details['invalid_value'] = str(value)
            
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=validation_details,
            http_status=400
        )

class AuthenticationError(BaseAppException):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败", details: Dict[str, Any] = None):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            details=details,
            http_status=401
        )

class AuthorizationError(BaseAppException):
    """授权异常"""
    
    def __init__(self, message: str = "权限不足", resource: str = None, action: str = None):
        details = {}
        if resource:
            details['resource'] = resource
        if action:
            details['action'] = action
            
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            details=details,
            http_status=403
        )

class DatabaseError(BaseAppException):
    """数据库操作异常"""
    
    def __init__(self, message: str, operation: str = None, table: str = None, cause: Exception = None):
        details = {}
        if operation:
            details['operation'] = operation
        if table:
            details['table'] = table
            
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details=details,
            http_status=500,
            cause=cause
        )

class ExternalServiceError(BaseAppException):
    """外部服务异常"""
    
    def __init__(self, message: str, service: str = None, endpoint: str = None, cause: Exception = None):
        details = {}
        if service:
            details['service'] = service
        if endpoint:
            details['endpoint'] = endpoint
            
        super().__init__(
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            details=details,
            http_status=502,
            cause=cause
        )

class ResourceNotFoundError(BaseAppException):
    """资源未找到异常"""

    def __init__(self, message: str, identifier: Union[str, int] = None):
        if identifier is not None:
            full_message = f"{message}: {identifier}"
            details = {'identifier': str(identifier)}
        else:
            full_message = message
            details = {}

        super().__init__(
            message=full_message,
            error_code="RESOURCE_NOT_FOUND",
            details=details,
            http_status=404
        )

class DuplicateResourceError(BaseAppException):
    """资源重复异常"""
    
    def __init__(self, resource: str, field: str = None, value: str = None):
        message = f"{resource}已存在"
        details = {'resource': resource}
        
        if field and value:
            message += f": {field}={value}"
            details['field'] = field
            details['value'] = value
            
        super().__init__(
            message=message,
            error_code="DUPLICATE_RESOURCE",
            details=details,
            http_status=409
        )

class RateLimitError(BaseAppException):
    """频率限制异常"""
    
    def __init__(self, message: str = "请求过于频繁", limit: int = None, window: int = None):
        details = {}
        if limit:
            details['limit'] = limit
        if window:
            details['window_seconds'] = window
            
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            details=details,
            http_status=429
        )

# 异常处理装饰器
def handle_exceptions(default_message: str = "操作失败", log_errors: bool = True):
    """异常处理装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except BaseAppException:
                # 重新抛出应用异常
                raise
            except Exception as e:
                if log_errors:
                    logger.exception(f"Unexpected error in {func.__name__}: {e}")
                
                # 将未知异常包装为应用异常
                raise BaseAppException(
                    message=default_message,
                    error_code="INTERNAL_ERROR",
                    details={'original_error': str(e)},
                    cause=e
                )
        return wrapper
    return decorator

def validate_required_fields(data: Dict[str, Any], required_fields: list):
    """验证必需字段"""
    missing_fields = []
    for field in required_fields:
        if field not in data or data[field] is None or data[field] == '':
            missing_fields.append(field)
    
    if missing_fields:
        raise ValidationError(
            message=f"缺少必需字段: {', '.join(missing_fields)}",
            details={'missing_fields': missing_fields}
        )

def validate_field_type(data: Dict[str, Any], field: str, expected_type: type):
    """验证字段类型"""
    if field in data and data[field] is not None:
        if not isinstance(data[field], expected_type):
            raise ValidationError(
                message=f"字段 {field} 类型错误",
                field=field,
                details={
                    'expected_type': expected_type.__name__,
                    'actual_type': type(data[field]).__name__
                }
            )

def validate_field_length(data: Dict[str, Any], field: str, min_length: int = None, max_length: int = None):
    """验证字段长度"""
    if field in data and data[field] is not None:
        value = str(data[field])
        length = len(value)
        
        if min_length is not None and length < min_length:
            raise ValidationError(
                message=f"字段 {field} 长度不能少于 {min_length} 个字符",
                field=field,
                value=value,
                details={'min_length': min_length, 'actual_length': length}
            )
        
        if max_length is not None and length > max_length:
            raise ValidationError(
                message=f"字段 {field} 长度不能超过 {max_length} 个字符",
                field=field,
                value=value,
                details={'max_length': max_length, 'actual_length': length}
            )

# Flask错误处理器注册函数
def register_error_handlers(app):
    """注册Flask错误处理器"""
    
    @app.errorhandler(BaseAppException)
    def handle_app_exception(error):
        """处理应用异常"""
        return error.to_json_response()
    
    @app.errorhandler(404)
    def handle_not_found(error):
        """处理404错误"""
        return jsonify({
            'success': False,
            'error_code': 'NOT_FOUND',
            'message': '请求的资源不存在',
            'http_status': 404
        }), 404
    
    @app.errorhandler(500)
    def handle_internal_error(error):
        """处理500错误"""
        logger.exception("Internal server error")
        return jsonify({
            'success': False,
            'error_code': 'INTERNAL_ERROR',
            'message': '服务器内部错误',
            'http_status': 500
        }), 500

# 异常工具函数
def raise_if_none(value: Any, error_message: str, error_class: type = ResourceNotFoundError):
    """如果值为None则抛出异常"""
    if value is None:
        raise error_class(error_message)
    return value

def raise_if_empty(value: Union[str, list, dict], error_message: str, error_class: type = ValidationError):
    """如果值为空则抛出异常"""
    if not value:
        raise error_class(error_message)
    return value
