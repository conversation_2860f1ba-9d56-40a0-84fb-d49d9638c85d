"""
增强版数据库管理组件
基于现有DatabaseManager，添加连接池、事务管理、健康检查等功能
"""
import sqlite3
import os
import logging
import threading
import time
from contextlib import contextmanager
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from queue import Queue, Empty
from functools import wraps

# 配置日志
logger = logging.getLogger(__name__)

class DatabaseConnectionPool:
    """数据库连接池"""
    
    def __init__(self, db_path: str, max_connections: int = 10, timeout: int = 30):
        self.db_path = db_path
        self.max_connections = max_connections
        self.timeout = timeout
        self._pool = Queue(maxsize=max_connections)
        self._created_connections = 0
        self._lock = threading.Lock()
        
        # 预创建一些连接
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        initial_connections = min(3, self.max_connections)
        for _ in range(initial_connections):
            conn = self._create_connection()
            if conn:
                self._pool.put(conn)
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """创建新的数据库连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=self.timeout
            )
            conn.row_factory = sqlite3.Row
            # 启用外键约束
            conn.execute("PRAGMA foreign_keys = ON")
            # 设置WAL模式以提高并发性能
            conn.execute("PRAGMA journal_mode = WAL")
            self._created_connections += 1
            logger.debug(f"Created new database connection. Total: {self._created_connections}")
            return conn
        except Exception as e:
            logger.error(f"Failed to create database connection: {e}")
            return None
    
    def get_connection(self) -> sqlite3.Connection:
        """从连接池获取连接"""
        try:
            # 尝试从池中获取连接
            conn = self._pool.get(timeout=self.timeout)
            
            # 检查连接是否有效
            if self._is_connection_valid(conn):
                return conn
            else:
                # 连接无效，创建新连接
                logger.warning("Invalid connection found in pool, creating new one")
                conn.close()
                return self._create_connection()
                
        except Empty:
            # 池中没有可用连接，创建新连接
            with self._lock:
                if self._created_connections < self.max_connections:
                    return self._create_connection()
                else:
                    raise Exception(f"Connection pool exhausted. Max connections: {self.max_connections}")
    
    def return_connection(self, conn: sqlite3.Connection):
        """将连接返回到池中"""
        if conn and self._is_connection_valid(conn):
            try:
                self._pool.put_nowait(conn)
            except:
                # 池已满，关闭连接
                conn.close()
                self._created_connections -= 1
        else:
            if conn:
                conn.close()
                self._created_connections -= 1
    
    def _is_connection_valid(self, conn: sqlite3.Connection) -> bool:
        """检查连接是否有效"""
        try:
            conn.execute("SELECT 1")
            return True
        except:
            return False
    
    def close_all(self):
        """关闭所有连接"""
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                conn.close()
            except Empty:
                break
        self._created_connections = 0

class EnhancedDatabaseManager:
    """增强版数据库管理器"""
    
    def __init__(self, db_path: str = None, max_connections: int = 10):
        # 如果没有提供路径，使用配置中的路径
        if db_path is None:
            from ..config import Config
            db_path = Config.DATABASE_PATH
            
        self.db_path = db_path
        self._ensure_db_exists()
        
        # 初始化连接池
        self.pool = DatabaseConnectionPool(db_path, max_connections)
        
        # 统计信息
        self.stats = {
            'queries_executed': 0,
            'transactions_committed': 0,
            'transactions_rolled_back': 0,
            'errors_occurred': 0
        }
    
    def _ensure_db_exists(self):
        """确保数据库文件存在"""
        if self.db_path != ":memory:":
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = self.pool.get_connection()
            yield conn
        except Exception as e:
            self.stats['errors_occurred'] += 1
            logger.error(f"Database connection error: {e}")
            raise
        finally:
            if conn:
                self.pool.return_connection(conn)
    
    def execute_query(self, query: str, params: tuple = None) -> List[sqlite3.Row]:
        """执行查询并返回结果"""
        start_time = time.time()
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                result = cursor.fetchall()
                
                self.stats['queries_executed'] += 1
                execution_time = time.time() - start_time
                
                if execution_time > 1.0:  # 记录慢查询
                    logger.warning(f"Slow query detected: {execution_time:.2f}s - {query[:100]}...")
                
                return result
                
        except Exception as e:
            self.stats['errors_occurred'] += 1
            logger.error(f"Query execution failed: {e} - Query: {query}")
            raise
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新操作并返回影响的行数"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                conn.commit()
                
                self.stats['queries_executed'] += 1
                return cursor.rowcount
                
        except Exception as e:
            self.stats['errors_occurred'] += 1
            logger.error(f"Update execution failed: {e} - Query: {query}")
            raise
    
    def execute_insert(self, query: str, params: tuple = None) -> int:
        """执行插入操作并返回新记录的ID"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                conn.commit()
                
                self.stats['queries_executed'] += 1
                return cursor.lastrowid
                
        except Exception as e:
            self.stats['errors_occurred'] += 1
            # 增强错误日志记录
            error_details = f"Insert execution failed: {str(e)}"
            if params:
                error_details += f" - Parameters: {params}"
            error_details += f" - Query: {query}"
            logger.error(error_details)
            
            # 打印到控制台以便调试
            print(f"DATABASE_ERROR: {error_details}")
            raise
    
    @contextmanager
    def transaction(self):
        """事务上下文管理器"""
        conn = None
        try:
            conn = self.pool.get_connection()
            conn.execute("BEGIN")
            yield conn
            conn.commit()
            self.stats['transactions_committed'] += 1
            
        except Exception as e:
            if conn:
                conn.rollback()
                self.stats['transactions_rolled_back'] += 1
            logger.error(f"Transaction failed: {e}")
            raise
        finally:
            if conn:
                self.pool.return_connection(conn)
    
    def health_check(self) -> Dict[str, Any]:
        """数据库健康检查"""
        try:
            start_time = time.time()
            
            # 测试基本连接
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            connection_time = time.time() - start_time
            
            # 检查数据库文件
            db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
            
            # 检查表是否存在
            required_tables = ['user', 'word', 'user_word', 'word_record', 'learning_plan']
            existing_tables = []
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                existing_tables = [row[0] for row in cursor.fetchall()]
            
            missing_tables = [table for table in required_tables if table not in existing_tables]
            
            return {
                'status': 'healthy' if not missing_tables else 'warning',
                'connection_time_ms': round(connection_time * 1000, 2),
                'database_size_mb': round(db_size / (1024 * 1024), 2),
                'existing_tables': existing_tables,
                'missing_tables': missing_tables,
                'pool_stats': {
                    'created_connections': self.pool._created_connections,
                    'max_connections': self.pool.max_connections
                },
                'query_stats': self.stats.copy(),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'database_stats': self.stats.copy(),
            'pool_stats': {
                'created_connections': self.pool._created_connections,
                'max_connections': self.pool.max_connections
            }
        }
    
    def close(self):
        """关闭数据库管理器"""
        self.pool.close_all()

# 事务装饰器
def transactional(db_manager: EnhancedDatabaseManager):
    """事务装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            with db_manager.transaction() as conn:
                # 将连接作为第一个参数传递给函数
                return func(conn, *args, **kwargs)
        return wrapper
    return decorator

# 全局增强数据库管理器实例（保持向后兼容）
enhanced_db = EnhancedDatabaseManager()

# 为了保持向后兼容，创建一个兼容层
class CompatibilityDatabaseManager:
    """兼容性数据库管理器 - 保持与原有API的兼容性"""
    
    def __init__(self):
        self.enhanced_db = enhanced_db
    
    def get_connection(self):
        """获取数据库连接 - 兼容原有API"""
        # 注意：这里返回的是上下文管理器，使用方式略有不同
        # 但对于现有代码中的 with db.get_connection() as conn: 用法是兼容的
        return self.enhanced_db.get_connection()
    
    def execute_query(self, query: str, params: tuple = None) -> List[sqlite3.Row]:
        """执行查询并返回结果"""
        return self.enhanced_db.execute_query(query, params)
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新操作并返回影响的行数"""
        return self.enhanced_db.execute_update(query, params)
    
    def execute_insert(self, query: str, params: tuple = None) -> int:
        """执行插入操作并返回新记录的ID"""
        return self.enhanced_db.execute_insert(query, params)

# 创建兼容的全局实例
db = CompatibilityDatabaseManager()
