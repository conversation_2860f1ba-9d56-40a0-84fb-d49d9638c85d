"""
错误处理系统
提供统一的错误处理、异常捕获和用户友好的错误响应
"""
import traceback
from typing import Dict, Any, <PERSON><PERSON>, Optional
from flask import request, jsonify, render_template, current_app
from werkzeug.exceptions import HTTPException

from .exceptions import BaseAppException
from .logging import get_logger


class ErrorHandler:
    """基础错误处理器"""
    
    def __init__(self):
        self.logger = get_logger('error_handler')
    
    def handle_error(self, error: Exception) -> Tuple[str, int]:
        """处理错误的基础方法"""
        error_id = self._generate_error_id()
        
        # 记录错误
        self._log_error(error, error_id)
        
        # 返回错误响应
        return self._format_error_response(error, error_id)
    
    def _generate_error_id(self) -> str:
        """生成错误ID"""
        import uuid
        return str(uuid.uuid4())[:8]
    
    def _log_error(self, error: Exception, error_id: str):
        """记录错误日志"""
        error_details = {
            'error_id': error_id,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'request_method': getattr(request, 'method', 'Unknown'),
            'request_path': getattr(request, 'path', 'Unknown'),
            'user_agent': str(getattr(request, 'user_agent', 'Unknown')) if hasattr(request, 'user_agent') else 'Unknown'
        }
        
        # 添加用户信息（如果可用）
        if hasattr(request, 'session') and 'user_id' in request.session:
            error_details['user_id'] = request.session['user_id']
        
        self.logger.error(
            f"Error {error_id}: {type(error).__name__}: {str(error)}",
            exception=error,
            **error_details
        )
    
    def _format_error_response(self, error: Exception, error_id: str) -> Tuple[str, int]:
        """格式化错误响应"""
        # 子类应该重写此方法
        return f"Internal Server Error (ID: {error_id})", 500


class APIErrorHandler(ErrorHandler):
    """API错误处理器"""
    
    def _format_error_response(self, error: Exception, error_id: str) -> Tuple[str, int]:
        """格式化API错误响应"""
        # 确定状态码
        if isinstance(error, HTTPException):
            status_code = error.code
        elif isinstance(error, BaseAppException):
            status_code = getattr(error, 'http_status', 400)
        else:
            status_code = 500
        
        # 确定错误消息
        if isinstance(error, BaseAppException):
            message = str(error)
            error_type = type(error).__name__
        elif isinstance(error, HTTPException):
            message = error.description
            error_type = 'HTTPError'
        else:
            message = "服务器内部错误"
            error_type = 'InternalError'
        
        # 构建响应
        response_data = {
            'success': False,
            'error': {
                'type': error_type,
                'message': message,
                'error_id': error_id
            }
        }
        
        # 在调试模式下添加详细信息
        if current_app.debug:
            response_data['error']['traceback'] = traceback.format_exc()
        
        return jsonify(response_data), status_code


class WebErrorHandler(ErrorHandler):
    """Web页面错误处理器"""
    
    def _format_error_response(self, error: Exception, error_id: str) -> Tuple[str, int]:
        """格式化Web错误响应"""
        # 确定状态码
        if isinstance(error, HTTPException):
            status_code = error.code
        else:
            status_code = 500
        
        # 确定错误消息
        if isinstance(error, BaseAppException):
            message = str(error)
        elif isinstance(error, HTTPException):
            message = error.description
        else:
            message = "服务器遇到了一个错误"
        
        # 渲染错误页面
        try:
            return render_template(
                'error.html',
                error_code=status_code,
                error_message=message,
                error_id=error_id,
                debug=current_app.debug
            ), status_code
        except:
            # 如果模板渲染失败，返回简单的HTML
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>错误 {status_code}</title>
                <meta charset="utf-8">
            </head>
            <body>
                <h1>错误 {status_code}</h1>
                <p>{message}</p>
                <p>错误ID: {error_id}</p>
            </body>
            </html>
            """
            return html, status_code


def register_error_handlers(app):
    """注册错误处理器到Flask应用"""
    
    api_handler = APIErrorHandler()
    web_handler = WebErrorHandler()
    
    def is_api_request():
        """判断是否为API请求"""
        return (
            request.path.startswith('/api/') or
            request.headers.get('Content-Type', '').startswith('application/json') or
            'application/json' in request.headers.get('Accept', '')
        )
    
    @app.errorhandler(BaseAppException)
    def handle_app_exception(error):
        """处理应用异常"""
        if is_api_request():
            return api_handler.handle_error(error)
        else:
            return web_handler.handle_error(error)
    
    @app.errorhandler(400)
    def handle_bad_request(error):
        """处理400错误"""
        if is_api_request():
            return api_handler.handle_error(error)
        else:
            return web_handler.handle_error(error)
    
    @app.errorhandler(401)
    def handle_unauthorized(error):
        """处理401错误"""
        if is_api_request():
            return api_handler.handle_error(error)
        else:
            # 重定向到登录页面
            from flask import redirect, url_for
            return redirect(url_for('login_page'))
    
    @app.errorhandler(403)
    def handle_forbidden(error):
        """处理403错误"""
        if is_api_request():
            return api_handler.handle_error(error)
        else:
            return web_handler.handle_error(error)
    
    @app.errorhandler(404)
    def handle_not_found(error):
        """处理404错误"""
        if is_api_request():
            return api_handler.handle_error(error)
        else:
            return web_handler.handle_error(error)
    
    @app.errorhandler(405)
    def handle_method_not_allowed(error):
        """处理405错误"""
        if is_api_request():
            return api_handler.handle_error(error)
        else:
            return web_handler.handle_error(error)
    
    @app.errorhandler(500)
    def handle_internal_error(error):
        """处理500错误"""
        if is_api_request():
            return api_handler.handle_error(error)
        else:
            return web_handler.handle_error(error)
    
    @app.errorhandler(Exception)
    def handle_unexpected_error(error):
        """处理未预期的错误"""
        if is_api_request():
            return api_handler.handle_error(error)
        else:
            return web_handler.handle_error(error)
    
    # 注册请求前后钩子
    @app.before_request
    def before_request():
        """请求前处理"""
        # 这里可以添加请求验证、限流等逻辑
        pass
    
    @app.after_request
    def after_request(response):
        """请求后处理"""
        # 添加安全头
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        # 记录响应日志
        if current_app.debug:
            logger = get_logger('request')
            logger.debug(
                f"Response: {response.status_code}",
                method=request.method,
                path=request.path,
                status_code=response.status_code
            )
        
        return response


class ErrorReporter:
    """错误报告器"""
    
    def __init__(self):
        self.logger = get_logger('error_reporter')
    
    def report_error(self, error: Exception, context: Dict[str, Any] = None):
        """报告错误"""
        error_data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'traceback': traceback.format_exc()
        }
        
        if context:
            error_data['context'] = context
        
        self.logger.error("Error reported", **error_data)
    
    def report_performance_issue(self, operation: str, duration: float, threshold: float = 1.0):
        """报告性能问题"""
        if duration > threshold:
            self.logger.warning(
                f"Performance issue: {operation}",
                operation=operation,
                duration=duration,
                threshold=threshold,
                issue_type='performance'
            )
    
    def report_security_event(self, event_type: str, details: Dict[str, Any]):
        """报告安全事件"""
        self.logger.warning(
            f"Security event: {event_type}",
            event_type=event_type,
            security_event=True,
            **details
        )


# 全局错误报告器
error_reporter = ErrorReporter()
