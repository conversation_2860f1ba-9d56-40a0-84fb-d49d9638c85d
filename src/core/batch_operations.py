"""
批量操作工具类
用于优化数据库查询性能，减少N+1查询问题
"""
from typing import List, Dict, Any, Optional
import sqlite3
from datetime import date, timedelta

from .database import DatabaseManager


class BatchQueryOptimizer:
    """批量查询优化器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def get_words_with_user_stats(self, user_id: int, word_ids: List[int]) -> Dict[int, Dict[str, Any]]:
        """
        批量获取单词信息和用户统计数据
        解决N+1查询问题
        
        Args:
            user_id: 用户ID
            word_ids: 单词ID列表
            
        Returns:
            Dict[int, Dict[str, Any]]: 单词ID -> 单词信息和统计数据
        """
        if not word_ids:
            return {}
        
        # 构建IN查询的占位符
        placeholders = ','.join('?' * len(word_ids))
        
        query = f"""
        SELECT 
            w.id, w.english_word, w.chinese_meaning, w.section,
            uw.learning_count, uw.correct_count, uw.proficiency, uw.status,
            uw.last_learning_date, uw.memory_method
        FROM word w
        LEFT JOIN user_word uw ON w.id = uw.word_id AND uw.user_id = ?
        WHERE w.id IN ({placeholders})
        ORDER BY w.id
        """
        
        params = [user_id] + word_ids
        results = self.db.execute_query(query, params)
        
        # 转换为字典格式
        word_stats = {}
        for row in results:
            word_stats[row['id']] = {
                'id': row['id'],
                'english_word': row['english_word'],
                'chinese_meaning': row['chinese_meaning'],
                'section': row['section'],
                'learning_count': row['learning_count'] or 0,
                'correct_count': row['correct_count'] or 0,
                'proficiency': row['proficiency'] or 0.0,
                'status': row['status'] or 'new',
                'last_learning_date': row['last_learning_date'],
                'memory_method': row['memory_method']
            }
        
        return word_stats
    
    def get_learning_records_batch(self, user_id: int, word_ids: List[int], 
                                  days: int = 30) -> Dict[int, List[Dict[str, Any]]]:
        """
        批量获取学习记录
        
        Args:
            user_id: 用户ID
            word_ids: 单词ID列表
            days: 查询天数
            
        Returns:
            Dict[int, List[Dict[str, Any]]]: 单词ID -> 学习记录列表
        """
        if not word_ids:
            return {}
        
        placeholders = ','.join('?' * len(word_ids))
        start_date = date.today() - timedelta(days=days)
        
        query = f"""
        SELECT word_id, date, duration_seconds, user_input, answer, is_correct
        FROM word_record
        WHERE user_id = ? AND word_id IN ({placeholders}) AND date >= ?
        ORDER BY word_id, date DESC
        """
        
        params = [user_id] + word_ids + [start_date]
        results = self.db.execute_query(query, params)
        
        # 按单词ID分组
        records_by_word = {}
        for row in results:
            word_id = row['word_id']
            if word_id not in records_by_word:
                records_by_word[word_id] = []
            
            records_by_word[word_id].append({
                'date': row['date'],
                'duration_seconds': row['duration_seconds'],
                'user_input': row['user_input'],
                'answer': row['answer'],
                'is_correct': row['is_correct']
            })
        
        return records_by_word
    
    def update_user_word_stats_batch(self, user_id: int, 
                                   word_stats: Dict[int, Dict[str, Any]]) -> bool:
        """
        批量更新用户单词统计数据
        
        Args:
            user_id: 用户ID
            word_stats: 单词统计数据 {word_id: {learning_count, correct_count, ...}}
            
        Returns:
            bool: 是否成功
        """
        if not word_stats:
            return True
        
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                for word_id, stats in word_stats.items():
                    # 使用UPSERT操作
                    cursor.execute("""
                        INSERT INTO user_word (user_id, word_id, learning_count, correct_count, 
                                             proficiency, status, last_learning_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                        ON CONFLICT(user_id, word_id) DO UPDATE SET
                            learning_count = excluded.learning_count,
                            correct_count = excluded.correct_count,
                            proficiency = excluded.proficiency,
                            status = excluded.status,
                            last_learning_date = excluded.last_learning_date
                    """, (
                        user_id, word_id,
                        stats.get('learning_count', 0),
                        stats.get('correct_count', 0),
                        stats.get('proficiency', 0.0),
                        stats.get('status', 'new'),
                        stats.get('last_learning_date')
                    ))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"Batch update failed: {e}")
            return False


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self._cache = {}
        self._ttl = {}
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        import time
        
        if key not in self._cache:
            return None
        
        # 检查TTL
        if key in self._ttl and time.time() > self._ttl[key]:
            del self._cache[key]
            del self._ttl[key]
            return None
        
        return self._cache[key]
    
    def set(self, key: str, value: Any, ttl: int = 300) -> None:
        """设置缓存值"""
        import time
        
        self._cache[key] = value
        if ttl > 0:
            self._ttl[key] = time.time() + ttl
    
    def clear(self) -> None:
        """清空缓存"""
        self._cache.clear()
        self._ttl.clear()


# 全局实例
from .database import enhanced_db
batch_optimizer = BatchQueryOptimizer(enhanced_db)
cache_manager = CacheManager()
