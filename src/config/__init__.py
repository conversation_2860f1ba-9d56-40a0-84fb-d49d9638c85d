"""
Configuration package - 配置管理
提供多环境配置支持和环境变量覆盖功能
"""

# 基础配置
from .config import Config

# 多环境配置
from .environments import (
    DevelopmentConfig,
    TestingConfig,
    ProductionConfig,
    StagingConfig,
    get_config,
    ConfigManager,
    config_manager,
    load_config_from_env,
    apply_env_config
)

__all__ = [
    # 基础配置
    'Config',

    # 环境配置
    'DevelopmentConfig',
    'TestingConfig',
    'ProductionConfig',
    'StagingConfig',
    'get_config',

    # 配置管理
    'ConfigManager',
    'config_manager',
    'load_config_from_env',
    'apply_env_config'
]

# 版本信息
__version__ = '2.0.0'
__config_version__ = '2.0.0'
