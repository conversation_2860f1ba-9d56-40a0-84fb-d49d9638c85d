"""
配置验证工具
提供配置验证、环境检查和配置诊断功能
"""
import os
import sys
from typing import Dict, List, Any, Tuple
from .environments import config_manager, get_config


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self, config_name=None):
        self.config_name = config_name or os.environ.get('FLASK_ENV', 'development')
        self.config = get_config(self.config_name)()
    
    def validate_all(self) -> Dict[str, Any]:
        """执行全面的配置验证"""
        results = {
            'environment': self.config_name,
            'overall_status': 'unknown',
            'checks': {}
        }
        
        # 执行各项检查
        results['checks']['basic'] = self._validate_basic_config()
        results['checks']['security'] = self._validate_security_config()
        results['checks']['database'] = self._validate_database_config()
        results['checks']['logging'] = self._validate_logging_config()
        results['checks']['learning'] = self._validate_learning_config()
        results['checks']['environment'] = self._validate_environment_vars()
        results['checks']['directories'] = self._validate_directories()
        
        # 计算总体状态
        all_errors = []
        all_warnings = []
        
        for check_name, check_result in results['checks'].items():
            all_errors.extend(check_result.get('errors', []))
            all_warnings.extend(check_result.get('warnings', []))
        
        if all_errors:
            results['overall_status'] = 'error'
        elif all_warnings:
            results['overall_status'] = 'warning'
        else:
            results['overall_status'] = 'ok'
        
        results['summary'] = {
            'total_errors': len(all_errors),
            'total_warnings': len(all_warnings),
            'errors': all_errors,
            'warnings': all_warnings
        }
        
        return results
    
    def _validate_basic_config(self) -> Dict[str, Any]:
        """验证基础配置"""
        errors = []
        warnings = []
        
        # 检查必需的配置项
        required_attrs = ['SECRET_KEY', 'DATABASE_PATH']
        for attr in required_attrs:
            if not hasattr(self.config, attr):
                errors.append(f"Missing required config attribute: {attr}")
            elif not getattr(self.config, attr):
                errors.append(f"Required config attribute is empty: {attr}")
        
        # 检查配置类型
        if hasattr(self.config, 'DEBUG') and not isinstance(self.config.DEBUG, bool):
            warnings.append("DEBUG should be a boolean value")
        
        if hasattr(self.config, 'TESTING') and not isinstance(self.config.TESTING, bool):
            warnings.append("TESTING should be a boolean value")
        
        return {
            'status': 'error' if errors else ('warning' if warnings else 'ok'),
            'errors': errors,
            'warnings': warnings
        }
    
    def _validate_security_config(self) -> Dict[str, Any]:
        """验证安全配置"""
        errors = []
        warnings = []
        
        # 检查SECRET_KEY
        if hasattr(self.config, 'SECRET_KEY'):
            secret_key = self.config.SECRET_KEY
            if secret_key in ['dev-secret-key-change-in-production', 'production-secret-key-must-be-changed']:
                if self.config_name == 'production':
                    errors.append("Production SECRET_KEY must be changed from default value")
                else:
                    warnings.append("Using default SECRET_KEY (should be changed for production)")
            
            if len(secret_key) < 16:
                warnings.append("SECRET_KEY should be at least 16 characters long")
        
        # 检查生产环境安全设置
        if self.config_name == 'production':
            if not getattr(self.config, 'SESSION_COOKIE_SECURE', False):
                warnings.append("SESSION_COOKIE_SECURE should be True in production")
            
            if not getattr(self.config, 'SESSION_COOKIE_HTTPONLY', True):
                warnings.append("SESSION_COOKIE_HTTPONLY should be True in production")
        
        return {
            'status': 'error' if errors else ('warning' if warnings else 'ok'),
            'errors': errors,
            'warnings': warnings
        }
    
    def _validate_database_config(self) -> Dict[str, Any]:
        """验证数据库配置"""
        errors = []
        warnings = []
        
        if hasattr(self.config, 'DATABASE_PATH'):
            db_path = self.config.DATABASE_PATH
            
            if db_path == ':memory:':
                if self.config_name == 'production':
                    errors.append("Production environment should not use in-memory database")
                else:
                    warnings.append("Using in-memory database (data will not persist)")
            else:
                # 检查数据库目录
                db_dir = os.path.dirname(db_path)
                if not os.path.exists(db_dir):
                    warnings.append(f"Database directory does not exist: {db_dir}")
                
                # 检查数据库文件权限
                if os.path.exists(db_path):
                    if not os.access(db_path, os.R_OK):
                        errors.append(f"Database file is not readable: {db_path}")
                    if not os.access(db_path, os.W_OK):
                        errors.append(f"Database file is not writable: {db_path}")
        
        return {
            'status': 'error' if errors else ('warning' if warnings else 'ok'),
            'errors': errors,
            'warnings': warnings
        }
    
    def _validate_logging_config(self) -> Dict[str, Any]:
        """验证日志配置"""
        errors = []
        warnings = []
        
        # 检查日志级别
        if hasattr(self.config, 'LOG_LEVEL'):
            log_level = self.config.LOG_LEVEL
            valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if log_level not in valid_levels:
                errors.append(f"Invalid LOG_LEVEL: {log_level}. Must be one of {valid_levels}")
        
        # 检查日志文件
        if hasattr(self.config, 'LOG_FILE') and self.config.LOG_FILE:
            log_file = self.config.LOG_FILE
            log_dir = os.path.dirname(log_file)
            
            if log_dir and not os.path.exists(log_dir):
                warnings.append(f"Log directory does not exist: {log_dir}")
            
            if os.path.exists(log_file):
                if not os.access(log_file, os.W_OK):
                    errors.append(f"Log file is not writable: {log_file}")
        
        return {
            'status': 'error' if errors else ('warning' if warnings else 'ok'),
            'errors': errors,
            'warnings': warnings
        }
    
    def _validate_learning_config(self) -> Dict[str, Any]:
        """验证学习配置"""
        errors = []
        warnings = []
        
        # 检查学习计划配置
        learning_configs = [
            ('NEW_WORDS_COUNT', 1, 20),
            ('REVIEW_WORDS_COUNT', 1, 50),
            ('DAILY_WORDS_COUNT', 5, 100)
        ]
        
        for attr, min_val, max_val in learning_configs:
            if hasattr(self.config, attr):
                value = getattr(self.config, attr)
                if not isinstance(value, int):
                    errors.append(f"{attr} must be an integer")
                elif value < min_val or value > max_val:
                    warnings.append(f"{attr} should be between {min_val} and {max_val}")
        
        # 检查星级配置
        star_configs = [
            ('DEFAULT_NEW_STAR_LEVEL', 1, 5),
            ('DEFAULT_REVIEW_STAR_LEVEL', 1, 5),
            ('MAX_STAR_LEVEL', 3, 10),
            ('MIN_STAR_LEVEL', 1, 3)
        ]
        
        for attr, min_val, max_val in star_configs:
            if hasattr(self.config, attr):
                value = getattr(self.config, attr)
                if not isinstance(value, int):
                    errors.append(f"{attr} must be an integer")
                elif value < min_val or value > max_val:
                    warnings.append(f"{attr} should be between {min_val} and {max_val}")
        
        return {
            'status': 'error' if errors else ('warning' if warnings else 'ok'),
            'errors': errors,
            'warnings': warnings
        }
    
    def _validate_environment_vars(self) -> Dict[str, Any]:
        """验证环境变量"""
        errors = []
        warnings = []
        
        # 检查推荐的环境变量
        recommended_vars = {
            'production': ['FLASK_SECRET_KEY', 'DATABASE_URL'],
            'staging': ['FLASK_SECRET_KEY'],
            'development': [],
            'testing': []
        }
        
        missing_vars = []
        for var in recommended_vars.get(self.config_name, []):
            if not os.environ.get(var):
                missing_vars.append(var)
        
        if missing_vars:
            if self.config_name == 'production':
                errors.extend([f"Missing required environment variable: {var}" for var in missing_vars])
            else:
                warnings.extend([f"Recommended environment variable not set: {var}" for var in missing_vars])
        
        return {
            'status': 'error' if errors else ('warning' if warnings else 'ok'),
            'errors': errors,
            'warnings': warnings
        }
    
    def _validate_directories(self) -> Dict[str, Any]:
        """验证目录结构"""
        errors = []
        warnings = []
        
        # 检查必需的目录
        required_dirs = ['templates', 'static']
        for dir_name in required_dirs:
            if not os.path.exists(dir_name):
                errors.append(f"Required directory missing: {dir_name}")
        
        # 检查推荐的目录
        recommended_dirs = ['logs', 'instance']
        for dir_name in recommended_dirs:
            if not os.path.exists(dir_name):
                warnings.append(f"Recommended directory missing: {dir_name}")
        
        return {
            'status': 'error' if errors else ('warning' if warnings else 'ok'),
            'errors': errors,
            'warnings': warnings
        }


def validate_config(config_name=None) -> Dict[str, Any]:
    """
    验证配置的便捷函数
    
    Args:
        config_name: 配置环境名称
        
    Returns:
        验证结果字典
    """
    validator = ConfigValidator(config_name)
    return validator.validate_all()


def print_config_report(config_name=None):
    """
    打印配置验证报告
    
    Args:
        config_name: 配置环境名称
    """
    result = validate_config(config_name)
    
    print(f"📋 配置验证报告 - 环境: {result['environment']}")
    print(f"总体状态: {result['overall_status'].upper()}")
    print()
    
    # 打印各项检查结果
    for check_name, check_result in result['checks'].items():
        status_icon = {
            'ok': '✅',
            'warning': '⚠️',
            'error': '❌'
        }.get(check_result['status'], '❓')
        
        print(f"{status_icon} {check_name.title()}: {check_result['status']}")
        
        for error in check_result.get('errors', []):
            print(f"   ❌ {error}")
        
        for warning in check_result.get('warnings', []):
            print(f"   ⚠️  {warning}")
    
    # 打印总结
    summary = result['summary']
    print()
    print(f"📊 总结: {summary['total_errors']}个错误, {summary['total_warnings']}个警告")
    
    if summary['total_errors'] > 0:
        print("❌ 配置验证失败，请修复错误后重试")
        return False
    elif summary['total_warnings'] > 0:
        print("⚠️  配置验证通过，但有警告需要注意")
        return True
    else:
        print("✅ 配置验证完全通过")
        return True


if __name__ == '__main__':
    """命令行配置验证工具"""
    import sys
    
    config_name = sys.argv[1] if len(sys.argv) > 1 else None
    success = print_config_report(config_name)
    sys.exit(0 if success else 1)
