"""
应用配置
"""
import os
from datetime import timedelta

class Config:
    # 基础配置
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))  # 项目根目录
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # 数据库配置
    DATABASE_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'instance', 'words.db')
    
    # Flask配置
    DEBUG = True
    TEMPLATES_AUTO_RELOAD = True
    
    # 学习计划配置
    DAILY_WORDS_COUNT = 30
    NEW_WORDS_COUNT = 10
    REVIEW_WORDS_COUNT = 20
    
    # 星级系统配置
    DEFAULT_NEW_STAR_LEVEL = 3
    DEFAULT_REVIEW_STAR_LEVEL = 4
    MAX_STAR_LEVEL = 5
    MIN_STAR_LEVEL = 1
    
    # 积分系统配置
    CORRECT_ANSWER_POINTS = 10
    WRONG_ANSWER_PENALTY = 20