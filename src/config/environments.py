"""
多环境配置管理
支持开发、测试、生产环境的不同配置
"""
import os
from datetime import timedelta
from .config import Config


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False
    
    # 开发环境数据库
    DATABASE_PATH = os.path.join(Config.BASE_DIR, 'instance', 'words_dev.db')
    
    # 开发环境日志
    LOG_LEVEL = 'DEBUG'
    LOG_FILE = 'logs/app_dev.log'
    
    # 开发环境Flask配置
    TEMPLATES_AUTO_RELOAD = True
    EXPLAIN_TEMPLATE_LOADING = False
    
    # 开发环境安全配置（较宽松）
    SECRET_KEY = 'dev-secret-key-change-in-production'
    SESSION_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 开发环境性能配置
    SEND_FILE_MAX_AGE_DEFAULT = 0  # 禁用静态文件缓存


class TestingConfig(Config):
    """测试环境配置"""
    DEBUG = False
    TESTING = True
    
    # 测试环境使用内存数据库
    DATABASE_PATH = ':memory:'
    
    # 测试环境日志
    LOG_LEVEL = 'WARNING'
    LOG_FILE = None  # 不写入文件
    
    # 测试环境安全配置
    SECRET_KEY = 'testing-secret-key'
    WTF_CSRF_ENABLED = False
    
    # 测试环境学习配置（加速测试）
    NEW_WORDS_COUNT = 3
    REVIEW_WORDS_COUNT = 5
    DAILY_WORDS_COUNT = 8


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False
    
    # 生产环境数据库
    DATABASE_PATH = os.environ.get('DATABASE_URL') or os.path.join(Config.BASE_DIR, 'instance', 'words.db')
    
    # 生产环境日志
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'logs/app.log'
    
    # 生产环境安全配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'production-secret-key-must-be-changed'
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Strict'
    
    # 生产环境性能配置
    SEND_FILE_MAX_AGE_DEFAULT = timedelta(hours=1)
    
    # 生产环境错误处理
    PROPAGATE_EXCEPTIONS = False


class StagingConfig(ProductionConfig):
    """预发布环境配置"""
    DEBUG = False
    TESTING = False
    
    # 预发布环境数据库
    DATABASE_PATH = os.environ.get('STAGING_DATABASE_URL') or os.path.join(Config.BASE_DIR, 'instance', 'words_staging.db')
    
    # 预发布环境日志
    LOG_LEVEL = 'DEBUG'
    LOG_FILE = 'logs/app_staging.log'
    
    # 预发布环境安全配置（相对宽松）
    SESSION_COOKIE_SECURE = False


# 配置映射
config_map = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'staging': StagingConfig,
    'default': DevelopmentConfig
}


def get_config(config_name=None):
    """
    获取配置类
    
    Args:
        config_name: 配置名称，如果为None则从环境变量获取
        
    Returns:
        配置类
    """
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    return config_map.get(config_name, DevelopmentConfig)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_name=None):
        self.config_name = config_name or os.environ.get('FLASK_ENV', 'development')
        self.config_class = get_config(self.config_name)
    
    def get_config(self):
        """获取配置实例"""
        return self.config_class()
    
    def validate_config(self):
        """验证配置"""
        config = self.get_config()
        errors = []
        warnings = []
        
        # 检查必需的配置项
        required_attrs = ['SECRET_KEY', 'DATABASE_PATH']
        for attr in required_attrs:
            if not hasattr(config, attr) or not getattr(config, attr):
                errors.append(f"Missing required config: {attr}")
        
        # 检查生产环境安全配置
        if self.config_name == 'production':
            if config.SECRET_KEY == 'production-secret-key-must-be-changed':
                errors.append("Production SECRET_KEY must be changed")
            
            if not config.SESSION_COOKIE_SECURE:
                warnings.append("SESSION_COOKIE_SECURE should be True in production")
        
        # 检查数据库路径
        if config.DATABASE_PATH != ':memory:':
            db_dir = os.path.dirname(config.DATABASE_PATH)
            if not os.path.exists(db_dir):
                warnings.append(f"Database directory does not exist: {db_dir}")
        
        # 检查日志文件路径
        if hasattr(config, 'LOG_FILE') and config.LOG_FILE:
            log_dir = os.path.dirname(config.LOG_FILE)
            if log_dir and not os.path.exists(log_dir):
                warnings.append(f"Log directory does not exist: {log_dir}")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings,
            'config_name': self.config_name
        }
    
    def create_directories(self):
        """创建必要的目录"""
        config = self.get_config()
        
        # 创建数据库目录
        if config.DATABASE_PATH != ':memory:':
            db_dir = os.path.dirname(config.DATABASE_PATH)
            os.makedirs(db_dir, exist_ok=True)
        
        # 创建日志目录
        if hasattr(config, 'LOG_FILE') and config.LOG_FILE:
            log_dir = os.path.dirname(config.LOG_FILE)
            if log_dir:
                os.makedirs(log_dir, exist_ok=True)
    
    def get_environment_info(self):
        """获取环境信息"""
        config = self.get_config()
        
        return {
            'environment': self.config_name,
            'debug': config.DEBUG,
            'testing': config.TESTING,
            'database_path': config.DATABASE_PATH,
            'log_level': getattr(config, 'LOG_LEVEL', 'INFO'),
            'log_file': getattr(config, 'LOG_FILE', None),
            'secret_key_set': bool(config.SECRET_KEY),
            'session_secure': getattr(config, 'SESSION_COOKIE_SECURE', False)
        }


# 全局配置管理器实例
config_manager = ConfigManager()


def load_config_from_env():
    """从环境变量加载配置覆盖"""
    env_config = {}
    
    # Flask基础配置
    if os.environ.get('FLASK_SECRET_KEY'):
        env_config['SECRET_KEY'] = os.environ.get('FLASK_SECRET_KEY')
    
    if os.environ.get('FLASK_DEBUG'):
        env_config['DEBUG'] = os.environ.get('FLASK_DEBUG').lower() == 'true'
    
    # 数据库配置
    if os.environ.get('DATABASE_URL'):
        env_config['DATABASE_PATH'] = os.environ.get('DATABASE_URL')
    
    # 日志配置
    if os.environ.get('LOG_LEVEL'):
        env_config['LOG_LEVEL'] = os.environ.get('LOG_LEVEL')
    
    if os.environ.get('LOG_FILE'):
        env_config['LOG_FILE'] = os.environ.get('LOG_FILE')
    
    # 学习配置
    if os.environ.get('NEW_WORDS_COUNT'):
        try:
            env_config['NEW_WORDS_COUNT'] = int(os.environ.get('NEW_WORDS_COUNT'))
        except ValueError:
            pass
    
    if os.environ.get('REVIEW_WORDS_COUNT'):
        try:
            env_config['REVIEW_WORDS_COUNT'] = int(os.environ.get('REVIEW_WORDS_COUNT'))
        except ValueError:
            pass
    
    return env_config


def apply_env_config(app):
    """应用环境变量配置到Flask应用"""
    env_config = load_config_from_env()
    
    for key, value in env_config.items():
        app.config[key] = value
    
    return len(env_config)  # 返回应用的配置项数量
