"""
语言学专家推荐反馈API端点
支持用户对推荐内容的详细反馈收集
"""

from flask import Blueprint, request, jsonify, current_app
from datetime import datetime
import sqlite3
import json
import uuid

pattern_feedback_bp = Blueprint('pattern_feedback', __name__)

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(current_app.config['DATABASE'])
    conn.row_factory = sqlite3.Row
    return conn

@pattern_feedback_bp.route('/api/pattern/word_action', methods=['POST'])
def record_word_action():
    """记录单词级别的用户操作"""
    try:
        data = request.get_json()
        
        # 验证必需参数
        required_fields = ['action', 'word_id', 'target_word_id', 'session_id']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 插入单词操作记录
        cursor.execute('''
            INSERT INTO user_pattern_interactions 
            (interaction_id, user_id, word_id, interaction_type, interaction_data, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            str(uuid.uuid4()),
            1,  # 默认用户ID，实际应用中应从session获取
            data['target_word_id'],
            f"word_{data['action']}",
            json.dumps({
                'action': data['action'],
                'target_word_id': data['word_id'],
                'session_id': data['session_id'],
                'timestamp': datetime.now().isoformat()
            }),
            datetime.now()
        ))
        
        # 更新单词交互统计
        cursor.execute('''
            UPDATE words SET 
                interaction_count = COALESCE(interaction_count, 0) + 1,
                last_interaction = ?
            WHERE id = ?
        ''', (datetime.now(), data['word_id']))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Word action recorded successfully'
        })
        
    except Exception as e:
        current_app.logger.error(f"Error recording word action: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@pattern_feedback_bp.route('/api/pattern/group_feedback', methods=['POST'])
def record_group_feedback():
    """记录组级别的用户反馈"""
    try:
        data = request.get_json()
        
        # 验证必需参数
        required_fields = ['feedback_type', 'group_type', 'target_word_id', 'session_id']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 插入组反馈记录
        cursor.execute('''
            INSERT INTO user_pattern_interactions 
            (interaction_id, user_id, word_id, interaction_type, interaction_data, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            str(uuid.uuid4()),
            1,  # 默认用户ID
            data['target_word_id'],
            f"group_{data['feedback_type']}",
            json.dumps({
                'feedback_type': data['feedback_type'],
                'group_type': data['group_type'],
                'session_id': data['session_id'],
                'timestamp': datetime.now().isoformat()
            }),
            datetime.now()
        ))
        
        # 如果是负面反馈，记录到改进建议表
        if data['feedback_type'] == 'not_helpful':
            cursor.execute('''
                INSERT OR IGNORE INTO recommendation_improvements 
                (word_id, group_type, feedback_type, created_at)
                VALUES (?, ?, ?, ?)
            ''', (
                data['target_word_id'],
                data['group_type'],
                'group_not_helpful',
                datetime.now()
            ))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Group feedback recorded successfully'
        })
        
    except Exception as e:
        current_app.logger.error(f"Error recording group feedback: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@pattern_feedback_bp.route('/api/pattern/overall_feedback', methods=['POST'])
def record_overall_feedback():
    """记录整体推荐质量反馈"""
    try:
        data = request.get_json()
        
        # 验证必需参数
        required_fields = ['rating', 'target_word_id', 'session_id']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # 验证评分范围
        if data['rating'] not in ['excellent', 'good', 'neutral', 'poor']:
            return jsonify({'error': 'Invalid rating value'}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 插入整体反馈记录
        cursor.execute('''
            INSERT INTO user_pattern_interactions 
            (interaction_id, user_id, word_id, interaction_type, interaction_data, created_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            str(uuid.uuid4()),
            1,  # 默认用户ID
            data['target_word_id'],
            f"overall_{data['rating']}",
            json.dumps({
                'rating': data['rating'],
                'recommendations_count': data.get('recommendations_count', 0),
                'session_id': data['session_id'],
                'timestamp': datetime.now().isoformat()
            }),
            datetime.now()
        ))
        
        # 更新推荐质量统计
        cursor.execute('''
            INSERT OR REPLACE INTO recommendation_quality_stats 
            (word_id, total_ratings, excellent_count, good_count, neutral_count, poor_count, last_updated)
            VALUES (
                ?, 
                COALESCE((SELECT total_ratings FROM recommendation_quality_stats WHERE word_id = ?), 0) + 1,
                COALESCE((SELECT excellent_count FROM recommendation_quality_stats WHERE word_id = ?), 0) + ?,
                COALESCE((SELECT good_count FROM recommendation_quality_stats WHERE word_id = ?), 0) + ?,
                COALESCE((SELECT neutral_count FROM recommendation_quality_stats WHERE word_id = ?), 0) + ?,
                COALESCE((SELECT poor_count FROM recommendation_quality_stats WHERE word_id = ?), 0) + ?,
                ?
            )
        ''', (
            data['target_word_id'],
            data['target_word_id'],
            data['target_word_id'],
            1 if data['rating'] == 'excellent' else 0,
            data['target_word_id'],
            1 if data['rating'] == 'good' else 0,
            data['target_word_id'],
            1 if data['rating'] == 'neutral' else 0,
            data['target_word_id'],
            1 if data['rating'] == 'poor' else 0,
            datetime.now()
        ))
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Overall feedback recorded successfully'
        })
        
    except Exception as e:
        current_app.logger.error(f"Error recording overall feedback: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@pattern_feedback_bp.route('/api/pattern/feedback_stats/<int:word_id>', methods=['GET'])
def get_feedback_stats(word_id):
    """获取指定单词的反馈统计"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取反馈统计
        cursor.execute('''
            SELECT * FROM recommendation_quality_stats 
            WHERE word_id = ?
        ''', (word_id,))
        
        stats = cursor.fetchone()
        
        if stats:
            result = {
                'word_id': word_id,
                'total_ratings': stats['total_ratings'],
                'excellent_count': stats['excellent_count'],
                'good_count': stats['good_count'],
                'neutral_count': stats['neutral_count'],
                'poor_count': stats['poor_count'],
                'average_score': (
                    stats['excellent_count'] * 4 + 
                    stats['good_count'] * 3 + 
                    stats['neutral_count'] * 2 + 
                    stats['poor_count'] * 1
                ) / max(stats['total_ratings'], 1),
                'last_updated': stats['last_updated']
            }
        else:
            result = {
                'word_id': word_id,
                'total_ratings': 0,
                'excellent_count': 0,
                'good_count': 0,
                'neutral_count': 0,
                'poor_count': 0,
                'average_score': 0,
                'last_updated': None
            }
        
        conn.close()
        return jsonify(result)
        
    except Exception as e:
        current_app.logger.error(f"Error getting feedback stats: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
