"""
API性能监控模块
为前端集成提供实时性能监控和优化建议
"""
import time
import functools
from datetime import datetime
from typing import Dict, Any, List
import json


class APIPerformanceMonitor:
    """API性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.call_history = []
        self.max_history = 1000
    
    def record_api_call(self, endpoint: str, duration: float, 
                       success: bool, user_id: int = None):
        """记录API调用性能"""
        timestamp = datetime.now()
        
        # 记录到历史
        call_record = {
            'endpoint': endpoint,
            'duration': duration,
            'success': success,
            'user_id': user_id,
            'timestamp': timestamp.isoformat()
        }
        
        self.call_history.append(call_record)
        
        # 保持历史记录在限制内
        if len(self.call_history) > self.max_history:
            self.call_history = self.call_history[-self.max_history:]
        
        # 更新指标
        if endpoint not in self.metrics:
            self.metrics[endpoint] = {
                'total_calls': 0,
                'successful_calls': 0,
                'total_duration': 0.0,
                'min_duration': float('inf'),
                'max_duration': 0.0,
                'last_call': None
            }
        
        metric = self.metrics[endpoint]
        metric['total_calls'] += 1
        if success:
            metric['successful_calls'] += 1
        
        metric['total_duration'] += duration
        metric['min_duration'] = min(metric['min_duration'], duration)
        metric['max_duration'] = max(metric['max_duration'], duration)
        metric['last_call'] = timestamp.isoformat()
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {}
        
        for endpoint, metric in self.metrics.items():
            if metric['total_calls'] > 0:
                avg_duration = metric['total_duration'] / metric['total_calls']
                success_rate = metric['successful_calls'] / metric['total_calls']
                
                summary[endpoint] = {
                    'total_calls': metric['total_calls'],
                    'success_rate': round(success_rate * 100, 2),
                    'avg_duration_ms': round(avg_duration * 1000, 2),
                    'min_duration_ms': round(metric['min_duration'] * 1000, 2),
                    'max_duration_ms': round(metric['max_duration'] * 1000, 2),
                    'last_call': metric['last_call']
                }
        
        return summary
    
    def get_real_time_status(self) -> Dict[str, Any]:
        """获取实时状态"""
        now = datetime.now()
        recent_calls = [
            call for call in self.call_history
            if (now - datetime.fromisoformat(call['timestamp'])).seconds < 300  # 5分钟内
        ]
        
        total_recent = len(recent_calls)
        successful_recent = sum(1 for call in recent_calls if call['success'])
        
        if total_recent > 0:
            recent_success_rate = successful_recent / total_recent
            recent_avg_duration = sum(call['duration'] for call in recent_calls) / total_recent
        else:
            recent_success_rate = 1.0
            recent_avg_duration = 0.0
        
        return {
            'timestamp': now.isoformat(),
            'recent_calls_5min': total_recent,
            'recent_success_rate': round(recent_success_rate * 100, 2),
            'recent_avg_duration_ms': round(recent_avg_duration * 1000, 2),
            'total_endpoints': len(self.metrics),
            'system_health': 'excellent' if recent_success_rate > 0.95 else 'good' if recent_success_rate > 0.9 else 'warning'
        }
    
    def get_optimization_suggestions(self) -> List[str]:
        """获取优化建议"""
        suggestions = []
        
        for endpoint, metric in self.metrics.items():
            if metric['total_calls'] > 0:
                avg_duration = metric['total_duration'] / metric['total_calls']
                success_rate = metric['successful_calls'] / metric['total_calls']
                
                # 性能建议
                if avg_duration > 0.5:  # 500ms
                    suggestions.append(f"⚠️ {endpoint}: 平均响应时间较慢 ({avg_duration*1000:.0f}ms)，建议优化")
                elif avg_duration > 0.2:  # 200ms
                    suggestions.append(f"💡 {endpoint}: 响应时间可优化 ({avg_duration*1000:.0f}ms)")
                else:
                    suggestions.append(f"✅ {endpoint}: 性能良好 ({avg_duration*1000:.0f}ms)")
                
                # 可靠性建议
                if success_rate < 0.95:
                    suggestions.append(f"🔧 {endpoint}: 成功率需要改进 ({success_rate*100:.1f}%)")
                else:
                    suggestions.append(f"✅ {endpoint}: 可靠性优秀 ({success_rate*100:.1f}%)")
        
        return suggestions


# 全局性能监控器实例
performance_monitor = APIPerformanceMonitor()


def monitor_performance(endpoint_name: str):
    """性能监控装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            success = False
            
            try:
                result = func(*args, **kwargs)
                success = True
                return result
            except Exception as e:
                raise e
            finally:
                duration = time.time() - start_time
                
                # 尝试获取user_id
                user_id = None
                if 'user_id' in kwargs:
                    user_id = kwargs['user_id']
                elif len(args) > 0 and hasattr(args[0], 'user_id'):
                    user_id = args[0].user_id
                
                performance_monitor.record_api_call(
                    endpoint_name, duration, success, user_id
                )
        
        return wrapper
    return decorator


def get_api_health_report() -> Dict[str, Any]:
    """获取API健康报告"""
    return {
        'performance_summary': performance_monitor.get_performance_summary(),
        'real_time_status': performance_monitor.get_real_time_status(),
        'optimization_suggestions': performance_monitor.get_optimization_suggestions(),
        'monitoring_active': True
    }


def log_frontend_integration_event(event_type: str, details: Dict[str, Any]):
    """记录前端集成事件"""
    timestamp = datetime.now().isoformat()
    
    event_record = {
        'timestamp': timestamp,
        'event_type': event_type,
        'details': details
    }
    
    # 这里可以记录到日志文件或数据库
    print(f"🔗 前端集成事件: {event_type} - {timestamp}")
    print(f"   详情: {json.dumps(details, ensure_ascii=False, indent=2)}")


# 为现有API添加性能监控
def add_monitoring_to_existing_apis():
    """为现有API添加性能监控"""
    # 这个函数可以用来包装现有的API函数
    pass
