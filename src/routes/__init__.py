"""
Routes package - 路由层
提供模块化的路由管理，支持向后兼容和新模块化架构
"""

# 新模块化导入 - 推荐使用
from .registry import register_all_routes, get_route_info, validate_routes, ROUTE_MODULES

# 各个路由模块的独立导入
from .page_routes import register_page_routes
from .auth_routes import register_auth_routes
from .learning_routes import register_learning_routes
from .admin_routes import register_admin_routes
from .api_routes import register_api_routes

__all__ = [
    # 路由注册中心
    'register_all_routes',
    'get_route_info',
    'validate_routes',
    'ROUTE_MODULES',
    
    # 各个路由模块
    'register_page_routes',
    'register_auth_routes', 
    'register_learning_routes',
    'register_admin_routes',
    'register_api_routes'
]

# 版本信息
__version__ = '2.0.0'  # 路由层重构版本

# 模块信息
__modules__ = {
    'page_routes': 'src.routes.page_routes',
    'auth_routes': 'src.routes.auth_routes',
    'learning_routes': 'src.routes.learning_routes', 
    'admin_routes': 'src.routes.admin_routes',
    'api_routes': 'src.routes.api_routes',
    'registry': 'src.routes.registry'
}

# 路由统计信息
__route_stats__ = {
    'total_modules': 5,
    'total_routes': 71,
    'page_routes': 22,
    'api_routes': 49,
    'auth_routes': 6,
    'learning_routes': 20,
    'admin_routes': 8,
    'general_api_routes': 15
}
