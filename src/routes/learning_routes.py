"""
学习路由模块
处理学习功能相关的API路由，包括学习计划、答题、生词本、辅助功能等
严格按照API_CONTRACT.md规范实现标准化响应格式
"""
import os
from datetime import date
from flask import request, jsonify, session

from ..models import User, Word, UserWord, WordRecord
from ..services import LearningPlanService, LearningService, StatisticsService
from ..services.learning.session_service import LearningSessionService
from ..services.learning.rewards_service import RewardsSystemService
from ..config import Config
from ..api import APIResponse, APIErrorCodes, APIMessages, api_response, require_auth


def register_learning_routes(app):
    """注册学习路由"""

    # ===== 学习会话管理API (减轻前端负载) =====

    @app.route('/api/learning_session/start', methods=['POST'])
    @require_auth
    @api_response
    def api_start_learning_session():
        """开始学习会话 - 后端处理所有初始化逻辑"""
        data = request.get_json() or {}
        user_id = session['user_id']
        session_date = data.get('date')

        try:
            if session_date:
                session_date = date.fromisoformat(session_date)
            else:
                session_date = date.today()
        except ValueError:
            return APIResponse.json_error(
                APIErrorCodes.VALIDATION_ERROR,
                "日期格式错误",
                {"date": session_date}
            )

        # 启动学习会话
        result = LearningSessionService.start_learning_session(
            user_id, session_date
        )

        if result.get('success'):
            return APIResponse.json_success(
                data=result,
                message="学习会话启动成功"
            )
        else:
            return APIResponse.json_error(
                APIErrorCodes.BUSINESS_LOGIC_ERROR,
                result.get('error', '启动学习会话失败'),
                {"user_id": user_id, "date": session_date.strftime('%Y-%m-%d')}
            )

    @app.route('/api/learning_session/submit_and_advance', methods=['POST'])
    @require_auth
    @api_response
    def api_submit_answer_and_advance():
        """提交答案并推进到下一个状态 - 原子操作"""
        data = request.get_json()
        if not data:
            return APIResponse.json_error(
                APIErrorCodes.INVALID_DATA_FORMAT,
                "请求数据不能为空"
            )

        user_id = session['user_id']
        session_id = data.get('session_id')
        answer_data = {
            'user_input': data.get('user_input', ''),
            'duration_seconds': data.get('duration_seconds', 0.0)
        }

        if not session_id:
            return APIResponse.json_error(
                APIErrorCodes.VALIDATION_ERROR,
                "缺少会话ID",
                {"required_field": "session_id"}
            )

        # 提交答案并推进状态
        result = LearningSessionService.submit_answer_and_advance(
            session_id, user_id, answer_data
        )

        if result.get('success'):
            return APIResponse.json_success(
                data=result,
                message="答案提交成功"
            )
        else:
            return APIResponse.json_error(
                APIErrorCodes.BUSINESS_LOGIC_ERROR,
                result.get('error', '答案提交失败'),
                {"session_id": session_id, "user_id": user_id}
            )

    # ===== 奖励系统API (减轻前端负载) =====

    @app.route('/api/rewards_system/calculate', methods=['POST'])
    @require_auth
    @api_response
    def api_calculate_rewards():
        """计算学习会话奖励 - 后端处理所有奖励逻辑"""
        data = request.get_json()
        if not data:
            return APIResponse.json_error(
                APIErrorCodes.INVALID_DATA_FORMAT,
                "请求数据不能为空"
            )

        user_id = session['user_id']
        learning_result = data.get('learning_result', {})

        # 验证必需字段
        required_fields = ['words_completed', 'correct_count']
        missing_fields = [field for field in required_fields
                         if field not in learning_result]

        if missing_fields:
            return APIResponse.json_error(
                APIErrorCodes.VALIDATION_ERROR,
                f"缺少必需字段: {', '.join(missing_fields)}",
                {"missing_fields": missing_fields}
            )

        # 计算奖励
        result = RewardsSystemService.calculate_session_rewards(
            user_id, learning_result
        )

        if result.get('success'):
            return APIResponse.json_success(
                data=result,
                message="奖励计算成功"
            )
        else:
            return APIResponse.json_error(
                APIErrorCodes.BUSINESS_LOGIC_ERROR,
                result.get('error', '奖励计算失败'),
                {"user_id": user_id, "learning_result": learning_result}
            )

    @app.route('/api/rewards_system/purchase_check', methods=['POST'])
    @require_auth
    @api_response
    def api_check_purchase_eligibility():
        """检查购买资格 - 后端验证积分和资格"""
        data = request.get_json()
        if not data:
            return APIResponse.json_error(
                APIErrorCodes.INVALID_DATA_FORMAT,
                "请求数据不能为空"
            )

        user_id = session['user_id']
        item_type = data.get('item_type', '')
        cost = data.get('cost', 0)

        if not item_type or cost <= 0:
            return APIResponse.json_error(
                APIErrorCodes.VALIDATION_ERROR,
                "物品类型和费用必须有效",
                {"item_type": item_type, "cost": cost}
            )

        # 检查购买资格
        result = RewardsSystemService.check_purchase_eligibility(
            user_id, item_type, cost
        )

        if result.get('success'):
            return APIResponse.json_success(
                data=result,
                message="购买资格检查完成"
            )
        else:
            return APIResponse.json_error(
                APIErrorCodes.BUSINESS_LOGIC_ERROR,
                result.get('error', '购买资格检查失败'),
                {"user_id": user_id, "item_type": item_type}
            )

    @app.route('/api/rewards_system/summary')
    @require_auth
    @api_response
    def api_get_rewards_summary():
        """获取用户奖励摘要 - 后端提供完整状态"""
        user_id = session['user_id']

        # 获取奖励摘要
        result = RewardsSystemService.get_user_rewards_summary(user_id)

        if result.get('success'):
            return APIResponse.json_success(
                data=result['summary'],
                message="奖励摘要获取成功"
            )
        else:
            return APIResponse.json_error(
                APIErrorCodes.BUSINESS_LOGIC_ERROR,
                result.get('error', '获取奖励摘要失败'),
                {"user_id": user_id}
            )

    # ===== 学习计划API =====

    # 注意: /api/daily_learning_plan 路由已在 api_routes.py 中定义
    # 这里移除重复定义以避免冲突

    @app.route('/get_daily_words')
    def get_daily_words():
        """获取每日学习单词（兼容旧接口）"""
        if 'user_id' not in session:
            return jsonify({'error': '请先登录'})

        user_id = session['user_id']
        plan = LearningPlanService.get_daily_plan(user_id)

        # 转换为旧格式
        words = []
        for item in plan:
            # 获取单词的详细统计信息包括熟练度百分比
            user_word_relation = UserWord.get_by_user_and_word(user_id, item['word_id'])
            word_stats = WordRecord.get_user_word_stats(user_id, item['word_id'])

            # 计算长期熟练度百分比
            long_term_proficiency = 0
            if user_word_relation and user_word_relation['proficiency'] is not None:
                long_term_proficiency = float(user_word_relation['proficiency'])

            # 计算准确率
            accuracy_rate = 0
            if word_stats['learning_count'] > 0:
                accuracy_rate = round((word_stats['correct_count'] / word_stats['learning_count']) * 100, 1)

            # 调试代码已移除

            words.append({
                'id': item['word_id'],
                'word': item['chinese_meaning'],
                'answer': item['english_word'],
                'section': item['section'],
                'star_level': item['star_level'],  # 🔧 统一使用star_level字段
                'item_type': item['item_type'],
                'long_term_proficiency': long_term_proficiency,  # 熟练度百分比（0-100）
                'is_new': item['item_type'] == 'new',
                'accuracy_rate': accuracy_rate,  # 历史正确率
                'learn_count': word_stats['learning_count'],  # 学习次数
                'last_learned': word_stats['last_learning_date'] or '未学习'  # 最近学习日期
            })

        # 返回前端期望的格式
        return jsonify({
            'daily_words': words,
            'total_count': len(words)
        })

    # ===== 学习API =====

    @app.route('/update_record', methods=['POST'])
    def update_record():
        """更新学习记录API（兼容旧接口）"""
        try:
            if 'user_id' not in session:
                return jsonify({'success': False, 'message': '请先登录'})

            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'message': '请求数据格式错误'})

            user_id = session['user_id']

            # 获取学习数据
            word_text = data.get('word', '')
            user_input = data.get('user_input', '')
            duration_seconds = float(data.get('duration_seconds', 0.0))

            # 查找单词ID
            word_id = data.get('word_id')
            if not word_id and word_text:
                word_obj = Word.search(word_text)
                if word_obj:
                    for w in word_obj:
                        if w['chinese_meaning'] == word_text:
                            word_id = w['id']
                            break

            # 友好的学习提交日志
            if word_id:
                print(f"📝 学习提交: {word_text} → {user_input} (用时: {duration_seconds:.1f}秒)")
            else:
                print(f"❌ 单词查找失败: 未找到 '{word_text}'")

            if not word_id:
                return jsonify({'success': False, 'message': '缺少单词ID或无法找到对应单词'})

            result = LearningService.submit_answer(
                user_id, word_id, user_input, duration_seconds
            )

            # 格式化输出学习结果
            if result.get('success'):
                status = "✅ 正确" if result.get('is_correct') else "❌ 错误"
                answer = result.get('correct_answer', '未知')
                star_level = result.get('new_star_level', 0)
                points = result.get('points_change', 0)
                points_text = f"+{points}" if points > 0 else str(points)
                print(f"📚 学习记录: {status} | 答案: {answer} | 星级: {star_level} | 积分: {points_text}")
            else:
                print(f"❌ 学习记录更新失败: {result.get('message', '未知错误')}")
            return jsonify(result)

        except Exception as e:
            print(f"update_record error: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'message': f'服务器错误: {str(e)}'
            })

    @app.route('/api/submit_answer', methods=['POST'])
    @require_auth
    @api_response
    def api_submit_answer():
        """
        提交答案API - 标准化版本
        严格按照API_CONTRACT.md规范实现
        """
        data = request.get_json()
        if not data:
            return APIResponse.json_error(
                APIErrorCodes.INVALID_DATA_FORMAT,
                "请求数据不能为空"
            )

        user_id = session['user_id']
        word_id = data.get('word_id')
        user_input = data.get('user_input', '')
        duration_seconds = data.get('duration_seconds', 0.0)
        plan_date = data.get('plan_date', str(date.today()))

        # 验证必需字段
        if not word_id:
            return APIResponse.json_error(
                APIErrorCodes.MISSING_REQUIRED_FIELD,
                "缺少必需字段: word_id",
                {"missing_field": "word_id"}
            )

        try:
            # 🔧 修复跨日期问题：解析并传递计划日期
            from datetime import datetime
            try:
                parsed_plan_date = datetime.strptime(plan_date, '%Y-%m-%d').date()
            except ValueError:
                parsed_plan_date = date.today()
                print(f"⚠️ 日期解析失败，使用今天: {plan_date} -> {parsed_plan_date}")

            print(f"📅 提交答案使用日期: {parsed_plan_date}")

            # 调用学习服务提交答案
            result = LearningService.submit_answer(
                user_id, word_id, user_input, duration_seconds, parsed_plan_date
            )

            if not result.get('success'):
                return APIResponse.json_error(
                    APIErrorCodes.BUSINESS_LOGIC_ERROR,
                    result.get('message', '答案提交失败'),
                    {"word_id": word_id, "user_input": user_input}
                )

            # 返回标准化响应，包含连击数和购物券信息
            return APIResponse.json_success(
                data={
                    'is_correct': result.get('is_correct', False),
                    'correct_answer': result.get('correct_answer', ''),
                    'new_star_level': result.get('new_star_level', 0),
                    'points_change': result.get('points_change', 0),
                    'new_points_total': result.get('new_points_total', 0),
                    'consecutive_correct': result.get('consecutive_correct', 0),  # 添加连击数
                    'voucher_result': result.get('voucher_result', {})  # 添加购物券信息
                },
                message=APIMessages.ANSWER_SUBMITTED
            )

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.BUSINESS_LOGIC_ERROR,
                f"答案提交失败: {str(e)}",
                {"word_id": word_id, "user_input": user_input}
            )

    @app.route('/api/user_status')
    @require_auth
    @api_response
    def api_user_status():
        """
        获取用户状态API - 新建标准化端点
        为前端状态管理器提供完整的用户状态数据
        """
        user_id = session['user_id']

        try:
            # 获取用户基本信息
            user = User.get_by_id(user_id)
            if not user:
                return APIResponse.json_error(
                    APIErrorCodes.INVALID_USER_ID,
                    "用户不存在",
                    {"user_id": user_id}
                )

            # 获取今日学习计划
            today_plan = LearningPlanService.get_daily_plan(user_id)

            # 计算学习进度
            completed_words = 0
            total_words = len(today_plan)

            for item in today_plan:
                # 简单判断：星级达到5星认为已完成
                if item.get('star_level', 0) >= 5:
                    completed_words += 1

            completion_rate = (completed_words / max(total_words, 1))

            # 获取今日购物券状态 (简化实现)
            daily_voucher_earned = session.get(
                f'daily_vouchers_{user_id}_{date.today().strftime("%Y-%m-%d")}', 0
            )
            daily_voucher_limit = 2

            # 返回标准化响应
            return APIResponse.json_success(
                data={
                    'user': {
                        'points': user['points'],
                        'vouchers': user['vouchers'],
                        'daily_voucher_earned': daily_voucher_earned,
                        'daily_voucher_limit': daily_voucher_limit
                    },
                    'learning': {
                        'daily_words': total_words,
                        'completed_words': completed_words,
                        'current_progress': {
                            'completed': completed_words,
                            'total': total_words,
                            'completion_rate': round(completion_rate, 3)
                        }
                    }
                },
                message="用户状态获取成功"
            )

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_SERVER_ERROR,
                f"获取用户状态失败: {str(e)}",
                {"user_id": user_id}
            )

    @app.route('/api/finish_learning', methods=['POST'])
    @require_auth
    @api_response
    def api_finish_learning():
        """结束学习会话API - 标准化版本"""
        user_id = session['user_id']

        try:
            result = LearningService.finish_learning_session(user_id)

            return APIResponse.json_success(
                data=result,
                message="学习会话结束成功"
            )

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.BUSINESS_LOGIC_ERROR,
                f"结束学习会话失败: {str(e)}",
                {"user_id": user_id}
            )

    # ===== 生词本API =====


    # 注意: /api/add_to_vocabulary 路由已在 api_routes.py 中定义
    # 保留兼容性路由
    @app.route('/add_to_vocabulary', methods=['POST'])
    def add_to_vocabulary():
        """添加到生词本API（兼容旧接口）"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.get_json()
        user_id = session['user_id']
        word_id = data.get('word_id')

        # 兼容前端传递word（中文含义）的情况
        if not word_id:
            word_text = data.get('word')  # 前端传递的中文含义
            if word_text:
                # 通过中文含义查找单词ID
                word_obj = Word.search(word_text)
                if word_obj:
                    for w in word_obj:
                        if w['chinese_meaning'] == word_text:
                            word_id = w['id']
                            break

        if not word_id:
            return jsonify({'success': False, 'message': '缺少单词ID或无法找到对应单词'})

        success = LearningService.add_to_vocabulary_book(user_id, word_id)

        if success:
            return jsonify({'success': True, 'message': '已添加到生词本'})
        else:
            return jsonify({'success': False, 'message': '添加失败'})

    @app.route('/api/vocabulary_book')
    def api_vocabulary_book():
        """获取生词本API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        user_id = session['user_id']
        words = StatisticsService.get_vocabulary_book(user_id)

        return jsonify({'success': True, 'words': words})

    @app.route('/api/remove_from_vocabulary', methods=['POST'])
    def api_remove_from_vocabulary():
        """从生词本移除API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.get_json()
        user_id = session['user_id']
        word_id = data.get('word_id')

        # 兼容前端传递word（中文含义）的情况
        if not word_id:
            word_text = data.get('word')  # 前端传递的中文含义
            if word_text:
                # 通过中文含义查找单词ID
                word_obj = Word.search(word_text)
                if word_obj:
                    for w in word_obj:
                        if w['chinese_meaning'] == word_text:
                            word_id = w['id']
                            break

        if not word_id:
            return jsonify({'success': False, 'message': '缺少单词ID或无法找到对应单词'})

        success = LearningService.remove_from_vocabulary_book(user_id, word_id)

        if success:
            return jsonify({'success': True, 'message': '已从生词本移除'})
        else:
            return jsonify({'success': False, 'message': '移除失败'})

    # ===== 辅助功能API =====

    @app.route('/api/get_memory_aid', methods=['POST'])
    def api_get_memory_aid():
        """获取记忆帮助API - 已废弃，请使用 /api/learning_session/memory_help"""
        # 添加废弃警告
        import warnings
        warnings.warn("API /api/get_memory_aid 已废弃，请使用 /api/learning_session/memory_help",
                     DeprecationWarning, stacklevel=2)

        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录',
                          'deprecated': True, 'new_api': '/api/learning_session/memory_help'})

        data = request.get_json()
        word_id = data.get('word_id')

        if not word_id:
            return jsonify({'success': False, 'message': '缺少单词ID'})

        # 获取单词信息
        word = Word.get_by_id(word_id)
        if not word:
            return jsonify({'success': False, 'message': '单词不存在'})

        # 生成记忆帮助
        english_word = word['english_word']
        chinese_meaning = word['chinese_meaning']

        # 简单的记忆帮助生成逻辑
        memory_aids = []

        # 1. 长度提示
        memory_aids.append(f"单词长度：{len(english_word)}个字母")

        # 2. 首尾字母提示
        if len(english_word) > 1:
            memory_aids.append(f"首字母：{english_word[0].upper()}，尾字母：{english_word[-1].upper()}")

        # 3. 音节提示（简单估算）
        vowels = 'aeiouAEIOU'
        vowel_count = sum(1 for char in english_word if char in vowels)
        memory_aids.append(f"大约{max(1, vowel_count)}个音节")

        # 4. 词性提示（基于section）
        section = word['section'] if word['section'] else ''
        if section:
            memory_aids.append(f"来源：{section}")

        return jsonify({
            'success': True,
            'word': english_word,
            'meaning': chinese_meaning,
            'memory_aids': memory_aids
        })

    @app.route('/api/skip_word', methods=['POST'])
    def api_skip_word():
        """跳过单词API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.get_json()
        word_id = data.get('word_id')

        if not word_id:
            return jsonify({'success': False, 'message': '缺少单词ID'})

        # 跳过单词：记录为错误答案，但不扣分
        user_id = session['user_id']

        # 获取单词信息
        word = Word.get_by_id(word_id)
        if not word:
            return jsonify({'success': False, 'message': '单词不存在'})

        # 记录跳过（作为错误答案处理，但用时为0）
        WordRecord.create(
            user_id, word_id, 0.0,
            '[SKIPPED]', word['english_word'], False
        )

        return jsonify({
            'success': True,
            'message': '已跳过该单词',
            'correct_answer': word['english_word']
        })

    @app.route('/api/mark_difficult', methods=['POST'])
    def api_mark_difficult():
        """标记为困难单词API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.get_json()
        word_id = data.get('word_id')

        if not word_id:
            return jsonify({'success': False, 'message': '缺少单词ID'})

        user_id = session['user_id']

        # 添加到生词本（attention状态）
        success = LearningService.add_to_vocabulary_book(user_id, word_id)

        if success:
            return jsonify({'success': True, 'message': '已标记为困难单词并添加到生词本'})
        else:
            return jsonify({'success': False, 'message': '标记失败'})

    # ===== 音频相关API =====

    @app.route('/api/word_audio/<int:word_id>')
    def api_word_audio(word_id):
        """获取单词音频API"""
        # 获取单词信息
        word = Word.get_by_id(word_id)
        if not word:
            return jsonify({'success': False, 'message': '单词不存在'})

        english_word = word['english_word']

        # 检查音频文件是否存在
        audio_filename = f"{english_word.lower()}.mp3"
        audio_path = os.path.join(app.static_folder, 'audio', 'words', audio_filename)

        if os.path.exists(audio_path):
            audio_url = f"/static/audio/words/{audio_filename}"
            return jsonify({
                'success': True,
                'audio_url': audio_url,
                'word': english_word
            })
        else:
            return jsonify({
                'success': False,
                'message': '音频文件不存在',
                'word': english_word
            })

    @app.route('/api/generate_audio', methods=['POST'])
    def api_generate_audio():
        """智能音频生成API - 当音频不存在时自动生成"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        try:
            data = request.get_json()
            english_word = data.get('word', '').strip()
            
            if not english_word:
                return jsonify({'success': False, 'message': '缺少单词参数'})

            # 处理文件名：替换空格为下划线，转为小写
            safe_filename = english_word.lower().replace(' ', '_')
            # 移除特殊字符
            safe_filename = ''.join(c for c in safe_filename if c.isalnum() or c in ['_', '-'])
            
            audio_filename = f"{safe_filename}.mp3"
            audio_path = os.path.join(app.static_folder, 'audio', 'words', audio_filename)
            
            # 检查文件是否已存在且有效（大小大于1KB）
            if os.path.exists(audio_path) and os.path.getsize(audio_path) > 1024:
                audio_url = f"/static/audio/words/{audio_filename}"
                return jsonify({
                    'success': True,
                    'audio_url': audio_url,
                    'word': english_word,
                    'generated': False,
                    'message': '音频文件已存在'
                })

            # 调用TTS API生成音频
            tts_success = _generate_tts_audio(english_word, audio_path)
            
            if tts_success:
                audio_url = f"/static/audio/words/{audio_filename}"
                return jsonify({
                    'success': True,
                    'audio_url': audio_url,
                    'word': english_word,
                    'generated': True,
                    'message': '音频生成成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'TTS音频生成失败',
                    'word': english_word
                })
                
        except Exception as e:
            app.logger.error(f"音频生成API异常: {e}")
            return jsonify({
                'success': False,
                'message': f'音频生成失败: {str(e)}'
            })

    @app.route('/api/generate_image', methods=['POST'])
    def api_generate_image():
        """智能图片生成API - 当图片不存在时自动生成"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        try:
            data = request.get_json()
            english_word = data.get('word', '').strip()
            chinese_meaning = data.get('meaning', '').strip()
            
            if not english_word:
                return jsonify({'success': False, 'message': '缺少单词参数'})

            # 处理文件名：替换空格为下划线，转为小写
            safe_filename = english_word.lower().replace(' ', '_')
            # 移除特殊字符
            safe_filename = ''.join(c for c in safe_filename if c.isalnum() or c in ['_', '-'])
            
            image_filename = f"{safe_filename}.jpg"
            image_path = os.path.join(app.static_folder, 'images', 'words', image_filename)
            
            # 检查文件是否已存在且有效（大小大于5KB）
            if os.path.exists(image_path) and os.path.getsize(image_path) > 5120:
                image_url = f"/static/images/words/{image_filename}"
                return jsonify({
                    'success': True,
                    'image_url': image_url,
                    'word': english_word,
                    'generated': False,
                    'message': '图片文件已存在'
                })

            # 调用AI图片生成API
            image_success = _generate_ai_image(english_word, chinese_meaning, image_path)
            
            if image_success:
                image_url = f"/static/images/words/{image_filename}"
                return jsonify({
                    'success': True,
                    'image_url': image_url,
                    'word': english_word,
                    'generated': True,
                    'message': '图片生成成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'AI图片生成失败',
                    'word': english_word
                })
                
        except Exception as e:
            app.logger.error(f"图片生成API异常: {e}")
            return jsonify({
                'success': False,
                'message': f'图片生成失败: {str(e)}'
            })

    # ===== 学习进度API =====

    @app.route('/api/learning_progress')
    def api_learning_progress():
        """获取学习进度API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        user_id = session['user_id']

        # 获取今日计划完成情况
        from datetime import date
        today_plan = LearningPlanService.get_daily_plan(user_id)
        completed_count = sum(1 for item in today_plan if item['star_level'] == 5)

        # 获取今日学习记录
        today_records = WordRecord.get_today_records(user_id)

        return jsonify({
            'success': True,
            'total_words': len(today_plan),
            'completed_words': completed_count,
            'completion_rate': (completed_count / len(today_plan) * 100) if today_plan else 0,
            'total_attempts': len(today_records),
            'correct_attempts': sum(1 for r in today_records if r['is_correct']),
            'accuracy_rate': (sum(1 for r in today_records if r['is_correct']) / len(today_records) * 100) if today_records else 0
        })

    @app.route('/api/word_details/<int:word_id>')
    def api_word_details(word_id):
        """获取单词详细信息API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        user_id = session['user_id']

        # 获取单词基本信息
        word = Word.get_by_id(word_id)
        if not word:
            return jsonify({'success': False, 'message': '单词不存在'})

        # 获取用户学习统计
        word_stats = WordRecord.get_user_word_stats(user_id, word_id)
        user_word = UserWord.get_by_user_and_word(user_id, word_id)

        return jsonify({
            'success': True,
            'word': {
                'id': word['id'],
                'english_word': word['english_word'],
                'chinese_meaning': word['chinese_meaning'],
                'section': word['section']
            },
            'stats': {
                'learning_count': word_stats['learning_count'],
                'correct_count': word_stats['correct_count'],
                'accuracy_rate': (word_stats['correct_count'] / word_stats['learning_count'] * 100) if word_stats['learning_count'] > 0 else 0,
                'avg_duration': word_stats['avg_duration'],
                'last_learning_date': word_stats['last_learning_date'],
                'proficiency': user_word['proficiency'] if user_word else 0,
                'status': user_word['status'] if user_word else 'new'
            }
        })

    # ===== 旧版本兼容API =====

    @app.route('/get_word')
    def get_word():
        """获取单词API（旧版本兼容）"""
        if 'user_id' not in session:
            return jsonify({'error': '请先登录'})

        user_id = session['user_id']

        # 获取今日学习计划
        plan = LearningPlanService.get_daily_plan(user_id)

        if not plan:
            return jsonify({'word': None, 'message': '学习完成'})

        # 获取第一个未完成的单词
        current_word = None
        for item in plan:
            if item['star_level'] < 5:  # 未完全掌握
                current_word = item
                break

        if not current_word:
            return jsonify({'word': None, 'message': '学习完成'})

        return jsonify({
            'word': current_word['chinese_meaning'],
            'answer': current_word['english_word'],
            'word_id': current_word['word_id'],
            'section': current_word['section'],
            'star_level': current_word['star_level']
        })

    @app.route('/check_spelling', methods=['POST'])
    def check_spelling():
        """检查拼写API（旧版本兼容）"""
        if 'user_id' not in session:
            return jsonify({'error': '请先登录'})

        data = request.get_json()
        word = data.get('word', '')  # 中文含义
        user_input = data.get('user_input', '')

        # 通过中文含义查找单词
        word_obj = Word.search(word)
        if not word_obj:
            return jsonify({'is_correct': False, 'message': '单词不存在'})

        # 找到匹配的单词
        target_word = None
        for w in word_obj:
            if w['chinese_meaning'] == word:
                target_word = w
                break

        if not target_word:
            return jsonify({'is_correct': False, 'message': '单词不存在'})

        # 检查拼写
        is_correct = user_input.lower().strip() == target_word['english_word'].lower()

        return jsonify({
            'is_correct': is_correct,
            'correct_answer': target_word['english_word'],
            'user_input': user_input
        })

    @app.route('/api/get_hint', methods=['POST'])
    def api_get_hint():
        """获取分层次掩码提示API - 已废弃，请使用 /api/learning_session/hint"""
        # 添加废弃警告
        import warnings
        warnings.warn("API /api/get_hint 已废弃，请使用 /api/learning_session/hint",
                     DeprecationWarning, stacklevel=2)

        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录',
                          'deprecated': True, 'new_api': '/api/learning_session/hint'})

        data = request.get_json()
        word = data.get('word', '')  # 中文含义
        hint_level = data.get('hint_level', 1)  # 提示级别 1-4

        print(f"🔍 提示API调用: word='{word}', hint_level={hint_level}")

        # 通过中文含义查找单词
        word_obj = Word.search(word)
        if not word_obj:
            return jsonify({'success': False, 'message': '单词不存在'})

        # 找到匹配的单词
        target_word = None
        for w in word_obj:
            if w['chinese_meaning'] == word:
                target_word = w
                break

        if not target_word:
            return jsonify({'success': False, 'message': '单词不存在'})

        english_word = target_word['english_word'].lower()
        word_length = len(english_word)

        # 🔧 修复：真正的渐进式随机掩码提示系统（5个级别，20%→40%→60%→80%→100%）
        import random

        if hint_level == 5:
            # 级别5：显示100%的字母（完整答案）
            hint = english_word
        elif hint_level in [1, 2, 3, 4]:
            # 计算要显示的字符数量
            percentages = {1: 0.2, 2: 0.4, 3: 0.6, 4: 0.8}
            show_chars = max(1, int(word_length * percentages[hint_level]))

            # 🔧 关键修复：为每个单词生成一致的随机种子，确保同一单词的提示是累积的
            # 使用单词内容作为种子，确保每次调用都产生相同的随机序列
            random.seed(hash(english_word) % 2147483647)

            # 生成所有字符位置的随机序列
            all_positions = list(range(word_length))
            random.shuffle(all_positions)

            # 选择前N个位置显示字符
            show_positions = set(all_positions[:show_chars])

            # 构建提示字符串
            hint = ''
            for i, char in enumerate(english_word):
                if i in show_positions:
                    hint += char
                else:
                    hint += '_'
        else:
            # 超过级别5，直接显示答案
            hint = english_word

        print(f"🔍 生成提示: '{english_word}' -> '{hint}' (级别{hint_level})")

        # 🔧 修复积分不足问题：先检查积分是否足够
        from ..models import User
        if 'user_id' in session:
            try:
                # 先获取当前用户积分
                user_info = User.get_by_id(session['user_id'])
                if not user_info:
                    return jsonify({'success': False, 'message': '用户不存在'})

                current_points = user_info['points'] or 0
                if current_points < 5:
                    return jsonify({
                        'success': False,
                        'message': f'积分不足！使用提示需要5积分，当前积分：{current_points}',
                        'current_points': current_points,
                        'required_points': 5
                    })

                # 积分足够，扣除积分
                User.update_points(session['user_id'], -5)

                return jsonify({
                    'success': True,
                    'hint': hint,
                    'hint_level': hint_level,
                    'max_level': 5,  # 🔧 修复：最大提示级别为5
                    'cost': 5,
                    'message': f'提示级别{hint_level}，扣除5积分'
                })

            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'获取提示失败：{str(e)}'
                })
        else:
            return jsonify({'success': False, 'message': '请先登录'})

    @app.route('/api/get_memory_help', methods=['POST'])
    def api_get_memory_help():
        """获取记忆帮助API（旧版本兼容）- 已废弃，请使用 /api/learning_session/memory_help"""
        # 添加废弃警告
        import warnings
        warnings.warn("API /api/get_memory_help 已废弃，请使用 /api/learning_session/memory_help",
                     DeprecationWarning, stacklevel=2)

        if 'user_id' not in session:
            return jsonify({'error': '请先登录',
                          'deprecated': True, 'new_api': '/api/learning_session/memory_help'})

        data = request.get_json()
        word = data.get('word', '')  # 中文含义

        # 通过中文含义查找单词
        word_obj = Word.search(word)
        if not word_obj:
            return jsonify({'memory_help': '单词不存在'})

        # 找到匹配的单词
        target_word = None
        for w in word_obj:
            if w['chinese_meaning'] == word:
                target_word = w
                break

        if not target_word:
            return jsonify({'memory_help': '单词不存在'})

        english_word = target_word['english_word']
        chinese_meaning = target_word['chinese_meaning']

        # 尝试从缓存文件中读取记忆帮助
        import os

        # 使用标准化的文件名格式（与前端保持一致）
        standard_filename = english_word.replace(" ", "_")
        possible_filenames = [
            f'{standard_filename}.txt',  # 标准化名称
            f'{standard_filename.lower()}.txt',  # 小写标准化名称
        ]

        cache_file = None
        for filename in possible_filenames:
            potential_path = os.path.join(app.static_folder, 'cache', 'memory_help', filename)
            if os.path.exists(potential_path):
                cache_file = potential_path
                break

        # 默认记忆帮助
        memory_help = f"""记忆帮助：{english_word} ({chinese_meaning})

1. 单词长度：{len(english_word)}个字母
2. 首字母：{english_word[0].upper()}
3. 建议多读几遍，联想相关场景
4. 可以尝试造句记忆"""

        if cache_file and os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    memory_help = f.read()
                print(f"✅ 成功读取缓存文件: {os.path.basename(cache_file)}")
            except Exception as e:
                print(f"❌ 读取缓存文件失败: {e}")
                # 保持默认记忆帮助
        else:
            print(f"⚠️ 未找到缓存文件，尝试的文件名: {possible_filenames}")

        return jsonify({
            'success': True,
            'memory_help': memory_help,  # 前端期望的字段名
            'source': 'cache' if cache_file else 'basic',
            'word': english_word,
            'meaning': chinese_meaning,
            'cache_file': os.path.basename(cache_file) if cache_file else None
        })

    @app.route('/api/get_memory_method', methods=['POST'])
    def api_get_memory_method():
        """获取用户记忆方法API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.get_json()
        word_id = data.get('word_id')

        if not word_id:
            return jsonify({'success': False, 'message': '缺少单词ID'})

        user_id = session['user_id']

        # 获取用户单词关系记录
        query = """
        SELECT memory_method FROM user_word
        WHERE user_id = ? AND word_id = ?
        """
        results = db.execute_query(query, (user_id, word_id))

        memory_method = ''
        if results and results[0]['memory_method']:
            memory_method = results[0]['memory_method']

        return jsonify({
            'success': True,
            'memory_method': memory_method
        })

    @app.route('/api/save_memory_method', methods=['POST'])
    def api_save_memory_method():
        """保存用户记忆方法API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})

        data = request.get_json()
        word_id = data.get('word_id')
        memory_method = data.get('memory_method', '').strip()

        if not word_id:
            return jsonify({'success': False, 'message': '缺少单词ID'})

        user_id = session['user_id']

        try:
            # 确保用户单词关系记录存在
            user_word = UserWord.get_or_create(user_id, word_id)

            # 更新记忆方法
            update_query = """
            UPDATE user_word
            SET memory_method = ?, updated_at = CURRENT_TIMESTAMP
            WHERE user_id = ? AND word_id = ?
            """
            db.execute_update(update_query, (memory_method, user_id, word_id))

            return jsonify({
                'success': True,
                'message': '记忆方法已保存'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'保存失败: {str(e)}'
            })

    # ===== 图片管理API =====

    @app.route('/check_image', methods=['POST'])
    def check_image():
        """检查图片是否存在API"""
        data = request.get_json()
        word = data.get('word', '')

        if not word:
            return jsonify({'exists': False, 'message': '缺少单词参数'})

        # 检查图片文件是否存在
        image_filename = f"{word.lower()}.jpg"
        image_path = os.path.join(app.static_folder, 'images', 'words', image_filename)

        exists = os.path.exists(image_path)

        return jsonify({
            'exists': exists,
            'word': word,
            'image_path': f"/static/images/words/{image_filename}" if exists else None
        })

    @app.route('/generate_image', methods=['POST'])
    def generate_image():
        """生成图片API（占位符实现）"""
        data = request.get_json()
        word = data.get('word', '')

        if not word:
            return jsonify({'success': False, 'message': '缺少单词参数'})

        # 占位符实现 - 实际应用中可以集成图片生成API
        # 这里只是返回成功状态，表示图片生成请求已接收
        return jsonify({
            'success': True,
            'word': word,
            'message': '图片生成请求已提交',
            'note': '实际图片生成功能需要集成外部服务'
        })

    # ===== 学习模式切换API =====

    @app.route('/api/learning_session/set_mode', methods=['POST'])
    def set_learning_mode():
        """设置学习模式 - 支持传统模式和拼写模式切换"""
        try:
            data = request.get_json()
            session_id = data.get('session_id')
            learning_mode = data.get('learning_mode')  # 'traditional' 或 'spelling'

            if not session_id or not learning_mode:
                return jsonify({
                    "success": False,
                    "error": "缺少必要参数"
                }), 400

            if learning_mode not in ['traditional', 'spelling']:
                return jsonify({
                    "success": False,
                    "error": "无效的学习模式"
                }), 400

            result = LearningSessionService.set_learning_mode(session_id, learning_mode)
            return jsonify(result)

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"设置学习模式失败: {str(e)}"
            }), 500

    @app.route('/api/learning_session/daily_report')
    def get_daily_report():
        """获取今日学习报告"""
        try:
            if 'user_id' not in session:
                return jsonify({
                    "success": False,
                    "error": "请先登录"
                }), 401

            user_id = session['user_id']
            report_date = request.args.get('date', str(date.today()))

            result = LearningSessionService.get_daily_report(user_id, report_date)
            return jsonify(result)

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取学习报告失败: {str(e)}"
            }), 500

    @app.route('/api/learning_session/statistics')
    def get_learning_statistics():
        """获取学习统计数据"""
        try:
            if 'user_id' not in session:
                return jsonify({
                    "success": False,
                    "error": "请先登录"
                }), 401

            user_id = session['user_id']
            period = request.args.get('period', 'week')  # week, month, all

            result = LearningSessionService.get_learning_statistics(user_id, period)
            return jsonify(result)

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取学习统计失败: {str(e)}"
            }), 500

    @app.route('/api/learning_session/spelling_feedback', methods=['POST'])
    def get_spelling_feedback():
        """拼写模式字母级反馈API"""
        try:
            data = request.get_json()
            session_id = data.get('session_id')
            user_input = data.get('user_input', '')
            target_word = data.get('target_word', '')
            letter_position = data.get('letter_position', 0)

            if not session_id or not target_word:
                return jsonify({
                    "success": False,
                    "error": "缺少必要参数"
                }), 400

            result = LearningSessionService.get_spelling_feedback(
                session_id, user_input, target_word, letter_position
            )
            return jsonify(result)

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取拼写反馈失败: {str(e)}"
            }), 500

    @app.route('/api/learning_session/plan_details')
    def get_plan_details():
        """获取学习计划详情API"""
        try:
            if 'user_id' not in session:
                return jsonify({
                    "success": False,
                    "error": "请先登录"
                }), 401

            user_id = session['user_id']
            plan_date = request.args.get('date', str(date.today()))

            result = LearningSessionService.get_plan_details(user_id, plan_date)
            return jsonify(result)

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取学习计划详情失败: {str(e)}"
            }), 500

    @app.route('/api/learning_session/hint', methods=['POST'])
    def get_learning_hint():
        """多级提示系统API"""
        try:
            data = request.get_json()
            word = data.get('word', '')
            hint_level = data.get('hint_level', 1)

            if not word:
                return jsonify({
                    "success": False,
                    "error": "缺少单词参数"
                }), 400

            result = LearningSessionService.get_learning_hint(word, hint_level)
            return jsonify(result)

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取学习提示失败: {str(e)}"
            }), 500

    @app.route('/api/word_images/<word>')
    def get_word_images(word):
        """获取单词图片资源API"""
        try:
            if not word:
                return jsonify({
                    "success": False,
                    "error": "缺少单词参数"
                }), 400

            result = LearningSessionService.get_word_images(word)
            return jsonify(result)

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取单词图片失败: {str(e)}"
            }), 500

    @app.route('/api/learning_session/memory_help', methods=['POST'])
    def get_memory_help():
        """AI记忆辅助API - 新增购物券消耗机制"""
        try:
            data = request.get_json()
            word = data.get('word', '')
            meaning = data.get('meaning', '')
            user_level = data.get('user_level', 'beginner')

            if not word:
                return jsonify({
                    "success": False,
                    "error": "缺少单词参数"
                }), 400

            # 🎯 新增：检查用户登录状态
            user_id = session.get('user_id')
            if not user_id:
                return jsonify({
                    "success": False,
                    "error": "用户未登录"
                }), 401

            # 🎯 新增：检查购物券是否足够
            from ..services.voucher_service import VoucherService
            user = User.get_by_id(user_id)
            if not user or user['vouchers'] < 1:
                return jsonify({
                    "success": False,
                    "error": "购物券不足！使用AI帮助需要1张购物券",
                    "vouchers_required": 1,
                    "current_vouchers": user['vouchers'] if user else 0
                }), 400

            # 获取AI记忆帮助
            result = LearningSessionService.get_memory_help(word, meaning, user_level)

            if result.get('success'):
                # 🎯 新增：消耗购物券
                voucher_consumed = VoucherService.consume_vouchers(
                    user_id, 1, f"AI帮助：{word}", "ai_help"
                )

                if not voucher_consumed:
                    return jsonify({
                        "success": False,
                        "error": "购物券扣除失败，请重试"
                    }), 500

                # 在响应中添加购物券信息
                result['vouchers_consumed'] = 1
                result['remaining_vouchers'] = user['vouchers'] - 1

            return jsonify(result)

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取记忆辅助失败: {str(e)}"
            }), 500

    # ===== 新增API - 减轻前端负载 =====

    @app.route('/api/learning_session/get_filtered_plan', methods=['POST'])
    @require_auth
    @api_response
    def api_get_filtered_plan():
        """获取过滤后的学习计划 - 迁移前端复杂逻辑到后端"""
        try:
            data = request.get_json() or {}
            user_id = session['user_id']
            filters = data.get('filters', {})

            # 获取基础学习计划
            from ..models.planning import LearningPlan
            today = date.today()
            base_plan = LearningPlan.get_daily_plan(user_id, today)

            if not base_plan:
                return jsonify({
                    "success": True,
                    "data": {
                        "filtered_words": [],
                        "total_count": 0,
                        "star_distribution": {},
                        "message": "今天没有学习计划"
                    }
                })

            # 应用过滤器
            filtered_words = []
            star_distribution = {}

            for word in base_plan:
                word_dict = dict(word)
                star_level = word_dict.get('star_level', 3)

                # 星级过滤
                if 'star_levels' in filters:
                    if star_level not in filters['star_levels']:
                        continue

                # 新词/复习词过滤
                if 'new_words' in filters and not filters['new_words']:
                    if star_level <= 2:  # 假设1-2星为新词
                        continue

                if 'review_words' in filters and not filters['review_words']:
                    if star_level >= 3:  # 假设3-5星为复习词
                        continue

                filtered_words.append(word_dict)
                star_distribution[star_level] = star_distribution.get(star_level, 0) + 1

            return jsonify({
                "success": True,
                "data": {
                    "filtered_words": filtered_words,
                    "total_count": len(filtered_words),
                    "star_distribution": star_distribution,
                    "filters_applied": filters
                }
            })

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取过滤计划失败: {str(e)}"
            }), 500

    @app.route('/api/learning_session/validate_answer', methods=['POST'])
    @require_auth
    @api_response
    def api_validate_answer():
        """验证答案 - 增强版答案验证，包含拼写变体检查"""
        try:
            data = request.get_json() or {}
            session_id = data.get('session_id')
            user_input = data.get('user_input', '').strip().lower()
            target_word = data.get('target_word', '').strip().lower()
            duration_seconds = data.get('duration_seconds', 0)

            if not session_id or not user_input:
                return jsonify({
                    "success": False,
                    "error": "缺少必要参数"
                }), 400

            # 基础验证
            is_exact_match = user_input == target_word

            # 拼写变体检查
            is_close_match = False
            similarity_score = 0

            if not is_exact_match and target_word:
                # 简单的相似度计算
                from difflib import SequenceMatcher
                similarity_score = SequenceMatcher(None, user_input, target_word).ratio()
                is_close_match = similarity_score >= 0.8  # 80%相似度

            # 确定最终结果
            is_correct = is_exact_match or is_close_match

            # 计算积分
            points_earned = 0
            if is_correct:
                if is_exact_match:
                    points_earned = 10  # 完全正确
                else:
                    points_earned = 5   # 接近正确

                # 时间加成
                if duration_seconds < 3:
                    points_earned += 2  # 快速回答加成

            return jsonify({
                "success": True,
                "validation_result": {
                    "is_correct": is_correct,
                    "is_exact_match": is_exact_match,
                    "is_close_match": is_close_match,
                    "similarity_score": similarity_score,
                    "points_earned": points_earned,
                    "duration_seconds": duration_seconds,
                    "feedback_message": "完全正确！" if is_exact_match else
                                      "接近正确！" if is_close_match else "答案错误"
                }
            })

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"验证答案失败: {str(e)}"
            }), 500

    # ===== 前后端分离架构API - 补充学习流程API =====

    @app.route('/api/learning_session/words', methods=['GET'])
    @require_auth
    @api_response
    def api_get_learning_words():
        """获取学习单词列表 - 前后端分离架构"""
        try:
            user_id = session['user_id']
            session_id = request.args.get('session_id')

            if not session_id:
                return jsonify({
                    "success": False,
                    "error": "缺少session_id参数"
                }), 400

            # 基于现有服务获取会话单词
            session_data = LearningSessionService.get_session_state(session_id, user_id)

            if not session_data.get('success'):
                return jsonify({
                    "success": False,
                    "error": "会话不存在或已过期"
                }), 404

            learning_state = session_data.get('learning_state', {})
            words = learning_state.get('words', [])

            return jsonify({
                "success": True,
                "data": {
                    "words": words,
                    "total_count": len(words),
                    "session_id": session_id,
                    "current_index": learning_state.get('current_index', 0)
                }
            })

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取学习单词失败: {str(e)}"
            }), 500

    @app.route('/api/learning_session/next', methods=['GET'])
    @require_auth
    @api_response
    def api_get_next_word():
        """获取下一个单词 - 前后端分离架构"""
        try:
            user_id = session['user_id']
            session_id = request.args.get('session_id')

            if not session_id:
                return jsonify({
                    "success": False,
                    "error": "缺少session_id参数"
                }), 400

            # 获取会话状态
            session_data = LearningSessionService.get_session_state(session_id, user_id)

            if not session_data.get('success'):
                return jsonify({
                    "success": False,
                    "error": "会话不存在或已过期"
                }), 404

            learning_state = session_data.get('learning_state', {})
            current_word = learning_state.get('current_word')
            has_next = learning_state.get('current_index', 0) < learning_state.get('total_words', 0) - 1

            return jsonify({
                "success": True,
                "data": {
                    "word": current_word,
                    "has_next": has_next,
                    "current_index": learning_state.get('current_index', 0),
                    "total_words": learning_state.get('total_words', 0)
                }
            })

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取下一个单词失败: {str(e)}"
            }), 500

    @app.route('/api/learning_session/end', methods=['POST'])
    @require_auth
    @api_response
    def api_end_learning_session():
        """结束学习会话 - 前后端分离架构"""
        try:
            data = request.get_json() or {}
            user_id = session['user_id']
            session_id = data.get('session_id')

            if not session_id:
                return APIResponse.json_error(
                    APIErrorCodes.MISSING_REQUIRED_FIELD,
                    "缺少session_id参数",
                    {"missing_field": "session_id"}
                )

            # 🔧 使用改进的优雅结束会话方法
            end_result = LearningSessionService.end_session_gracefully(session_id, user_id)

            if end_result.get('success'):
                # 获取会话最终统计
                final_stats = LearningSessionService.get_daily_report(user_id, str(date.today()))

                return APIResponse.json_success(
                    data={
                        "session_summary": end_result['session_summary'],
                        "final_stats": final_stats.get('data', {}) if final_stats.get('success') else {}
                    },
                    message="学习会话结束成功"
                )
            else:
                return APIResponse.json_error(
                    APIErrorCodes.BUSINESS_LOGIC_ERROR,
                    end_result.get('message', '结束学习会话失败'),
                    {"session_id": session_id}
                )

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"结束学习会话失败: {str(e)}"
            }), 500

    # ===== 数据一致性监控API =====

    @app.route('/api/monitoring/consistency/status', methods=['GET'])
    @require_auth
    @api_response
    def api_get_monitoring_status():
        """获取数据一致性监控状态"""
        try:
            from ..services.monitoring.consistency_monitor import get_monitoring_status

            status = get_monitoring_status()

            return APIResponse.json_success(
                data=status,
                message="获取监控状态成功"
            )

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.BUSINESS_LOGIC_ERROR,
                f"获取监控状态失败: {str(e)}"
            )

    @app.route('/api/monitoring/consistency/check', methods=['POST'])
    @require_auth
    @api_response
    def api_force_consistency_check():
        """强制执行数据一致性检查"""
        try:
            from ..services.monitoring.consistency_monitor import force_consistency_check

            result = force_consistency_check()

            return APIResponse.json_success(
                data=result,
                message="一致性检查完成"
            )

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.BUSINESS_LOGIC_ERROR,
                f"执行一致性检查失败: {str(e)}"
            )

    # ===== 用户数据API =====

    @app.route('/api/user/profile', methods=['GET'])
    @require_auth
    @api_response
    def api_get_user_profile():
        """获取用户信息 - 前后端分离架构"""
        try:
            user_id = session['user_id']
            from ..models import User

            user = User.get_by_id(user_id)
            if not user:
                return jsonify({
                    "success": False,
                    "error": "用户不存在"
                }), 404

            user_dict = dict(user)

            return jsonify({
                "success": True,
                "data": {
                    "user_id": user_id,
                    "username": user_dict.get('username', ''),
                    "points": user_dict.get('points', 0),
                    "vouchers": user_dict.get('vouchers', 0),
                    "level": user_dict.get('level', 1),
                    "created_at": user_dict.get('created_at', ''),
                    "last_login": user_dict.get('last_login', '')
                }
            })

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取用户信息失败: {str(e)}"
            }), 500



    # ===== AI辅助功能API =====

    @app.route('/api/ai/hint', methods=['POST'])
    @require_auth
    @api_response
    def api_get_ai_hint():
        """获取AI提示 - 前后端分离架构"""
        try:
            data = request.get_json() or {}
            word = data.get('word', '').strip()
            hint_level = data.get('hint_level', 1)

            if not word:
                return jsonify({
                    "success": False,
                    "error": "缺少单词参数"
                }), 400

            if not isinstance(hint_level, int) or hint_level < 1 or hint_level > 5:
                return jsonify({
                    "success": False,
                    "error": "提示级别必须是1-5之间的整数"
                }), 400

            # 使用现有HintService
            result = LearningSessionService.get_learning_hint(word, hint_level)

            if result.get('success'):
                hint_data = result.get('data', {})
                return jsonify({
                    "success": True,
                    "data": {
                        "hint": hint_data.get('hint_content', ''),
                        "hint_level": hint_level,
                        "word": word,
                        "hint_type": hint_data.get('hint_type', 'progressive')
                    }
                })
            else:
                return jsonify({
                    "success": False,
                    "error": result.get('error', '获取提示失败')
                }), 500

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取AI提示失败: {str(e)}"
            }), 500

    @app.route('/api/ai/memory_help', methods=['POST'])
    @require_auth
    @api_response
    def api_get_ai_memory_help():
        """获取AI记忆帮助 - 前后端分离架构"""
        try:
            data = request.get_json() or {}
            word = data.get('word', '').strip()
            meaning = data.get('meaning', '').strip()
            user_level = data.get('user_level', 'beginner')

            if not word:
                return jsonify({
                    "success": False,
                    "error": "缺少单词参数"
                }), 400

            if not meaning:
                return jsonify({
                    "success": False,
                    "error": "缺少单词含义参数"
                }), 400

            # 使用现有MemoryService
            result = LearningSessionService.get_memory_help(word, meaning, user_level)

            if result.get('success'):
                memory_data = result.get('data', {})
                return jsonify({
                    "success": True,
                    "data": {
                        "memory_tips": memory_data.get('memory_tips', []),
                        "techniques": memory_data.get('techniques', []),
                        "word": word,
                        "meaning": meaning,
                        "user_level": user_level
                    }
                })
            else:
                return jsonify({
                    "success": False,
                    "error": result.get('error', '获取记忆帮助失败')
                }), 500

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取AI记忆帮助失败: {str(e)}"
            }), 500

    # ===== 前端分离关键API - 替换大型JavaScript文件 =====

    @app.route('/api/spell_check', methods=['POST'])
    @require_auth
    @api_response
    def api_spell_check():
        """智能拼写检查 - 替换前端SpellChecker.js (657行)"""
        try:
            data = request.get_json() or {}
            user_input = data.get('user_input', '').strip().lower()
            target_word = data.get('target_word', '').strip().lower()

            if not user_input or not target_word:
                return jsonify({
                    "success": False,
                    "error": "缺少必要参数"
                }), 400

            # 基础检查
            is_exact_match = user_input == target_word

            # 相似度计算
            from difflib import SequenceMatcher
            similarity_score = SequenceMatcher(None, user_input, target_word).ratio()

            # 智能拼写检查逻辑
            is_close_match = similarity_score >= 0.8
            is_correct = is_exact_match or is_close_match

            # 生成建议
            suggestions = []
            if not is_correct:
                # 简单的建议生成
                if len(user_input) == len(target_word):
                    suggestions.append(f"检查拼写: {target_word}")
                elif len(user_input) < len(target_word):
                    suggestions.append(f"可能缺少字母: {target_word}")
                else:
                    suggestions.append(f"可能多了字母: {target_word}")

            # 确定纠错类型
            correction_type = "exact" if is_exact_match else "close" if is_close_match else "incorrect"

            return jsonify({
                "success": True,
                "data": {
                    "is_correct": is_correct,
                    "is_exact_match": is_exact_match,
                    "similarity_score": similarity_score,
                    "suggestions": suggestions,
                    "correction_type": correction_type,
                    "user_input": user_input,
                    "target_word": target_word
                }
            })

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"拼写检查失败: {str(e)}"
            }), 500

    @app.route('/api/hint_system', methods=['POST'])
    @require_auth
    @api_response
    def api_hint_system():
        """完整提示系统 - 替换前端HintSystem.js (806行)"""
        try:
            data = request.get_json() or {}
            word = data.get('word', '').strip()
            hint_level = data.get('hint_level', 1)
            hint_type = data.get('hint_type', 'progressive')

            if not word:
                return jsonify({
                    "success": False,
                    "error": "缺少单词参数"
                }), 400

            if not isinstance(hint_level, int) or hint_level < 1 or hint_level > 5:
                return jsonify({
                    "success": False,
                    "error": "提示级别必须是1-5之间的整数"
                }), 400

            # 生成渐进式提示
            hint_content = ""
            if hint_type == "progressive":
                # 渐进式提示：逐步显示更多字母
                word_length = len(word)
                reveal_count = int((hint_level / 5.0) * word_length)
                reveal_count = max(1, min(reveal_count, word_length))

                # 随机选择要显示的字母位置
                import random
                positions = list(range(word_length))
                random.shuffle(positions)
                revealed_positions = sorted(positions[:reveal_count])

                hint_chars = []
                for i, char in enumerate(word):
                    if i in revealed_positions:
                        hint_chars.append(char)
                    else:
                        hint_chars.append('_')

                hint_content = ' '.join(hint_chars)

            elif hint_type == "length":
                hint_content = f"这个单词有 {len(word)} 个字母"

            elif hint_type == "first_letter":
                hint_content = f"这个单词以字母 '{word[0].upper()}' 开头"

            else:
                # 默认提示
                hint_content = f"提示级别 {hint_level}: 这个单词有 {len(word)} 个字母"

            has_next = hint_level < 5

            return jsonify({
                "success": True,
                "data": {
                    "hint_content": hint_content,
                    "hint_level": hint_level,
                    "hint_type": hint_type,
                    "next_level_available": has_next,
                    "word_length": len(word)
                }
            })

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"提示系统失败: {str(e)}"
            }), 500

    @app.route('/api/learning_state', methods=['POST'])
    @require_auth
    @api_response
    def api_learning_state():
        """学习状态管理 - 替换前端LearningEngine.js (731行)"""
        try:
            data = request.get_json() or {}
            session_id = data.get('session_id')
            action = data.get('action', 'get')  # 'update', 'get', 'reset'
            state_data = data.get('state_data', {})
            user_id = session['user_id']

            if not session_id:
                return jsonify({
                    "success": False,
                    "error": "缺少session_id参数"
                }), 400

            if action == 'get':
                # 获取学习状态
                session_data = LearningSessionService.get_session_state(session_id, user_id)
                if session_data.get('success'):
                    learning_state = session_data.get('learning_state', {})
                    return jsonify({
                        "success": True,
                        "data": {
                            "learning_state": learning_state,
                            "session_info": {
                                "session_id": session_id,
                                "user_id": user_id,
                                "status": "active"
                            },
                            "progress": {
                                "current_index": learning_state.get('current_index', 0),
                                "total_words": learning_state.get('total_words', 0),
                                "completion_rate": (learning_state.get('current_index', 0) / max(learning_state.get('total_words', 1), 1)) * 100
                            }
                        }
                    })
                else:
                    return jsonify({
                        "success": False,
                        "error": "会话不存在或已过期"
                    }), 404

            elif action == 'update':
                # 更新学习状态（这里可以扩展具体的状态更新逻辑）
                return jsonify({
                    "success": True,
                    "data": {
                        "message": "学习状态更新成功",
                        "updated_fields": list(state_data.keys())
                    }
                })

            elif action == 'reset':
                # 重置学习状态（这里可以扩展重置逻辑）
                return jsonify({
                    "success": True,
                    "data": {
                        "message": "学习状态重置成功",
                        "session_id": session_id
                    }
                })

            else:
                return jsonify({
                    "success": False,
                    "error": "无效的操作类型"
                }), 400

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"学习状态管理失败: {str(e)}"
            }), 500

    @app.route('/api/word_filter', methods=['POST'])
    @require_auth
    @api_response
    def api_word_filter():
        """单词筛选算法 - 替换前端筛选逻辑"""
        try:
            data = request.get_json() or {}
            user_id = session['user_id']
            filters = data.get('filters', {})

            # 获取基础学习计划
            from ..models.planning import LearningPlan
            today = date.today()
            base_plan = LearningPlan.get_daily_plan(user_id, today)

            if not base_plan:
                return {
                    "success": True,
                    "data": {
                        "filtered_words": [],
                        "filter_stats": {"total": 0, "filtered": 0},
                        "total_count": 0
                    }
                }

            # 应用复杂筛选逻辑
            filtered_words = []
            filter_stats = {"total": len(base_plan), "by_star": {}, "by_type": {}}

            for word in base_plan:
                word_dict = dict(word)
                star_level = word_dict.get('star_level', 3)

                # 星级筛选
                if 'star_levels' in filters:
                    if star_level not in filters['star_levels']:
                        continue

                # 新词/复习词筛选
                is_new_word = star_level <= 2
                if 'new_words' in filters and not filters['new_words'] and is_new_word:
                    continue
                if 'review_words' in filters and not filters['review_words'] and not is_new_word:
                    continue

                # 难度筛选
                if 'difficulty' in filters:
                    difficulty = filters['difficulty']
                    if difficulty == 'easy' and star_level > 2:
                        continue
                    elif difficulty == 'medium' and (star_level < 3 or star_level > 4):
                        continue
                    elif difficulty == 'hard' and star_level < 4:
                        continue

                filtered_words.append(word_dict)

                # 统计信息
                filter_stats["by_star"][star_level] = filter_stats["by_star"].get(star_level, 0) + 1
                word_type = "new" if is_new_word else "review"
                filter_stats["by_type"][word_type] = filter_stats["by_type"].get(word_type, 0) + 1

            filter_stats["filtered"] = len(filtered_words)

            return {
                "success": True,
                "data": {
                    "filtered_words": filtered_words,
                    "filter_stats": filter_stats,
                    "total_count": len(filtered_words),
                    "applied_filters": filters
                }
            }

        except Exception as e:
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_SERVER_ERROR,
                f"单词筛选失败: {str(e)}"
            )

    @app.route('/api/progress_calculate', methods=['POST'])
    @require_auth
    @api_response
    def api_progress_calculate():
        """进度计算 - 替换前端进度逻辑"""
        try:
            data = request.get_json() or {}
            user_id = session['user_id']
            session_id = data.get('session_id')

            if not session_id:
                return jsonify({
                    "success": False,
                    "error": "缺少session_id参数"
                }), 400

            # 获取会话状态
            session_data = LearningSessionService.get_session_state(session_id, user_id)

            if not session_data.get('success'):
                return jsonify({
                    "success": False,
                    "error": "会话不存在或已过期"
                }), 404

            learning_state = session_data.get('learning_state', {})
            current_index = learning_state.get('current_index', 0)
            total_words = learning_state.get('total_words', 0)

            # 计算进度
            completion_rate = (current_index / max(total_words, 1)) * 100
            remaining_words = max(0, total_words - current_index)

            # 估算时间
            avg_time_per_word = 30  # 假设每个单词平均30秒
            estimated_remaining_time = remaining_words * avg_time_per_word

            # 获取今日统计
            daily_report = LearningSessionService.get_daily_report(user_id, str(date.today()))
            daily_stats = daily_report.get('data', {}) if daily_report.get('success') else {}

            return jsonify({
                "success": True,
                "data": {
                    "current_progress": {
                        "current_index": current_index,
                        "total_words": total_words,
                        "remaining_words": remaining_words,
                        "completion_rate": round(completion_rate, 2)
                    },
                    "completion_rate": round(completion_rate, 2),
                    "time_estimates": {
                        "estimated_remaining_seconds": estimated_remaining_time,
                        "estimated_remaining_minutes": round(estimated_remaining_time / 60, 1),
                        "avg_time_per_word": avg_time_per_word
                    },
                    "daily_stats": daily_stats
                }
            })

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"进度计算失败: {str(e)}"
            }), 500

    @app.route('/api/media_validation', methods=['POST'])
    @require_auth
    @api_response
    def api_media_validation():
        """媒体验证 - 替换前端MediaManager.js验证逻辑"""
        try:
            data = request.get_json() or {}
            word = data.get('word', '').strip()
            media_type = data.get('media_type', 'audio')

            if not word:
                return jsonify({
                    "success": False,
                    "error": "缺少单词参数"
                }), 400

            if media_type not in ['audio', 'image']:
                return jsonify({
                    "success": False,
                    "error": "媒体类型必须是 'audio' 或 'image'"
                }), 400

            # 媒体验证逻辑
            media_available = False
            media_url = ""
            fallback_options = []

            if media_type == 'audio':
                # 音频文件验证
                audio_path = f"/static/audio/{word}.mp3"
                # 这里应该检查文件是否存在，简化处理
                media_available = True  # 假设音频文件存在
                media_url = audio_path
                fallback_options = [
                    f"/static/audio/{word}.wav",
                    f"/static/audio/{word}.ogg"
                ]

            elif media_type == 'image':
                # 图片文件验证
                image_path = f"/static/images/{word}.jpg"
                # 这里应该检查文件是否存在，简化处理
                media_available = True  # 假设图片文件存在
                media_url = image_path
                fallback_options = [
                    f"/static/images/{word}.png",
                    f"/static/images/{word}.gif",
                    "/static/images/default.jpg"
                ]

            return jsonify({
                "success": True,
                "data": {
                    "media_available": media_available,
                    "media_url": media_url,
                    "fallback_options": fallback_options,
                    "word": word,
                    "media_type": media_type
                }
            })

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"媒体验证失败: {str(e)}"
            }), 500

    # 注意: /api/rewards_system/calculate 路由已在 api_routes.py 中定义
    # 这里移除重复定义以避免冲突

    @app.route('/api/learning_session/statistics', methods=['GET'])
    @require_auth
    @api_response
    def api_get_statistics():
        """获取学习统计 - 扩展版统计API"""
        try:
            user_id = session['user_id']
            date_str = request.args.get('date', str(date.today()))

            # 使用现有的daily_report逻辑
            result = LearningSessionService.get_daily_report(user_id, date_str)

            if result.get('success'):
                # 扩展统计信息
                data = result.get('data', {})

                # 添加额外统计
                extended_stats = {
                    **data,
                    'learning_efficiency': data.get('accuracy_rate', 0) * data.get('words_per_minute', 1),
                    'performance_grade': RewardsSystemService._calculate_performance_grade(data),
                    'daily_progress': {
                        'words_learned': data.get('unique_words_learned', 0),
                        'time_spent': data.get('total_duration', 0),
                        'points_earned': data.get('points_earned_today', 0)
                    }
                }

                return jsonify({
                    "success": True,
                    "statistics": extended_stats
                })
            else:
                return jsonify(result)

        except Exception as e:
            return jsonify({
                "success": False,
                "error": f"获取统计失败: {str(e)}"
            }), 500

    # Pattern API 路由 - 已禁用
    @app.route('/api/word_pattern_suggestions/<int:word_id>', methods=['GET'])
    @require_auth
    @api_response
    def api_get_word_pattern_suggestions(word_id):
        """获取单词的pattern推荐建议 - 功能已禁用"""
        # 🚫 Pattern功能已禁用
        return APIResponse.json_error(
            APIErrorCodes.FEATURE_DISABLED,
            "Pattern功能暂时禁用",
            {"feature": "pattern_suggestions", "status": "disabled"}
        )

        # 原有代码已注释
        """
        user_id = session['user_id']
        
        try:
            from ..services.pattern.pattern_recommender import PatternRecommenderService
            from ..config import Config
            
            recommender = PatternRecommenderService(Config.DATABASE_PATH)
            
            # 获取相似单词推荐
            recommendations = recommender.get_similar_words(user_id, word_id)
            
            # 获取单词的pattern上下文
            context = recommender.get_word_pattern_context(word_id)
            
            # 格式化推荐数据
            formatted_recommendations = []
            for rec in recommendations:
                formatted_rec = {
                    'pattern_info': {
                        'pattern_id': rec.pattern_info.get('pattern_id'),
                        'pattern_type': rec.pattern_info['pattern_type'],
                        'pattern_name': rec.pattern_info['pattern_name'],
                        'match_reason': rec.pattern_info.get('match_reason'),
                        # 新增四维度分类字段
                        'cognitive_level': rec.pattern_info.get('cognitive_level', 'basic'),
                        'dimension_category': rec.pattern_info.get('dimension_category', 'semantic'),
                        'concept_group': rec.pattern_info.get('concept_group'),
                        'educational_value': rec.pattern_info.get('educational_value', 0.5),
                        'is_concept_integrated': rec.pattern_info.get('is_concept_integrated', False),
                        # 🦄 关键字段：语言学专家推荐标识
                        'is_linguistic_expert': rec.pattern_info.get('is_linguistic_expert', False),
                        'linguistic_principle': rec.pattern_info.get('linguistic_principle'),
                    },
                    'recommendation_reason': rec.recommendation_reason,
                    'similar_words': [
                        {
                            'word_id': word.word_id,
                            'english_word': word.english_word,
                            'chinese_meaning': word.chinese_meaning,
                            'similarity_score': round(word.similarity_score, 2),
                            'learning_status': word.learning_status,
                            'proficiency': round(word.proficiency, 1)
                        }
                        for word in rec.similar_words
                    ]
                }
                formatted_recommendations.append(formatted_rec)
            
            response_data = {
                'word_id': word_id,
                'recommendations': formatted_recommendations,
                'pattern_context': context,
                'total_patterns': len(context.get('patterns', [])),
                'has_recommendations': len(formatted_recommendations) > 0
            }
            
            return APIResponse.json_success(
                data=response_data,
                message="Pattern推荐获取成功"
            )
            
        except Exception as e:
            from ..core import get_logger
            logger = get_logger(__name__)
            logger.error(f"获取Pattern推荐失败: {e}")
            
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_ERROR,
                "获取Pattern推荐失败",
                {"word_id": word_id, "error": str(e)}
            )
        """

    @app.route('/api/pattern_learning_suggestions', methods=['GET'])
    @require_auth
    @api_response
    def api_get_pattern_learning_suggestions():
        """获取用户的pattern学习建议 - 功能已禁用"""
        # 🚫 Pattern功能已禁用
        return APIResponse.json_error(
            APIErrorCodes.FEATURE_DISABLED,
            "Pattern功能暂时禁用",
            {"feature": "pattern_learning_suggestions", "status": "disabled"}
        )

        # 原有代码已注释
        """
        user_id = session['user_id']
        pattern_type = request.args.get('pattern_type')  # 可选过滤条件
        
        try:
            from ..services.pattern.pattern_recommender import PatternRecommenderService
            from ..config import Config
            
            recommender = PatternRecommenderService(Config.DATABASE_PATH)
            suggestions = recommender.get_pattern_learning_suggestions(user_id, pattern_type)
            
            # 格式化建议数据
            formatted_suggestions = []
            for suggestion in suggestions:
                formatted_suggestion = {
                    'pattern_type': suggestion['pattern_type'],
                    'pattern_name': suggestion['pattern_name'],
                    'total_words_in_pattern': suggestion['total_words_in_pattern'],
                    'learned_words': suggestion['learned_words'],
                    'completion_rate': round(suggestion['completion_rate'] * 100, 1),  # 转换为百分比
                    'avg_proficiency': suggestion['avg_proficiency'],
                    'suggestion_type': suggestion['suggestion_type'],
                    'next_words': suggestion.get('next_words', [])[:3]  # 最多3个建议单词
                }
                formatted_suggestions.append(formatted_suggestion)
            
            response_data = {
                'user_id': user_id,
                'suggestions': formatted_suggestions,
                'total_patterns': len(formatted_suggestions),
                'pattern_type_filter': pattern_type
            }
            
            return APIResponse.json_success(
                data=response_data,
                message="Pattern学习建议获取成功"
            )
            
        except Exception as e:
            from ..core import get_logger
            logger = get_logger(__name__)
            logger.error(f"获取Pattern学习建议失败: {e}")
            
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_ERROR,
                "获取Pattern学习建议失败",
                {"user_id": user_id, "error": str(e)}
            )
        """

    @app.route('/api/pattern_interaction', methods=['POST'])
    @require_auth
    @api_response
    def api_record_pattern_interaction():
        """记录用户与pattern的交互 - 功能已禁用"""
        # 🚫 Pattern功能已禁用
        return APIResponse.json_error(
            APIErrorCodes.FEATURE_DISABLED,
            "Pattern功能暂时禁用",
            {"feature": "pattern_interaction", "status": "disabled"}
        )

        # 原有代码已注释
        """
        user_id = session['user_id']
        data = request.get_json() or {}
        
        pattern_id = data.get('pattern_id')
        word_id = data.get('word_id')
        interaction_type = data.get('interaction_type')  # 'view', 'click', 'helpful', 'not_helpful'
        session_id = data.get('session_id')
        
        if not all([pattern_id, word_id, interaction_type]):
            return APIResponse.json_error(
                APIErrorCodes.VALIDATION_ERROR,
                "缺少必要参数",
                {"required": ["pattern_id", "word_id", "interaction_type"]}
            )
        
        if interaction_type not in ['view', 'click', 'helpful', 'not_helpful', 'ignore']:
            return APIResponse.json_error(
                APIErrorCodes.VALIDATION_ERROR,
                "交互类型无效",
                {"valid_types": ['view', 'click', 'helpful', 'not_helpful', 'ignore']}
            )
        
        try:
            from ..services.pattern.pattern_recommender import PatternRecommenderService
            from ..config import Config
            
            recommender = PatternRecommenderService(Config.DATABASE_PATH)
            success = recommender.record_pattern_interaction(
                user_id, pattern_id, word_id, interaction_type, session_id
            )
            
            if success:
                return APIResponse.json_success(
                    data={"recorded": True},
                    message="交互记录成功"
                )
            else:
                return APIResponse.json_error(
                    APIErrorCodes.INTERNAL_ERROR,
                    "交互记录失败"
                )
                
        except Exception as e:
            from ..core import get_logger
            logger = get_logger(__name__)
            logger.error(f"记录Pattern交互失败: {e}")
            
            return APIResponse.json_error(
                APIErrorCodes.INTERNAL_ERROR,
                "记录交互失败",
                {"error": str(e)}
            )
        """


def _generate_tts_audio(text: str, output_path: str) -> bool:
    """
    调用TTS API生成音频文件
    
    Args:
        text: 要转换的文本
        output_path: 输出文件路径
        
    Returns:
        是否成功生成
    """
    import requests
    
    try:
        # TTS API配置
        api_key = "sk-QBBtTxLIJ4FUK62i6d2c6fAb4c9f4c3aBbF6A7073022099f"
        base_url = "https://api.365api.shop/v1"
        
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        # TTS配置
        tts_config = {
            "model": "tts-1",
            "voice": "alloy",  # 适合儿童的声音
            "response_format": "mp3",
            "speed": 0.9  # 稍慢一点，便于儿童理解
        }
        
        # 准备API请求
        endpoint = f"{base_url}/audio/speech"
        data = {
            **tts_config,
            "input": text
        }
        
        print(f"🎵 正在生成音频: {text}")
        
        # 调用TTS API
        response = requests.post(
            endpoint, 
            headers=headers, 
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            # 确保目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 保存音频文件
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            file_size = len(response.content)
            print(f"✅ 音频生成成功: {os.path.basename(output_path)} (大小: {file_size} 字节)")
            return True
        else:
            print(f"❌ TTS API调用失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ TTS生成异常: {e}")
        return False


def _generate_ai_image(english_word: str, chinese_meaning: str, output_path: str) -> bool:
    """
    调用GPT-4O-All API生成单词学习图片

    Args:
        english_word: 英文单词
        chinese_meaning: 中文含义
        output_path: 输出文件路径

    Returns:
        bool: 生成成功返回True，失败返回False
    """
    import requests
    import base64

    try:
        # 为少儿学习优化的图片生成提示词
        prompt = f"""Create a colorful, child-friendly, cartoon-style illustration of "{english_word}" ({chinese_meaning}).

Requirements:
- Bright and cheerful colors suitable for children aged 6-12
- Simple, clear visual elements that are easy to understand
- Educational and engaging content
- NO TEXT OR LETTERS visible in the image
- High contrast and appealing to kids
- Safe and appropriate content for children
- Cartoon/illustration style (not photographic)
- Clear visual representation of the word's meaning"""

        # 使用GPT-4O-All API配置
        api_base_url = "https://api.365api.shop/v1"
        api_key = "sk-QBBtTxLIJ4FUK62i6d2c6fAb4c9f4c3aBbF6A7073022099f"

        # API请求头
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        # API请求数据 (OpenAI DALL-E格式)
        data = {
            "model": "dall-e-2",  # 使用DALL-E 2模型 - 支持512x512分辨率
            "prompt": prompt,
            "n": 1,  # 生成1张图片
            "size": "512x512",  # 图片尺寸 - 降低分辨率以减小文件大小
            "response_format": "b64_json"  # 返回base64编码的图片数据
        }

        print(f"🎨 正在使用GPT-4O-All API生成图片: {english_word} ({chinese_meaning})")

        # 调用GPT-4O-All图片生成API
        response = requests.post(
            f"{api_base_url}/images/generations",
            headers=headers,
            json=data,
            timeout=120  # 图片生成可能需要更长时间
        )

        if response.status_code == 200:
            response_data = response.json()

            # 检查响应格式
            if 'data' in response_data and len(response_data['data']) > 0:
                image_data = response_data['data'][0]

                if 'b64_json' in image_data:
                    try:
                        # 解码base64图片数据
                        b64_data = image_data['b64_json']

                        # 清理base64数据（移除可能的前缀）
                        if b64_data.startswith('data:image'):
                            # 如果有data URL前缀，移除它
                            b64_data = b64_data.split(',')[1]

                        image_bytes = base64.b64decode(b64_data)

                        # 验证是否为有效的图片文件
                        if len(image_bytes) > 5120:  # 确保图片有效（大于5KB）
                            # 检查文件头以确认是有效的图片格式并确定正确的扩展名
                            detected_extension = None
                            if image_bytes.startswith(b'\xff\xd8\xff'):  # JPEG
                                detected_extension = '.jpg'
                            elif image_bytes.startswith(b'\x89PNG'):     # PNG
                                detected_extension = '.png'
                            elif image_bytes.startswith(b'GIF87a') or image_bytes.startswith(b'GIF89a'):  # GIF
                                detected_extension = '.gif'
                            elif image_bytes.startswith(b'RIFF'):        # WebP (RIFF container)
                                detected_extension = '.webp'

                            if detected_extension:
                                # 🔧 修复：根据实际格式调整文件路径
                                base_name = os.path.splitext(os.path.basename(output_path))[0]
                                dir_name = os.path.dirname(output_path)
                                corrected_output_path = os.path.join(dir_name, base_name + detected_extension)
                                
                                # 确保目录存在
                                os.makedirs(os.path.dirname(corrected_output_path), exist_ok=True)

                                # 保存图片文件到正确路径
                                with open(corrected_output_path, 'wb') as f:
                                    f.write(image_bytes)

                                file_size = len(image_bytes)
                                print(f"✅ GPT-4O-All图片生成成功: {os.path.basename(corrected_output_path)} (大小: {file_size} 字节, 格式: {detected_extension})")
                                
                                # 🔧 修复：如果原始jpg路径和实际格式不同，需要删除错误文件并创建正确链接
                                if corrected_output_path != output_path:
                                    # 如果请求的是jpg但实际是其他格式，创建一个软链接以保持兼容性
                                    if os.path.exists(output_path):
                                        os.remove(output_path)
                                    try:
                                        # 创建相对路径的软链接
                                        relative_target = os.path.basename(corrected_output_path)
                                        os.symlink(relative_target, output_path)
                                        print(f"🔗 创建兼容性链接: {os.path.basename(output_path)} -> {relative_target}")
                                    except Exception as link_error:
                                        print(f"⚠️ 创建链接失败: {link_error}")
                                
                                return True
                            else:
                                print(f"❌ 图片生成失败: 无效的图片格式")
                                return False
                        else:
                            print(f"❌ 图片生成失败: 图片数据太小")
                            return False
                    except Exception as decode_error:
                        print(f"❌ 图片解码失败: {decode_error}")
                        return False
                else:
                    print(f"❌ 图片生成失败: 响应中缺少b64_json字段")
                    return False
            else:
                print(f"❌ 图片生成失败: 响应格式错误 - {response_data}")
                return False
        else:
            print(f"❌ GPT-4O-All API调用失败: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"❌ 图片生成异常: {e}")
        return False

    # ===== Pattern推荐API =====

