"""
认证路由模块
处理用户注册、登录、登出相关的API路由
"""
from flask import request, jsonify, render_template, session, redirect, url_for

from ..services import AuthService, LearningPlanService, LearningService


def register_auth_routes(app):
    """注册认证路由"""
    
    @app.route('/api/register', methods=['POST'])
    def api_register():
        """用户注册API"""
        # 支持JSON和表单数据
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form
            
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        
        if not username or not password:
            if request.is_json:
                return jsonify({'success': False, 'message': '用户名和密码不能为空'})
            else:
                return render_template('register.html', error='用户名和密码不能为空')
        
        # 验证用户名和密码格式
        valid_username, username_msg = AuthService.validate_username(username)
        if not valid_username:
            if request.is_json:
                return jsonify({'success': False, 'message': username_msg})
            else:
                return render_template('register.html', error=username_msg)
        
        valid_password, password_msg = AuthService.validate_password(password)
        if not valid_password:
            if request.is_json:
                return jsonify({'success': False, 'message': password_msg})
            else:
                return render_template('register.html', error=password_msg)
        
        success, message, user_id = AuthService.register(username, password)
        
        if success:
            # 自动登录
            session['user_id'] = user_id
            session['username'] = username
            
            # 注册后确保数据一致性（移除不存在的方法调用）
            
            # 生成首日学习计划
            LearningPlanService.generate_daily_plan(user_id)
            
            if request.is_json:
                return jsonify({'success': success, 'message': message})
            else:
                return redirect('/dashboard')
        else:
            if request.is_json:
                return jsonify({'success': success, 'message': message})
            else:
                return render_template('register.html', error=message)
    
    @app.route('/api/login', methods=['POST'])
    def api_login():
        """用户登录API"""
        # 支持JSON和表单数据
        if request.is_json:
            data = request.get_json()
        else:
            data = request.form
            
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()
        
        if not username or not password:
            if request.is_json:
                return jsonify({'success': False, 'message': '用户名和密码不能为空'})
            else:
                return render_template('login.html', error='用户名和密码不能为空')
        
        success, message, user_info = AuthService.login(username, password)
        
        if success:
            session['user_id'] = user_info['id']
            session['username'] = user_info['username']
            
            # 登录后确保数据一致性（移除不存在的方法调用）

            # 确保有当日学习计划 - 增强错误处理
            try:
                result = LearningPlanService.generate_daily_plan(user_info['id'])
                if result:
                    print(f"✅ 用户 {user_info['username']} (ID: {user_info['id']}) 学习计划生成成功")
                else:
                    print(f"⚠️ 用户 {user_info['username']} (ID: {user_info['id']}) 学习计划生成失败")
            except Exception as e:
                print(f"❌ 用户 {user_info['username']} (ID: {user_info['id']}) 学习计划生成异常: {e}")
                # 不影响登录流程，但记录错误
            
            if request.is_json:
                return jsonify({'success': success, 'message': message, 'user': user_info})
            else:
                return redirect('/dashboard')
        else:
            if request.is_json:
                return jsonify({'success': success, 'message': message, 'user': user_info})
            else:
                return render_template('login.html', error=message)
    
    @app.route('/api/logout', methods=['POST'])
    def api_logout():
        """用户登出API"""
        # 在登出前完成学习会话
        if 'user_id' in session:
            LearningService.finish_learning_session(session['user_id'])
        
        session.clear()
        return jsonify({'success': True, 'message': '已退出登录'})
    
    @app.route('/api/change_password', methods=['POST'])
    def api_change_password():
        """修改密码API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})
        
        data = request.get_json()
        old_password = data.get('old_password', '').strip()
        new_password = data.get('new_password', '').strip()
        
        if not old_password or not new_password:
            return jsonify({'success': False, 'message': '旧密码和新密码不能为空'})
        
        # 验证新密码格式
        valid_password, password_msg = AuthService.validate_password(new_password)
        if not valid_password:
            return jsonify({'success': False, 'message': password_msg})
        
        user_id = session['user_id']
        success, message = AuthService.change_password(user_id, old_password, new_password)
        
        return jsonify({'success': success, 'message': message})
    
    @app.route('/api/reset_password', methods=['POST'])
    def api_reset_password():
        """重置密码API（管理员功能）"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'message': '请先登录'})
        
        # 检查管理员权限
        from ..models import User
        user = User.get_by_id(session['user_id'])
        if not user or user['username'] != 'admin':
            return jsonify({'success': False, 'message': '权限不足'})
        
        data = request.get_json()
        username = data.get('username', '').strip()
        new_password = data.get('new_password', '').strip()
        
        if not username or not new_password:
            return jsonify({'success': False, 'message': '用户名和新密码不能为空'})
        
        # 验证新密码格式
        valid_password, password_msg = AuthService.validate_password(new_password)
        if not valid_password:
            return jsonify({'success': False, 'message': password_msg})
        
        success, message = AuthService.reset_password(username, new_password)
        
        return jsonify({'success': success, 'message': message})
    
    @app.route('/api/check_session')
    def api_check_session():
        """检查会话状态API"""
        if 'user_id' not in session:
            return jsonify({'success': False, 'logged_in': False, 'message': '未登录'})
        
        from ..models import User
        user = User.get_by_id(session['user_id'])
        if not user:
            # 用户不存在，清除session
            session.clear()
            return jsonify({'success': False, 'logged_in': False, 'message': '用户不存在'})
        
        user_info = {
            'id': user['id'],
            'username': user['username'],
            'points': user['points'],
            'vouchers': user['vouchers']
        }
        
        return jsonify({
            'success': True, 
            'logged_in': True, 
            'user': user_info,
            'message': '会话有效'
        })
