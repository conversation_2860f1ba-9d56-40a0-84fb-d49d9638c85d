"""
特征学习相关路由
提供用户学习洞察、难度预测等API
"""
from flask import Blueprint, request, jsonify, render_template, session
from ..services.feature_learning.feature_learning_service import FeatureLearningService
from ..services.feature_learning.pattern_learner import <PERSON><PERSON><PERSON><PERSON><PERSON>
from ..models.feature_learning import UserLearningPatterns, FeatureLearningConfig
from ..core.decorators import require_auth
from ..core import get_logger

logger = get_logger(__name__)

feature_learning_bp = Blueprint('feature_learning', __name__)


@feature_learning_bp.route('/insights')
@require_auth()
def learning_insights():
    """显示用户学习洞察页面"""
    user_id = session.get('user_id')
    
    # 获取用户学习洞察
    insights = FeatureLearningService.get_user_learning_insights(user_id)
    
    return render_template('feature_learning/insights.html', insights=insights)


@feature_learning_bp.route('/api/insights')
@require_auth()
def api_learning_insights():
    """获取用户学习洞察API"""
    user_id = session.get('user_id')

    try:
        insights = FeatureLearningService.get_user_learning_insights(user_id)
        return jsonify({
            'success': True,
            'data': insights
        })

    except Exception as e:
        logger.error("获取学习洞察失败", user_id=user_id, error=str(e))
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        }), 500


@feature_learning_bp.route('/api/detailed-insights')
@require_auth()
def api_detailed_learning_insights():
    """获取用户详细学习洞察API"""
    user_id = session.get('user_id')

    try:
        detailed_insights = FeatureLearningService.get_detailed_learning_insights(user_id)
        return jsonify({
            'success': True,
            'data': detailed_insights
        })

    except Exception as e:
        logger.error("获取详细学习洞察失败", user_id=user_id, error=str(e))
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        }), 500


@feature_learning_bp.route('/api/predict-difficulty', methods=['POST'])
@require_auth()
def api_predict_difficulty():
    """预测单词难度API"""
    user_id = session.get('user_id')
    
    try:
        data = request.get_json()
        word_info = {
            'word_length': data.get('word_length', 0),
            'word_difficulty': data.get('word_difficulty', 3),
            'english_word': data.get('english_word', ''),
            'chinese_meaning': data.get('chinese_meaning', '')
        }
        
        prediction = FeatureLearningService.predict_word_difficulty(user_id, word_info)
        
        return jsonify({
            'success': True,
            'data': prediction
        })
        
    except Exception as e:
        logger.error("预测单词难度失败", user_id=user_id, error=str(e))
        return jsonify({
            'success': False,
            'message': f'预测失败: {str(e)}'
        }), 500


@feature_learning_bp.route('/api/recommendations')
@require_auth()  
def api_get_recommendations():
    """获取个性化学习建议API"""
    user_id = session.get('user_id')
    
    try:
        recommendations = PatternLearner.recommend_review_strategy(user_id)
        
        return jsonify({
            'success': True,
            'data': recommendations
        })
        
    except Exception as e:
        logger.error("获取学习建议失败", user_id=user_id, error=str(e))
        return jsonify({
            'success': False,
            'message': f'获取失败: {str(e)}'
        }), 500


@feature_learning_bp.route('/api/update-patterns', methods=['POST'])
@require_auth()
def api_update_patterns():
    """手动更新用户学习模式API"""
    user_id = session.get('user_id')
    
    try:
        result = PatternLearner.learn_user_patterns(user_id)
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error("更新学习模式失败", user_id=user_id, error=str(e))
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500


@feature_learning_bp.route('/api/enhanced-similarity', methods=['POST'])
@require_auth()
def api_enhanced_similarity():
    """增强相似度检查API（用于前端实时反馈）"""
    try:
        data = request.get_json()
        user_input = data.get('user_input', '')
        correct_answer = data.get('correct_answer', '')
        
        if not user_input or not correct_answer:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
        
        similarity_result = FeatureLearningService.enhanced_similarity_check(
            user_input, correct_answer
        )
        
        return jsonify({
            'success': True,
            'data': similarity_result
        })
        
    except Exception as e:
        logger.error("增强相似度检查失败", error=str(e))
        return jsonify({
            'success': False,
            'message': f'检查失败: {str(e)}'
        }), 500


@feature_learning_bp.route('/admin/config')
@require_auth()
def admin_config():
    """特征学习配置管理页面（管理员）"""
    user_id = session.get('user_id')
    
    # 简单的管理员检查（实际应该有更完善的权限系统）
    if user_id != 1:  # 假设user_id=1是管理员
        return "权限不足", 403
    
    try:
        configs = FeatureLearningConfig.get_all_configs()
        return render_template('feature_learning/admin_config.html', configs=configs)
        
    except Exception as e:
        logger.error("获取配置失败", error=str(e))
        return f"获取配置失败: {str(e)}", 500


@feature_learning_bp.route('/admin/api/config', methods=['POST'])
@require_auth()
def admin_api_update_config():
    """更新特征学习配置API（管理员）"""
    user_id = session.get('user_id')
    
    # 管理员权限检查
    if user_id != 1:
        return jsonify({
            'success': False,
            'message': '权限不足'
        }), 403
    
    try:
        data = request.get_json()
        config_key = data.get('config_key')
        config_value = data.get('config_value')
        description = data.get('description', '')
        
        if not config_key or not config_value:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
        
        FeatureLearningConfig.set_config(config_key, config_value, description)
        
        return jsonify({
            'success': True,
            'message': '配置更新成功'
        })
        
    except Exception as e:
        logger.error("更新配置失败", error=str(e))
        return jsonify({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }), 500


@feature_learning_bp.route('/api/similarity-test', methods=['POST'])
def api_similarity_test():
    """相似度测试API（开发调试用）"""
    try:
        data = request.get_json()
        test_cases = data.get('test_cases', [])
        
        results = []
        for case in test_cases:
            user_input = case.get('user_input', '')
            correct_answer = case.get('correct_answer', '')
            
            if user_input and correct_answer:
                similarity_result = FeatureLearningService.enhanced_similarity_check(
                    user_input, correct_answer
                )
                
                results.append({
                    'user_input': user_input,
                    'correct_answer': correct_answer,
                    'result': similarity_result
                })
        
        return jsonify({
            'success': True,
            'data': results
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'测试失败: {str(e)}'
        }), 500