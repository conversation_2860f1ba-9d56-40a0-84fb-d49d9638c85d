"""
免复习选择功能的路由
处理用户在完成每日学习后的免复习选择请求
"""

from flask import request, jsonify, session
from datetime import date, datetime
import logging

from ..services.learning.skip_review_service import SkipReviewService
from ..core.decorators import require_auth, timing

logger = logging.getLogger(__name__)

def register_skip_review_routes(app):
    """注册免复习选择相关的路由"""
    
    @app.route('/api/skip-review/today-words', methods=['GET'])
    @require_auth()
    @timing()
    def get_today_learned_words():
        """
        获取今日学习的单词列表，用于免复习选择
        
        Query Parameters:
            date: 可选，指定日期 (YYYY-MM-DD格式)，默认为今天
            
        Returns:
            JSON: 今日学习的单词列表
        """
        try:
            user_id = session.get('user_id')
            
            # 解析日期参数
            date_str = request.args.get('date')
            learning_date = date.today()
            
            if date_str:
                try:
                    learning_date = date.fromisoformat(date_str)
                except ValueError:
                    return jsonify({
                        'success': False,
                        'message': '日期格式错误，请使用YYYY-MM-DD格式'
                    }), 400
            
            # 获取今日学习的单词
            words = SkipReviewService.get_today_learned_words(user_id, learning_date)
            
            return jsonify({
                'success': True,
                'data': {
                    'words': words,
                    'total_count': len(words),
                    'learning_date': learning_date.isoformat(),
                    'recommend_count': sum(1 for w in words if w['recommend_skip'])
                }
            })
            
        except Exception as e:
            logger.error(f"获取今日学习单词失败: user_id={session.get('user_id')}, error={e}")
            return jsonify({
                'success': False,
                'message': '获取学习单词失败，请稍后重试'
            }), 500
    
    @app.route('/api/skip-review/set', methods=['POST'])
    @require_auth()
    @timing()
    def set_skip_review():
        """
        设置指定单词的免复习期
        
        Request Body:
            {
                "word_ids": [1, 2, 3],
                "skip_days": 7
            }
            
        Returns:
            JSON: 操作结果
        """
        try:
            user_id = session.get('user_id')
            data = request.get_json()
            
            if not data:
                return jsonify({
                    'success': False,
                    'message': '请求数据不能为空'
                }), 400
            
            word_ids = data.get('word_ids', [])
            skip_days = data.get('skip_days', 7)
            
            # 验证参数
            if not isinstance(word_ids, list) or not word_ids:
                return jsonify({
                    'success': False,
                    'message': '请选择至少一个单词'
                }), 400
            
            if not isinstance(skip_days, int) or skip_days <= 0 or skip_days > 365:
                return jsonify({
                    'success': False,
                    'message': '免复习天数必须是1-365之间的整数'
                }), 400
            
            # 设置免复习
            result = SkipReviewService.set_skip_review(user_id, word_ids, skip_days)
            
            if result['success']:
                return jsonify(result)
            else:
                return jsonify(result), 400
                
        except Exception as e:
            logger.error(f"设置免复习失败: user_id={session.get('user_id')}, error={e}")
            return jsonify({
                'success': False,
                'message': '设置失败，请稍后重试'
            }), 500
    
    @app.route('/api/skip-review/clear', methods=['POST'])
    @require_auth()
    @timing()
    def clear_skip_review():
        """
        清除指定单词的免复习期
        
        Request Body:
            {
                "word_ids": [1, 2, 3]  // 可选，为空表示清除所有
            }
            
        Returns:
            JSON: 操作结果
        """
        try:
            user_id = session.get('user_id')
            data = request.get_json() or {}
            
            word_ids = data.get('word_ids')
            
            # 如果指定了word_ids，验证格式
            if word_ids is not None:
                if not isinstance(word_ids, list):
                    return jsonify({
                        'success': False,
                        'message': 'word_ids必须是数组格式'
                    }), 400
            
            # 清除免复习
            result = SkipReviewService.clear_skip_review(user_id, word_ids)
            
            if result['success']:
                return jsonify(result)
            else:
                return jsonify(result), 400
                
        except Exception as e:
            logger.error(f"清除免复习失败: user_id={session.get('user_id')}, error={e}")
            return jsonify({
                'success': False,
                'message': '清除失败，请稍后重试'
            }), 500
    
    @app.route('/api/skip-review/statistics', methods=['GET'])
    @require_auth()
    @timing()
    def get_skip_review_statistics():
        """
        获取用户的免复习统计信息
        
        Returns:
            JSON: 统计信息
        """
        try:
            user_id = session.get('user_id')
            
            # 获取统计信息
            stats = SkipReviewService.get_skip_review_statistics(user_id)
            
            return jsonify({
                'success': True,
                'data': stats
            })
            
        except Exception as e:
            logger.error(f"获取免复习统计失败: user_id={session.get('user_id')}, error={e}")
            return jsonify({
                'success': False,
                'message': '获取统计信息失败，请稍后重试'
            }), 500
    
    @app.route('/api/skip-review/quick-set', methods=['POST'])
    @require_auth()
    @timing()
    def quick_set_by_proficiency():
        """
        根据熟练度快速设置免复习
        
        Request Body:
            {
                "min_proficiency": 80,  // 最低熟练度
                "skip_days": 7,
                "learning_date": "2025-01-17"  // 可选，默认今天
            }
            
        Returns:
            JSON: 操作结果
        """
        try:
            user_id = session.get('user_id')
            data = request.get_json()
            
            if not data:
                return jsonify({
                    'success': False,
                    'message': '请求数据不能为空'
                }), 400
            
            min_proficiency = data.get('min_proficiency', 80)
            skip_days = data.get('skip_days', 7)
            date_str = data.get('learning_date')
            
            # 解析学习日期
            learning_date = date.today()
            if date_str:
                try:
                    learning_date = date.fromisoformat(date_str)
                except ValueError:
                    return jsonify({
                        'success': False,
                        'message': '日期格式错误，请使用YYYY-MM-DD格式'
                    }), 400
            
            # 验证参数
            if not isinstance(min_proficiency, (int, float)) or min_proficiency < 0 or min_proficiency > 100:
                return jsonify({
                    'success': False,
                    'message': '最低熟练度必须是0-100之间的数值'
                }), 400
            
            if not isinstance(skip_days, int) or skip_days <= 0 or skip_days > 365:
                return jsonify({
                    'success': False,
                    'message': '免复习天数必须是1-365之间的整数'
                }), 400
            
            # 获取今日学习的单词
            words = SkipReviewService.get_today_learned_words(user_id, learning_date)
            
            # 筛选符合条件的单词
            eligible_words = [
                w for w in words 
                if w['proficiency'] >= min_proficiency and not w['is_currently_skipped']
            ]
            
            if not eligible_words:
                return jsonify({
                    'success': False,
                    'message': f'没有找到熟练度≥{min_proficiency}%的单词'
                })
            
            # 提取单词ID
            word_ids = [w['word_id'] for w in eligible_words]
            
            # 设置免复习
            result = SkipReviewService.set_skip_review(user_id, word_ids, skip_days)
            
            if result['success']:
                result['eligible_words'] = eligible_words
                result['criteria'] = {
                    'min_proficiency': min_proficiency,
                    'skip_days': skip_days,
                    'learning_date': learning_date.isoformat()
                }
                return jsonify(result)
            else:
                return jsonify(result), 400
                
        except Exception as e:
            logger.error(f"快速设置免复习失败: user_id={session.get('user_id')}, error={e}")
            return jsonify({
                'success': False,
                'message': '快速设置失败，请稍后重试'
            }), 500