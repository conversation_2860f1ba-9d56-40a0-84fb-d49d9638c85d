"""
添加免复习功能的数据库迁移
为user_word表增加skip_review_until字段，支持用户选择免复习天数
"""

import sqlite3
import logging
from datetime import datetime
from typing import Optional

logger = logging.getLogger(__name__)

def run_migration(db_path: str) -> bool:
    """
    执行数据库迁移：为user_word表添加skip_review_until字段
    
    Args:
        db_path: 数据库文件路径
        
    Returns:
        bool: 迁移是否成功
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(user_word)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'skip_review_until' not in columns:
            logger.info("开始添加skip_review_until字段...")
            
            # 添加skip_review_until字段
            cursor.execute("""
                ALTER TABLE user_word 
                ADD COLUMN skip_review_until DATE
            """)
            
            # 创建索引以提高查询性能
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_user_word_skip_review 
                ON user_word(user_id, skip_review_until)
            """)
            
            conn.commit()
            logger.info("✅ skip_review_until字段添加成功")
            
            # 验证字段是否正确添加
            cursor.execute("PRAGMA table_info(user_word)")
            updated_columns = [column[1] for column in cursor.fetchall()]
            
            if 'skip_review_until' in updated_columns:
                logger.info("✅ 字段验证通过")
                return True
            else:
                logger.error("❌ 字段验证失败")
                return False
        else:
            logger.info("skip_review_until字段已存在，跳过迁移")
            return True
            
    except Exception as e:
        logger.error(f"❌ 迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def rollback_migration(db_path: str) -> bool:
    """
    回滚迁移：移除skip_review_until字段
    注意：SQLite不支持DROP COLUMN，需要重建表
    
    Args:
        db_path: 数据库文件路径
        
    Returns:
        bool: 回滚是否成功
    """
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        logger.info("开始回滚skip_review_until字段...")
        
        # SQLite不支持DROP COLUMN，需要重建表
        # 1. 创建备份表
        cursor.execute("""
            CREATE TABLE user_word_backup AS 
            SELECT user_id, word_id, proficiency, learning_count, correct_count, 
                   last_learning_date, status, created_at, updated_at
            FROM user_word
        """)
        
        # 2. 删除原表
        cursor.execute("DROP TABLE user_word")
        
        # 3. 重建原表（不含skip_review_until字段）
        cursor.execute("""
            CREATE TABLE user_word (
                user_id INTEGER NOT NULL,
                word_id INTEGER NOT NULL,
                proficiency REAL DEFAULT 0.0,
                learning_count INTEGER DEFAULT 0,
                correct_count INTEGER DEFAULT 0,
                last_learning_date DATE,
                status TEXT DEFAULT 'new',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (user_id, word_id),
                FOREIGN KEY (user_id) REFERENCES user(id),
                FOREIGN KEY (word_id) REFERENCES word(id)
            )
        """)
        
        # 4. 恢复数据
        cursor.execute("""
            INSERT INTO user_word 
            SELECT * FROM user_word_backup
        """)
        
        # 5. 删除备份表
        cursor.execute("DROP TABLE user_word_backup")
        
        # 6. 重建索引
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_user_word_user_id 
            ON user_word(user_id)
        """)
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_user_word_status 
            ON user_word(status)
        """)
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_user_word_proficiency 
            ON user_word(proficiency)
        """)
        
        conn.commit()
        logger.info("✅ skip_review_until字段回滚成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 回滚失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    # 测试迁移
    import sys
    import os
    
    # 添加项目根目录到路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 执行迁移
    db_path = "instance/words.db"
    if os.path.exists(db_path):
        success = run_migration(db_path)
        if success:
            print("✅ 迁移执行成功")
        else:
            print("❌ 迁移执行失败")
    else:
        print(f"❌ 数据库文件不存在: {db_path}")