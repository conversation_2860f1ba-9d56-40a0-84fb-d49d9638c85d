"""
数据库迁移：添加购物券日志表
支持购物券的即时更新机制和历史记录追踪
"""
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.models.base import db


def create_voucher_log_table():
    """
    创建购物券日志表
    用于记录购物券的获得和消耗历史，替代Session存储
    """
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS voucher_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        vouchers_change INTEGER NOT NULL,  -- 购物券变化量（正数获得，负数消耗）
        reason VARCHAR(255) NOT NULL,      -- 获得/消耗原因
        trigger_type VARCHAR(50) NOT NULL, -- 触发类型：answer_streak, session_complete, ai_help等
        date DATE NOT NULL,                -- 日期
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIG<PERSON> KEY (user_id) REFERENCES user(id)
    );
    """
    
    # 创建索引以优化查询性能
    create_indexes_sql = [
        """
        CREATE INDEX IF NOT EXISTS idx_voucher_log_user_date 
        ON voucher_log(user_id, date);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_voucher_log_user_type 
        ON voucher_log(user_id, trigger_type);
        """,
        """
        CREATE INDEX IF NOT EXISTS idx_voucher_log_date_change 
        ON voucher_log(date, vouchers_change);
        """
    ]
    
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建表
            cursor.execute(create_table_sql)
            print("✅ 购物券日志表创建成功")
            
            # 创建索引
            for index_sql in create_indexes_sql:
                cursor.execute(index_sql)
            print("✅ 购物券日志表索引创建成功")
            
            conn.commit()
            return True
            
    except Exception as e:
        print(f"❌ 创建购物券日志表失败: {e}")
        return False


def migrate_session_data_to_db():
    """
    迁移Session中的购物券数据到数据库
    这是一个一次性迁移，将现有的Session数据转移到数据库表中
    """
    try:
        # 注意：这个函数需要在有Flask应用上下文的环境中运行
        # 因为需要访问session数据
        from flask import session
        
        # 由于Session数据是临时的，这里主要是为了演示迁移逻辑
        # 实际部署时，Session中的数据会自然过期
        print("ℹ️ Session数据迁移：由于Session数据的临时性，无需特殊迁移")
        print("ℹ️ 新的购物券获得将直接记录到数据库表中")
        return True
        
    except Exception as e:
        print(f"⚠️ Session数据迁移警告: {e}")
        return True  # 不阻止迁移进程


def run_migration():
    """
    执行完整的购物券系统迁移
    """
    print("🚀 开始购物券系统迁移...")
    
    # 1. 创建购物券日志表
    if not create_voucher_log_table():
        print("❌ 购物券日志表创建失败，迁移中止")
        return False
    
    # 2. 迁移Session数据（可选）
    migrate_session_data_to_db()
    
    # 3. 验证表结构
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='voucher_log'
            """)
            if not cursor.fetchone():
                print("❌ 购物券日志表验证失败")
                return False
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(voucher_log)")
            columns = cursor.fetchall()
            expected_columns = ['id', 'user_id', 'vouchers_change', 'reason', 'trigger_type', 'date', 'created_at']
            actual_columns = [col[1] for col in columns]
            
            for expected_col in expected_columns:
                if expected_col not in actual_columns:
                    print(f"❌ 缺少列: {expected_col}")
                    return False
            
            print("✅ 购物券日志表结构验证成功")
            
    except Exception as e:
        print(f"❌ 表结构验证失败: {e}")
        return False
    
    print("🎉 购物券系统迁移完成！")
    print("📊 新功能：")
    print("  - 购物券获得/消耗历史记录")
    print("  - 基于数据库的每日限制检查")
    print("  - 即时购物券奖励机制")
    print("  - 购物券使用统计分析")
    
    return True


def rollback_migration():
    """
    回滚购物券系统迁移
    """
    print("🔄 开始回滚购物券系统迁移...")
    
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 删除购物券日志表
            cursor.execute("DROP TABLE IF EXISTS voucher_log")
            
            # 删除相关索引（SQLite会自动删除）
            
            conn.commit()
            print("✅ 购物券日志表删除成功")
            
    except Exception as e:
        print(f"❌ 回滚失败: {e}")
        return False
    
    print("🎉 购物券系统迁移回滚完成！")
    return True


if __name__ == "__main__":
    # 直接运行此脚本进行迁移
    import sys
    import os
    
    # 添加项目根目录到Python路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_migration()
    else:
        run_migration()
