"""
添加用户输入特征学习相关表
支持用户输入行为分析和个性化学习推荐
"""
import sqlite3
from ..models.models import db

def create_feature_learning_tables():
    """创建特征学习相关的数据表"""
    
    with db.get_connection() as conn:
        cursor = conn.cursor()
        
        # 1. 用户输入特征表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_input_features (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            word_id INTEGER NOT NULL,
            record_id INTEGER NOT NULL,
            feature_data TEXT NOT NULL,  -- JSON格式存储特征向量
            extracted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            feature_version VARCHAR(10) DEFAULT '1.0',
            FOREIGN KEY (user_id) REFERENCES user(id),
            FOREIGN KEY (word_id) REFERENCES word(id),
            FOREIGN KEY (record_id) REFERENCES word_record(id)
        )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_features ON user_input_features (user_id, word_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_record_features ON user_input_features (record_id)')
        
        # 2. 用户学习模式表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_learning_patterns (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            pattern_type VARCHAR(50) NOT NULL,  -- error_type, learning_speed, difficulty_preference等
            pattern_data TEXT NOT NULL,  -- JSON格式存储模式数据
            confidence_score REAL DEFAULT 0.0,  -- 模式置信度 0-1
            sample_count INTEGER DEFAULT 0,  -- 样本数量
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES user(id),
            UNIQUE(user_id, pattern_type)
        )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_patterns ON user_learning_patterns (user_id, pattern_type)')
        
        # 3. 单词特征统计表（群体数据）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS word_features_stats (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            word_id INTEGER NOT NULL,
            feature_type VARCHAR(50) NOT NULL,  -- difficulty, common_errors, avg_time等
            feature_value TEXT NOT NULL,  -- JSON格式
            sample_count INTEGER DEFAULT 0,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (word_id) REFERENCES word(id),
            UNIQUE(word_id, feature_type)
        )
        ''')
        
        # 创建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_word_stats ON word_features_stats (word_id, feature_type)')
        
        # 4. 特征学习配置表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS feature_learning_config (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            config_key VARCHAR(100) NOT NULL UNIQUE,
            config_value TEXT NOT NULL,  -- JSON格式配置值
            description TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        conn.commit()
        print("✅ 特征学习数据表创建成功")

def insert_default_config():
    """插入默认配置"""
    
    default_configs = [
        {
            'key': 'feature_extraction_enabled',
            'value': '{"enabled": true, "async": true}',
            'description': '特征提取开关配置'
        },
        {
            'key': 'similarity_algorithms',
            'value': '{"levenshtein": true, "ngram": true, "phonetic": false}',
            'description': '相似度算法配置'
        },
        {
            'key': 'pattern_learning_thresholds',
            'value': '{"min_samples": 10, "confidence_threshold": 0.7, "update_frequency": 24}',
            'description': '模式学习阈值配置'
        },
        {
            'key': 'feature_retention_policy',
            'value': '{"days_to_keep": 365, "max_records_per_user": 10000}',
            'description': '特征数据保留策略'
        }
    ]
    
    with db.get_connection() as conn:
        cursor = conn.cursor()
        
        for config in default_configs:
            cursor.execute('''
            INSERT OR REPLACE INTO feature_learning_config 
            (config_key, config_value, description, updated_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (config['key'], config['value'], config['description']))
        
        conn.commit()
        print("✅ 默认配置插入成功")

def migrate():
    """执行迁移"""
    try:
        create_feature_learning_tables()
        insert_default_config()
        print("🎉 特征学习表迁移完成")
        return True
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return False

if __name__ == "__main__":
    migrate()