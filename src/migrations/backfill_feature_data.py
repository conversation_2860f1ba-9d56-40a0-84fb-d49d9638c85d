#!/usr/bin/env python3
"""
回填特征数据迁移脚本
为现有的学习记录生成特征数据，使学习洞察功能能够正常工作
"""
import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from src.models.base import db
from src.services.feature_learning.feature_extractor import FeatureExtractor
from src.models.feature_learning import UserInputFeatures


class FeatureDataBackfill:
    """特征数据回填工具"""
    
    def __init__(self, batch_size: int = 100):
        self.batch_size = batch_size
        self.processed_count = 0
        self.error_count = 0
        
    def run_backfill(self, limit: int = None, user_id: int = None):
        """
        执行特征数据回填
        
        Args:
            limit: 处理记录数量限制
            user_id: 指定用户ID（可选）
        """
        print("🚀 开始特征数据回填...")
        
        # 获取需要处理的学习记录
        records = self._get_records_to_process(limit, user_id)
        total_records = len(records)
        
        if total_records == 0:
            print("✅ 没有需要处理的记录")
            return
            
        print(f"📊 找到 {total_records} 条需要处理的记录")
        
        # 分批处理
        for i in range(0, total_records, self.batch_size):
            batch = records[i:i + self.batch_size]
            self._process_batch(batch, i + 1, total_records)
            
        print(f"\n🎉 回填完成！")
        print(f"✅ 成功处理: {self.processed_count} 条记录")
        print(f"❌ 处理失败: {self.error_count} 条记录")
        
    def _get_records_to_process(self, limit: int = None, user_id: int = None) -> List[Dict[str, Any]]:
        """获取需要处理的学习记录"""
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        if user_id:
            where_conditions.append("wr.user_id = ?")
            params.append(user_id)
            
        # 只处理还没有特征数据的记录
        where_conditions.append("""
            wr.id NOT IN (
                SELECT DISTINCT record_id 
                FROM user_input_features 
                WHERE record_id IS NOT NULL
            )
        """)
        
        where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        limit_clause = f"LIMIT {limit}" if limit else ""
        
        query = f"""
        SELECT 
            wr.id as record_id,
            wr.user_id,
            wr.word_id,
            wr.user_input,
            wr.duration_seconds,
            wr.is_correct,
            wr.date,
            w.english_word as correct_answer,
            w.chinese_meaning,
            w.section
        FROM word_record wr
        JOIN word w ON wr.word_id = w.id
        {where_clause}
        ORDER BY wr.id DESC
        {limit_clause}
        """
        
        return db.execute_query(query, params)
    
    def _process_batch(self, batch: List[Dict[str, Any]], start_index: int, total: int):
        """处理一批记录"""
        print(f"🔄 处理批次 {start_index}-{min(start_index + len(batch) - 1, total)} / {total}")
        
        for record in batch:
            try:
                self._process_single_record(record)
                self.processed_count += 1
                
                # 每处理10条记录显示一次进度
                if self.processed_count % 10 == 0:
                    print(f"   ✅ 已处理 {self.processed_count} 条记录")
                    
            except Exception as e:
                self.error_count += 1
                print(f"   ❌ 处理记录 {record['record_id']} 失败: {str(e)}")
                
    def _process_single_record(self, record: Dict[str, Any]):
        """处理单条记录"""
        user_input = record['user_input'] or ""
        correct_answer = record['correct_answer'] or ""
        duration_seconds = record['duration_seconds'] or 0.0
        
        # 准备上下文信息
        context = {
            'word_length': len(correct_answer),
            'word_difficulty': 3,  # 默认难度
            'session_position': 1,  # 默认位置
            'learning_stage': 'practice',
            'backfill_mode': True,
            'original_date': record['date']
        }
        
        # 提取特征
        features = FeatureExtractor.extract_all_features(
            user_input, correct_answer, duration_seconds, context
        )
        
        # 保存特征数据
        UserInputFeatures.create(
            user_id=record['user_id'],
            word_id=record['word_id'],
            record_id=record['record_id'],
            feature_data=features
        )


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='回填特征数据')
    parser.add_argument('--limit', type=int, help='处理记录数量限制')
    parser.add_argument('--user-id', type=int, help='指定用户ID')
    parser.add_argument('--batch-size', type=int, default=100, help='批处理大小')
    
    args = parser.parse_args()
    
    backfill = FeatureDataBackfill(batch_size=args.batch_size)
    backfill.run_backfill(limit=args.limit, user_id=args.user_id)


if __name__ == '__main__':
    main()
