"""
增强版语言学专家推荐服务
解决数据稀疏问题，提供更智能的推荐算法
"""

import sqlite3
import json
import re
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass
from src.core import get_logger
from .pattern.recommendation_config import recommendation_config_manager
from .linguistic_recommender import LinguisticRecommenderService, LinguisticRecommendation, LinguisticSimilarWord

logger = get_logger(__name__)

@dataclass
class SmartRecommendationContext:
    """智能推荐上下文"""
    user_id: int
    target_word_id: int
    target_word: str
    target_meaning: str
    user_proficiency_level: str  # 'beginner', 'intermediate', 'advanced'
    learned_words_count: int
    recent_errors: List[str]
    learning_preferences: Dict[str, float]

class EnhancedLinguisticRecommenderService(LinguisticRecommenderService):
    """增强版语言学推荐服务"""
    
    def __init__(self, db_path: str = 'instance/words.db'):
        super().__init__(db_path)
        self._similarity_threshold = 0.3
        self._max_fallback_recommendations = 8
        
        # 语言学相似度权重 (已移除语义相关权重)
        self.similarity_weights = {
            'exact_prefix_match': 1.0,      # 完全前缀匹配
            'prefix_family': 0.8,           # 前缀家族
            'morphology_pattern': 0.7,      # 构词模式
            'phonetic_similarity': 0.5,     # 语音相似
            'orthographic_pattern': 0.4,    # 拼写模式
            'learning_difficulty': 0.3,     # 学习难度相似
        }
    
    def get_linguistic_recommendations(self, user_id: int, word_id: int, 
                                     context: str = 'learning') -> List[LinguisticRecommendation]:
        """获取增强版语言学推荐"""
        try:
            # 构建推荐上下文
            rec_context = self._build_recommendation_context(user_id, word_id)
            
            # 尝试基础语言学推荐
            base_recommendations = super().get_linguistic_recommendations(user_id, word_id, context)
            
            # 如果基础推荐不足，使用智能补全
            if len(base_recommendations) < 2:
                logger.info(f"基础推荐不足({len(base_recommendations)})，启用智能补全")
                enhanced_recommendations = self._get_smart_fallback_recommendations(rec_context, context)
                base_recommendations.extend(enhanced_recommendations)
            
            # 应用智能排序和过滤
            final_recommendations = self._apply_smart_ranking(base_recommendations, rec_context, context)
            
            return final_recommendations[:self._get_max_recommendations(rec_context, context)]
            
        except Exception as e:
            logger.error(f"增强版语言学推荐失败: {e}")
            return []
    
    def _build_recommendation_context(self, user_id: int, word_id: int) -> SmartRecommendationContext:
        """构建推荐上下文"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取目标单词信息
            cursor.execute("SELECT english_word, chinese_meaning FROM word WHERE id = ?", (word_id,))
            word_info = cursor.fetchone()
            target_word, target_meaning = word_info if word_info else ("", "")
            
            # 获取用户学习统计
            cursor.execute("""
                SELECT COUNT(*) as learned_count, AVG(proficiency) as avg_proficiency
                FROM user_word 
                WHERE user_id = ? AND status != 'new'
            """, (user_id,))
            stats = cursor.fetchone()
            learned_count, avg_proficiency = stats if stats else (0, 0)
            
            # 确定用户水平
            if avg_proficiency >= 80:
                proficiency_level = 'advanced'
            elif avg_proficiency >= 50:
                proficiency_level = 'intermediate'
            else:
                proficiency_level = 'beginner'
            
            # 获取最近错误
            cursor.execute("""
                SELECT wr.user_input, w.english_word
                FROM word_record wr
                JOIN word w ON wr.word_id = w.id
                WHERE wr.user_id = ? AND wr.is_correct = 0
                ORDER BY wr.date DESC
                LIMIT 10
            """, (user_id,))
            recent_errors = [row[0] for row in cursor.fetchall() if row[0]]
            
            conn.close()
            
            # 获取学习偏好 (已移除语义权重)
            config = recommendation_config_manager.get_user_config(user_id)
            learning_preferences = {
                'morphology_weight': config.morphology_weight_factor,
                'phonetic_weight': config.phonetic_weight_factor,
                'difficulty_preference': getattr(config, 'difficulty_preference', 0.5)
            }
            
            return SmartRecommendationContext(
                user_id=user_id,
                target_word_id=word_id,
                target_word=target_word,
                target_meaning=target_meaning,
                user_proficiency_level=proficiency_level,
                learned_words_count=learned_count,
                recent_errors=recent_errors,
                learning_preferences=learning_preferences
            )
            
        except Exception as e:
            logger.error(f"构建推荐上下文失败: {e}")
            return SmartRecommendationContext(
                user_id=user_id,
                target_word_id=word_id,
                target_word="",
                target_meaning="",
                user_proficiency_level='beginner',
                learned_words_count=0,
                recent_errors=[],
                learning_preferences={}
            )
    
    def _get_smart_fallback_recommendations(self, context: SmartRecommendationContext, 
                                          learning_context: str) -> List[LinguisticRecommendation]:
        """智能回退推荐算法"""
        recommendations = []
        
        try:
            # 1. 基于拼写模式的推荐
            orthographic_rec = self._get_orthographic_pattern_recommendations(context)
            if orthographic_rec:
                recommendations.append(orthographic_rec)
            
            # 2. 基于语音相似的推荐
            phonetic_rec = self._get_phonetic_similarity_recommendations(context)
            if phonetic_rec:
                recommendations.append(phonetic_rec)
            
            # 3. 基于学习难度的推荐
            difficulty_rec = self._get_difficulty_based_recommendations(context)
            if difficulty_rec:
                recommendations.append(difficulty_rec)
            
            # 4. 基于用户错误模式的推荐
            if context.recent_errors and learning_context == 'learning':
                error_pattern_rec = self._get_error_pattern_recommendations(context)
                if error_pattern_rec:
                    recommendations.append(error_pattern_rec)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"智能回退推荐失败: {e}")
            return []
    
    def _get_orthographic_pattern_recommendations(self, context: SmartRecommendationContext) -> Optional[LinguisticRecommendation]:
        """基于拼写模式的推荐"""
        try:
            target_word = context.target_word.lower()
            similar_words = []
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取用户已学单词
            cursor.execute("""
                SELECT w.id, w.english_word, w.chinese_meaning, uw.proficiency, uw.status
                FROM word w
                JOIN user_word uw ON w.id = uw.word_id
                WHERE uw.user_id = ? AND uw.status != 'new' AND w.id != ?
            """, (context.user_id, context.target_word_id))
            
            learned_words = cursor.fetchall()
            
            for word_data in learned_words:
                word_id, english_word, chinese_meaning, proficiency, status = word_data
                similarity = self._calculate_orthographic_similarity(target_word, english_word.lower())
                
                if similarity >= self._similarity_threshold:
                    similar_words.append(LinguisticSimilarWord(
                        word_id=word_id,
                        english_word=english_word,
                        chinese_meaning=chinese_meaning,
                        similarity_reason=f"拼写模式相似度: {similarity:.2f}",
                        similarity_type='orthographic',
                        confidence=similarity,
                        proficiency=proficiency,
                        learning_status=status
                    ))
            
            conn.close()
            
            if similar_words:
                # 按相似度排序
                similar_words.sort(key=lambda x: x.confidence, reverse=True)
                
                return LinguisticRecommendation(
                    category='orthographic_pattern',
                    category_name='拼写模式相似',
                    explanation=f"这些单词与 '{context.target_word}' 有相似的拼写模式，有助于记忆和区分",
                    linguistic_principle="拼写模式识别有助于建立视觉记忆联系，提高单词识别速度",
                    similar_words=similar_words[:5],
                    educational_value="中等"
                )
            
            return None
            
        except Exception as e:
            logger.error(f"拼写模式推荐失败: {e}")
            return None
    
    def _calculate_orthographic_similarity(self, word1: str, word2: str) -> float:
        """计算拼写相似度"""
        if not word1 or not word2:
            return 0.0
        
        # 长度相似度
        len_similarity = 1 - abs(len(word1) - len(word2)) / max(len(word1), len(word2))
        
        # 字符重叠度
        set1, set2 = set(word1), set(word2)
        char_overlap = len(set1 & set2) / len(set1 | set2)
        
        # 开头结尾相似度
        start_similarity = 1.0 if word1[:2] == word2[:2] else 0.0
        end_similarity = 1.0 if word1[-2:] == word2[-2:] else 0.0
        
        # 编辑距离相似度
        edit_distance = self._levenshtein_distance(word1, word2)
        edit_similarity = 1 - edit_distance / max(len(word1), len(word2))
        
        # 加权平均
        similarity = (
            len_similarity * 0.2 +
            char_overlap * 0.3 +
            start_similarity * 0.2 +
            end_similarity * 0.1 +
            edit_similarity * 0.2
        )
        
        return similarity
    
    def _levenshtein_distance(self, s1: str, s2: str) -> int:
        """计算编辑距离"""
        if len(s1) < len(s2):
            return self._levenshtein_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
    
    def _get_phonetic_similarity_recommendations(self, context: SmartRecommendationContext) -> Optional[LinguisticRecommendation]:
        """基于语音相似的推荐"""
        try:
            target_word = context.target_word.lower()
            similar_words = []
            
            # 简单的语音模式匹配
            phonetic_patterns = self._extract_phonetic_patterns(target_word)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT w.id, w.english_word, w.chinese_meaning, uw.proficiency, uw.status
                FROM word w
                JOIN user_word uw ON w.id = uw.word_id
                WHERE uw.user_id = ? AND uw.status != 'new' AND w.id != ?
            """, (context.user_id, context.target_word_id))
            
            learned_words = cursor.fetchall()
            
            for word_data in learned_words:
                word_id, english_word, chinese_meaning, proficiency, status = word_data
                word_patterns = self._extract_phonetic_patterns(english_word.lower())
                
                similarity = self._calculate_phonetic_similarity(phonetic_patterns, word_patterns)
                
                if similarity >= self._similarity_threshold:
                    similar_words.append(LinguisticSimilarWord(
                        word_id=word_id,
                        english_word=english_word,
                        chinese_meaning=chinese_meaning,
                        similarity_reason=f"语音模式相似度: {similarity:.2f}",
                        similarity_type='phonetic',
                        confidence=similarity,
                        proficiency=proficiency,
                        learning_status=status
                    ))
            
            conn.close()
            
            if similar_words:
                similar_words.sort(key=lambda x: x.confidence, reverse=True)
                
                return LinguisticRecommendation(
                    category='phonetic_similarity',
                    category_name='语音模式相似',
                    explanation=f"这些单词与 '{context.target_word}' 有相似的发音模式，有助于语音记忆",
                    linguistic_principle="语音模式识别有助于建立听觉记忆联系，提高发音准确性",
                    similar_words=similar_words[:5],
                    educational_value="中等"
                )
            
            return None
            
        except Exception as e:
            logger.error(f"语音相似推荐失败: {e}")
            return None
    
    def _extract_phonetic_patterns(self, word: str) -> Set[str]:
        """提取语音模式"""
        patterns = set()
        
        # 元音模式
        vowels = 'aeiou'
        vowel_pattern = ''.join([c if c in vowels else 'C' for c in word])
        patterns.add(vowel_pattern)
        
        # 辅音簇
        consonant_clusters = re.findall(r'[bcdfghjklmnpqrstvwxyz]{2,}', word)
        patterns.update(consonant_clusters)
        
        # 押韵模式
        if len(word) >= 3:
            patterns.add(word[-3:])  # 最后3个字母
            patterns.add(word[-2:])  # 最后2个字母
        
        # 开头音
        if len(word) >= 2:
            patterns.add(word[:2])
        
        return patterns
    
    def _calculate_phonetic_similarity(self, patterns1: Set[str], patterns2: Set[str]) -> float:
        """计算语音相似度"""
        if not patterns1 or not patterns2:
            return 0.0
        
        intersection = patterns1 & patterns2
        union = patterns1 | patterns2
        
        return len(intersection) / len(union) if union else 0.0
    
    def _get_difficulty_based_recommendations(self, context: SmartRecommendationContext) -> Optional[LinguisticRecommendation]:
        """基于学习难度的推荐"""
        try:
            # 根据用户水平确定难度范围
            if context.user_proficiency_level == 'beginner':
                target_length_range = (3, 6)
                complexity_preference = 'simple'
            elif context.user_proficiency_level == 'intermediate':
                target_length_range = (4, 8)
                complexity_preference = 'moderate'
            else:
                target_length_range = (5, 12)
                complexity_preference = 'complex'
            
            similar_words = []
            target_length = len(context.target_word)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT w.id, w.english_word, w.chinese_meaning, uw.proficiency, uw.status
                FROM word w
                JOIN user_word uw ON w.id = uw.word_id
                WHERE uw.user_id = ? AND uw.status != 'new' AND w.id != ?
                AND LENGTH(w.english_word) BETWEEN ? AND ?
            """, (context.user_id, context.target_word_id, target_length_range[0], target_length_range[1]))
            
            learned_words = cursor.fetchall()
            
            for word_data in learned_words:
                word_id, english_word, chinese_meaning, proficiency, status = word_data
                
                # 计算难度相似度
                length_similarity = 1 - abs(len(english_word) - target_length) / max(len(english_word), target_length)
                
                if length_similarity >= 0.7:  # 长度相似
                    similar_words.append(LinguisticSimilarWord(
                        word_id=word_id,
                        english_word=english_word,
                        chinese_meaning=chinese_meaning,
                        similarity_reason=f"学习难度相似 (长度: {len(english_word)})",
                        similarity_type='difficulty',
                        confidence=length_similarity,
                        proficiency=proficiency,
                        learning_status=status
                    ))
            
            conn.close()
            
            if similar_words:
                similar_words.sort(key=lambda x: x.confidence, reverse=True)
                
                return LinguisticRecommendation(
                    category='difficulty_based',
                    category_name='难度匹配推荐',
                    explanation=f"这些单词与 '{context.target_word}' 难度相近，适合当前学习水平",
                    linguistic_principle="难度匹配的单词有助于建立学习信心，避免认知负荷过重",
                    similar_words=similar_words[:5],
                    educational_value="高"
                )
            
            return None
            
        except Exception as e:
            logger.error(f"难度推荐失败: {e}")
            return None
    
    def _get_error_pattern_recommendations(self, context: SmartRecommendationContext) -> Optional[LinguisticRecommendation]:
        """基于错误模式的推荐"""
        try:
            if not context.recent_errors:
                return None
            
            # 分析错误模式
            error_patterns = self._analyze_error_patterns(context.recent_errors, context.target_word)
            
            if not error_patterns:
                return None
            
            similar_words = []
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT w.id, w.english_word, w.chinese_meaning, uw.proficiency, uw.status
                FROM word w
                JOIN user_word uw ON w.id = uw.word_id
                WHERE uw.user_id = ? AND uw.status != 'new' AND w.id != ?
            """, (context.user_id, context.target_word_id))
            
            learned_words = cursor.fetchall()
            
            for word_data in learned_words:
                word_id, english_word, chinese_meaning, proficiency, status = word_data
                
                # 检查是否容易与目标单词混淆
                confusion_risk = self._calculate_confusion_risk(context.target_word, english_word, error_patterns)
                
                if confusion_risk >= 0.5:
                    similar_words.append(LinguisticSimilarWord(
                        word_id=word_id,
                        english_word=english_word,
                        chinese_meaning=chinese_meaning,
                        similarity_reason=f"易混淆对比 (风险度: {confusion_risk:.2f})",
                        similarity_type='error_prevention',
                        confidence=confusion_risk,
                        proficiency=proficiency,
                        learning_status=status
                    ))
            
            conn.close()
            
            if similar_words:
                similar_words.sort(key=lambda x: x.confidence, reverse=True)
                
                return LinguisticRecommendation(
                    category='error_prevention',
                    category_name='易混淆对比',
                    explanation=f"基于您的错误模式，这些单词容易与 '{context.target_word}' 混淆，建议对比学习",
                    linguistic_principle="对比学习易混淆单词有助于建立清晰的区分记忆，减少错误",
                    similar_words=similar_words[:4],
                    educational_value="很高"
                )
            
            return None
            
        except Exception as e:
            logger.error(f"错误模式推荐失败: {e}")
            return None
    
    def _analyze_error_patterns(self, errors: List[str], target_word: str) -> Dict[str, float]:
        """分析错误模式"""
        patterns = {}
        
        for error in errors:
            if not error or len(error) < 2:
                continue
            
            # 字母替换模式
            for i, (c1, c2) in enumerate(zip(error.lower(), target_word.lower())):
                if c1 != c2:
                    pattern = f"substitute_{c2}_with_{c1}"
                    patterns[pattern] = patterns.get(pattern, 0) + 1
            
            # 长度错误模式
            if len(error) != len(target_word):
                if len(error) < len(target_word):
                    patterns['too_short'] = patterns.get('too_short', 0) + 1
                else:
                    patterns['too_long'] = patterns.get('too_long', 0) + 1
        
        # 归一化
        total_errors = len(errors)
        if total_errors > 0:
            patterns = {k: v / total_errors for k, v in patterns.items()}
        
        return patterns
    
    def _calculate_confusion_risk(self, word1: str, word2: str, error_patterns: Dict[str, float]) -> float:
        """计算混淆风险"""
        risk = 0.0
        
        # 基础相似度风险
        base_similarity = self._calculate_orthographic_similarity(word1.lower(), word2.lower())
        risk += base_similarity * 0.5
        
        # 基于错误模式的风险
        for pattern, frequency in error_patterns.items():
            if pattern.startswith('substitute_'):
                # 检查是否存在容易替换的字母
                parts = pattern.split('_')
                if len(parts) >= 4:
                    target_char, error_char = parts[1], parts[3]
                    if target_char in word1.lower() and error_char in word2.lower():
                        risk += frequency * 0.3
        
        return min(risk, 1.0)
    
    def _apply_smart_ranking(self, recommendations: List[LinguisticRecommendation], 
                           context: SmartRecommendationContext, 
                           learning_context: str) -> List[LinguisticRecommendation]:
        """应用智能排序"""
        try:
            # 根据用户偏好和上下文调整权重
            for rec in recommendations:
                base_score = len(rec.similar_words)
                
                # 根据推荐类型调整分数
                type_multiplier = self._get_type_multiplier(rec.category, context, learning_context)
                
                # 根据用户水平调整
                level_multiplier = self._get_level_multiplier(rec.category, context.user_proficiency_level)
                
                # 计算最终分数
                rec.final_score = base_score * type_multiplier * level_multiplier
            
            # 按分数排序
            recommendations.sort(key=lambda x: getattr(x, 'final_score', 0), reverse=True)
            
            return recommendations
            
        except Exception as e:
            logger.error(f"智能排序失败: {e}")
            return recommendations
    
    def _get_type_multiplier(self, category: str, context: SmartRecommendationContext, learning_context: str) -> float:
        """获取类型权重倍数"""
        multipliers = {
            'prefix_true': 2.0,           # 真前缀最高优先级
            'orthographic_pattern': 1.2,  # 拼写模式
            'phonetic_similarity': 1.1,   # 语音相似
            'difficulty_based': 1.3,      # 难度匹配
            'error_prevention': 1.8,      # 错误预防
        }

        # 语义推荐已移除
        
        return multipliers.get(category, 1.0)
    
    def _get_level_multiplier(self, category: str, proficiency_level: str) -> float:
        """获取水平权重倍数"""
        level_preferences = {
            'beginner': {
                'orthographic_pattern': 1.3,
                'phonetic_similarity': 1.2,
                'difficulty_based': 1.5,
            },
            'intermediate': {
                'prefix_true': 1.3,
                'error_prevention': 1.4,
                'orthographic_pattern': 1.0,
            },
            'advanced': {
                'prefix_true': 1.5,
                'morphology_pattern': 1.4,
                'difficulty_based': 0.8,
            }
        }
        
        return level_preferences.get(proficiency_level, {}).get(category, 1.0)
    
    def _get_max_recommendations(self, context: SmartRecommendationContext, learning_context: str) -> int:
        """获取最大推荐数量"""
        if context.user_proficiency_level == 'beginner':
            return 2 if learning_context == 'testing' else 3
        elif context.user_proficiency_level == 'intermediate':
            return 3 if learning_context == 'testing' else 4
        else:
            return 4 if learning_context == 'testing' else 5
