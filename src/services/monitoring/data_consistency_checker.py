"""
数据一致性检查和自动修复工具
定期检查word_record和user_word表的数据一致性，并提供自动修复功能
"""

import sqlite3
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional
import logging

from ...core.database import db
from ...services.proficiency.calculator import ProficiencyCalculator

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataConsistencyChecker:
    """数据一致性检查器"""
    
    @staticmethod
    def check_user_consistency(user_id: int) -> Dict[str, Any]:
        """
        检查单个用户的数据一致性
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict: 检查结果
        """
        logger.info(f"开始检查用户{user_id}的数据一致性")
        
        # 查找不一致的单词
        inconsistent_query = """
        SELECT DISTINCT wr.word_id, w.english_word, w.chinese_meaning,
               COUNT(wr.id) as actual_learning_count,
               SUM(CASE WHEN wr.is_correct = 1 THEN 1 ELSE 0 END) as actual_correct_count,
               AVG(wr.duration_seconds) as actual_avg_duration,
               MAX(wr.date) as actual_last_date,
               uw.learning_count as stored_learning_count,
               uw.correct_count as stored_correct_count,
               uw.proficiency as stored_proficiency,
               uw.last_learning_date as stored_last_date,
               uw.status
        FROM word_record wr
        JOIN word w ON wr.word_id = w.id
        LEFT JOIN user_word uw ON wr.user_id = uw.user_id AND wr.word_id = uw.word_id
        WHERE wr.user_id = ?
        GROUP BY wr.word_id, w.english_word, w.chinese_meaning, 
                 uw.learning_count, uw.correct_count, uw.proficiency, 
                 uw.last_learning_date, uw.status
        HAVING actual_learning_count != COALESCE(uw.learning_count, 0) 
            OR actual_correct_count != COALESCE(uw.correct_count, 0)
            OR actual_last_date != uw.last_learning_date
        ORDER BY actual_learning_count DESC
        """
        
        inconsistent_words = db.execute_query(inconsistent_query, (user_id,))
        
        result = {
            'user_id': user_id,
            'check_time': datetime.now().isoformat(),
            'inconsistent_count': len(inconsistent_words),
            'inconsistent_words': inconsistent_words,
            'status': 'consistent' if len(inconsistent_words) == 0 else 'inconsistent'
        }
        
        logger.info(f"用户{user_id}检查完成: 发现{len(inconsistent_words)}个不一致单词")
        return result
    
    @staticmethod
    def check_all_users_consistency() -> Dict[str, Any]:
        """
        检查所有用户的数据一致性
        
        Returns:
            Dict: 全局检查结果
        """
        logger.info("开始检查所有用户的数据一致性")
        
        # 获取所有有学习记录的用户
        users_query = "SELECT DISTINCT user_id FROM word_record ORDER BY user_id"
        users = db.execute_query(users_query)
        
        results = []
        total_inconsistent = 0
        
        for user in users:
            user_id = user['user_id']
            user_result = DataConsistencyChecker.check_user_consistency(user_id)
            results.append(user_result)
            total_inconsistent += user_result['inconsistent_count']
        
        global_result = {
            'check_time': datetime.now().isoformat(),
            'total_users_checked': len(users),
            'total_inconsistent_words': total_inconsistent,
            'users_with_issues': len([r for r in results if r['status'] == 'inconsistent']),
            'user_results': results,
            'overall_status': 'consistent' if total_inconsistent == 0 else 'inconsistent'
        }
        
        logger.info(f"全局检查完成: {len(users)}个用户, {total_inconsistent}个不一致单词")
        return global_result
    
    @staticmethod
    def fix_user_inconsistency(user_id: int, dry_run: bool = True) -> Dict[str, Any]:
        """
        修复单个用户的数据不一致问题
        
        Args:
            user_id: 用户ID
            dry_run: 是否为预览模式
            
        Returns:
            Dict: 修复结果
        """
        logger.info(f"开始{'预览' if dry_run else '修复'}用户{user_id}的数据不一致")
        
        # 先检查不一致情况
        check_result = DataConsistencyChecker.check_user_consistency(user_id)
        inconsistent_words = check_result['inconsistent_words']
        
        if not inconsistent_words:
            return {
                'user_id': user_id,
                'action': 'fix',
                'dry_run': dry_run,
                'success_count': 0,
                'failed_count': 0,
                'message': '没有发现数据不一致问题'
            }
        
        success_count = 0
        failed_count = 0
        fix_details = []
        
        for word in inconsistent_words:
            word_id = word['word_id']
            english_word = word['english_word']
            
            try:
                # 计算正确的统计数据
                actual_learning_count = word['actual_learning_count']
                actual_correct_count = word['actual_correct_count']
                actual_avg_duration = word['actual_avg_duration'] or 0.0
                actual_last_date = word['actual_last_date']
                
                # 计算熟练度
                proficiency = ProficiencyCalculator.calculate_proficiency(
                    actual_learning_count, actual_correct_count, 
                    actual_avg_duration, actual_last_date
                )
                
                fix_detail = {
                    'word_id': word_id,
                    'english_word': english_word,
                    'before': {
                        'learning_count': word['stored_learning_count'] or 0,
                        'correct_count': word['stored_correct_count'] or 0,
                        'proficiency': word['stored_proficiency'] or 0.0,
                        'last_learning_date': word['stored_last_date']
                    },
                    'after': {
                        'learning_count': actual_learning_count,
                        'correct_count': actual_correct_count,
                        'proficiency': proficiency,
                        'last_learning_date': actual_last_date
                    }
                }
                
                if not dry_run:
                    # 执行实际修复
                    update_query = """
                    UPDATE user_word 
                    SET learning_count = ?, correct_count = ?, 
                        proficiency = ?, last_learning_date = ?
                    WHERE user_id = ? AND word_id = ?
                    """
                    
                    affected_rows = db.execute_update(update_query, (
                        actual_learning_count, actual_correct_count, 
                        proficiency, actual_last_date, user_id, word_id
                    ))
                    
                    if affected_rows > 0:
                        fix_detail['status'] = 'fixed'
                        success_count += 1
                    else:
                        fix_detail['status'] = 'failed'
                        fix_detail['error'] = '没有行被更新'
                        failed_count += 1
                else:
                    fix_detail['status'] = 'preview'
                    success_count += 1
                
                fix_details.append(fix_detail)
                
            except Exception as e:
                fix_detail = {
                    'word_id': word_id,
                    'english_word': english_word,
                    'status': 'error',
                    'error': str(e)
                }
                fix_details.append(fix_detail)
                failed_count += 1
                logger.error(f"修复word_id {word_id}时出错: {e}")
        
        result = {
            'user_id': user_id,
            'action': 'fix',
            'dry_run': dry_run,
            'total_words': len(inconsistent_words),
            'success_count': success_count,
            'failed_count': failed_count,
            'fix_details': fix_details,
            'message': f"{'预览' if dry_run else '修复'}完成: 成功{success_count}个, 失败{failed_count}个"
        }
        
        logger.info(f"用户{user_id}{'预览' if dry_run else '修复'}完成: 成功{success_count}, 失败{failed_count}")
        return result
    
    @staticmethod
    def auto_fix_all_inconsistencies(dry_run: bool = True) -> Dict[str, Any]:
        """
        自动修复所有数据不一致问题
        
        Args:
            dry_run: 是否为预览模式
            
        Returns:
            Dict: 修复结果
        """
        logger.info(f"开始{'预览' if dry_run else '修复'}所有数据不一致问题")
        
        # 先进行全局检查
        global_check = DataConsistencyChecker.check_all_users_consistency()
        
        if global_check['overall_status'] == 'consistent':
            return {
                'action': 'auto_fix_all',
                'dry_run': dry_run,
                'message': '没有发现数据不一致问题',
                'total_users': global_check['total_users_checked'],
                'users_fixed': 0,
                'total_words_fixed': 0
            }
        
        # 修复每个有问题的用户
        fix_results = []
        total_words_fixed = 0
        
        for user_result in global_check['user_results']:
            if user_result['status'] == 'inconsistent':
                user_id = user_result['user_id']
                fix_result = DataConsistencyChecker.fix_user_inconsistency(user_id, dry_run)
                fix_results.append(fix_result)
                total_words_fixed += fix_result['success_count']
        
        result = {
            'action': 'auto_fix_all',
            'dry_run': dry_run,
            'check_time': global_check['check_time'],
            'total_users': global_check['total_users_checked'],
            'users_with_issues': global_check['users_with_issues'],
            'users_fixed': len(fix_results),
            'total_words_fixed': total_words_fixed,
            'fix_results': fix_results,
            'message': f"{'预览' if dry_run else '修复'}完成: {len(fix_results)}个用户, {total_words_fixed}个单词"
        }
        
        logger.info(f"全局{'预览' if dry_run else '修复'}完成: {len(fix_results)}个用户, {total_words_fixed}个单词")
        return result
    
    @staticmethod
    def generate_consistency_report(output_file: Optional[str] = None) -> str:
        """
        生成数据一致性报告
        
        Args:
            output_file: 输出文件路径（可选）
            
        Returns:
            str: 报告内容
        """
        logger.info("生成数据一致性报告")
        
        check_result = DataConsistencyChecker.check_all_users_consistency()
        
        report_lines = [
            "# 数据一致性检查报告",
            f"**检查时间**: {check_result['check_time']}",
            f"**检查用户数**: {check_result['total_users_checked']}",
            f"**总体状态**: {'✅ 一致' if check_result['overall_status'] == 'consistent' else '❌ 不一致'}",
            f"**不一致单词总数**: {check_result['total_inconsistent_words']}",
            f"**有问题的用户数**: {check_result['users_with_issues']}",
            "",
            "## 用户详情",
            ""
        ]
        
        for user_result in check_result['user_results']:
            user_id = user_result['user_id']
            status_icon = '✅' if user_result['status'] == 'consistent' else '❌'
            
            report_lines.extend([
                f"### 用户 {user_id} {status_icon}",
                f"- **状态**: {user_result['status']}",
                f"- **不一致单词数**: {user_result['inconsistent_count']}",
                ""
            ])
            
            if user_result['inconsistent_count'] > 0:
                report_lines.append("**不一致单词列表**:")
                for word in user_result['inconsistent_words'][:10]:  # 只显示前10个
                    report_lines.append(
                        f"- word_id={word['word_id']} ({word['english_word']}): "
                        f"实际学习{word['actual_learning_count']}次, "
                        f"存储{word['stored_learning_count'] or 0}次"
                    )
                if user_result['inconsistent_count'] > 10:
                    report_lines.append(f"- ... 还有{user_result['inconsistent_count'] - 10}个")
                report_lines.append("")
        
        report_content = "\n".join(report_lines)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            logger.info(f"报告已保存到: {output_file}")
        
        return report_content
