"""
数据一致性监控服务
定期检查数据一致性，发现问题时自动告警和修复
"""

import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from .data_consistency_checker import DataConsistencyChecker
from ...core.database import db

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class MonitoringConfig:
    """监控配置"""
    check_interval_minutes: int = 60  # 检查间隔（分钟）
    auto_fix_enabled: bool = True  # 是否启用自动修复
    alert_threshold: int = 5  # 告警阈值（不一致单词数）
    max_auto_fix_words: int = 100  # 单次自动修复的最大单词数
    enable_email_alerts: bool = False  # 是否启用邮件告警
    enable_log_alerts: bool = True  # 是否启用日志告警


class ConsistencyMonitor:
    """数据一致性监控器"""
    
    def __init__(self, config: MonitoringConfig = None):
        self.config = config or MonitoringConfig()
        self.is_running = False
        self.monitor_thread = None
        self.last_check_time = None
        self.alert_history = []
        
    def start_monitoring(self):
        """启动监控"""
        if self.is_running:
            logger.warning("监控已在运行中")
            return
        
        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info(f"数据一致性监控已启动，检查间隔: {self.config.check_interval_minutes}分钟")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        logger.info("数据一致性监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                self._perform_consistency_check()
                
                # 等待下次检查
                time.sleep(self.config.check_interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"监控循环中发生错误: {e}")
                # 发生错误时等待较短时间后重试
                time.sleep(60)
    
    def _perform_consistency_check(self):
        """执行一致性检查"""
        try:
            logger.info("开始执行数据一致性检查")
            self.last_check_time = datetime.now()
            
            # 执行全局检查
            check_result = DataConsistencyChecker.check_all_users_consistency()
            
            total_inconsistent = check_result['total_inconsistent_words']
            users_with_issues = check_result['users_with_issues']
            
            logger.info(f"一致性检查完成: {total_inconsistent}个不一致单词, {users_with_issues}个用户有问题")
            
            # 判断是否需要告警
            if total_inconsistent >= self.config.alert_threshold:
                self._trigger_alert(check_result)
                
                # 判断是否需要自动修复
                if (self.config.auto_fix_enabled and 
                    total_inconsistent <= self.config.max_auto_fix_words):
                    self._perform_auto_fix(check_result)
            
            # 记录检查历史
            self._record_check_history(check_result)
            
        except Exception as e:
            logger.error(f"执行一致性检查时发生错误: {e}")
    
    def _trigger_alert(self, check_result: Dict[str, Any]):
        """触发告警"""
        alert_info = {
            'timestamp': datetime.now().isoformat(),
            'total_inconsistent_words': check_result['total_inconsistent_words'],
            'users_with_issues': check_result['users_with_issues'],
            'check_result': check_result
        }
        
        # 日志告警
        if self.config.enable_log_alerts:
            logger.warning(
                f"🚨 数据一致性告警: 发现{check_result['total_inconsistent_words']}个不一致单词, "
                f"{check_result['users_with_issues']}个用户受影响"
            )
            
            # 详细记录有问题的用户
            for user_result in check_result['user_results']:
                if user_result['status'] == 'inconsistent':
                    logger.warning(
                        f"  用户{user_result['user_id']}: {user_result['inconsistent_count']}个不一致单词"
                    )
        
        # 邮件告警（如果启用）
        if self.config.enable_email_alerts:
            self._send_email_alert(alert_info)
        
        # 记录告警历史
        self.alert_history.append(alert_info)
        
        # 保持告警历史不超过100条
        if len(self.alert_history) > 100:
            self.alert_history = self.alert_history[-100:]
    
    def _perform_auto_fix(self, check_result: Dict[str, Any]):
        """执行自动修复"""
        try:
            logger.info("开始执行自动修复")
            
            fix_result = DataConsistencyChecker.auto_fix_all_inconsistencies(dry_run=False)
            
            if fix_result['total_words_fixed'] > 0:
                logger.info(
                    f"✅ 自动修复完成: 修复了{fix_result['users_fixed']}个用户的"
                    f"{fix_result['total_words_fixed']}个单词"
                )
            else:
                logger.warning("自动修复未修复任何单词")
                
        except Exception as e:
            logger.error(f"自动修复失败: {e}")
    
    def _send_email_alert(self, alert_info: Dict[str, Any]):
        """发送邮件告警"""
        # 这里可以集成邮件发送服务
        # 例如：SMTP、SendGrid、阿里云邮件推送等
        logger.info("邮件告警功能待实现")
    
    def _record_check_history(self, check_result: Dict[str, Any]):
        """记录检查历史"""
        # 这里可以将检查结果保存到数据库或文件
        # 用于后续的趋势分析和报告生成
        pass
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            'is_running': self.is_running,
            'last_check_time': self.last_check_time.isoformat() if self.last_check_time else None,
            'config': {
                'check_interval_minutes': self.config.check_interval_minutes,
                'auto_fix_enabled': self.config.auto_fix_enabled,
                'alert_threshold': self.config.alert_threshold,
                'max_auto_fix_words': self.config.max_auto_fix_words
            },
            'alert_count_last_24h': len([
                alert for alert in self.alert_history
                if datetime.fromisoformat(alert['timestamp']) > datetime.now() - timedelta(hours=24)
            ])
        }
    
    def get_recent_alerts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的告警"""
        return self.alert_history[-limit:] if self.alert_history else []
    
    def force_check(self) -> Dict[str, Any]:
        """强制执行一次检查"""
        logger.info("执行强制一致性检查")
        
        check_result = DataConsistencyChecker.check_all_users_consistency()
        
        # 如果发现问题，触发告警但不自动修复
        if check_result['total_inconsistent_words'] > 0:
            self._trigger_alert(check_result)
        
        return {
            'check_time': datetime.now().isoformat(),
            'result': check_result,
            'message': f"检查完成: 发现{check_result['total_inconsistent_words']}个不一致单词"
        }


# 全局监控实例
_global_monitor = None


def get_global_monitor() -> ConsistencyMonitor:
    """获取全局监控实例"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = ConsistencyMonitor()
    return _global_monitor


def start_global_monitoring(config: MonitoringConfig = None):
    """启动全局监控"""
    monitor = get_global_monitor()
    if config:
        monitor.config = config
    monitor.start_monitoring()


def stop_global_monitoring():
    """停止全局监控"""
    monitor = get_global_monitor()
    monitor.stop_monitoring()


def get_monitoring_status() -> Dict[str, Any]:
    """获取全局监控状态"""
    monitor = get_global_monitor()
    return monitor.get_monitoring_status()


def force_consistency_check() -> Dict[str, Any]:
    """强制执行一致性检查"""
    monitor = get_global_monitor()
    return monitor.force_check()
