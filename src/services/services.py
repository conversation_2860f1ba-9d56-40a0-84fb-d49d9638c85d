"""
核心业务逻辑服务
基于data_model.md的业务流程实现
"""
from datetime import date, datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import math
import bcrypt
from werkzeug.security import check_password_hash
from ..models import User, Word, UserWord, WordRecord, LearningPlan
from ..config import Config

class AuthService:
    """认证服务"""
    
    @staticmethod
    def register(username: str, password: str) -> <PERSON>ple[bool, str, Optional[int]]:
        """用户注册"""
        try:
            # 检查用户名是否已存在
            existing_user = User.get_by_username(username)
            if existing_user:
                return False, "用户名已存在", None
            
            # 创建用户
            user_id = User.create(username, password)
            return True, "注册成功", user_id
            
        except Exception as e:
            return False, f"注册失败: {str(e)}", None
    
    @staticmethod
    def verify_password(stored_password: str, provided_password: str) -> bool:
        """验证密码"""
        try:
            # 检查bcrypt格式（$2b$开头）
            if stored_password.startswith('$2b$'):
                return bcrypt.checkpw(provided_password.encode('utf-8'), stored_password.encode('utf-8'))

            # 检查scrypt格式（scrypt:开头）
            elif stored_password.startswith('scrypt:'):
                return check_password_hash(stored_password, provided_password)

            # 检查SHA256格式（64位十六进制字符串）
            elif len(stored_password) == 64 and all(c in '0123456789abcdef' for c in stored_password):
                import hashlib
                provided_hash = hashlib.sha256(provided_password.encode()).hexdigest()
                return stored_password == provided_hash

            # 检查明文密码（测试用）
            else:
                return stored_password == provided_password

        except Exception as e:
            print(f"Password verification error: {e}")
            return False
    
    @staticmethod
    def login(username: str, password: str) -> Tuple[bool, str, Optional[Dict]]:
        """用户登录"""
        try:
            user = User.get_by_username(username)
            if not user:
                return False, "用户不存在", None
            
            # 使用密码验证函数
            if not AuthService.verify_password(user['password'], password):
                return False, "密码错误", None
            
            user_info = {
                'id': user['id'],
                'username': user['username'],
                'points': user['points'],
                'vouchers': user['vouchers']
            }
            
            return True, "登录成功", user_info
            
        except Exception as e:
            return False, f"登录失败: {str(e)}", None

class ProficiencyCalculator:
    """熟练度计算服务"""
    
    @staticmethod
    def calculate_proficiency(learning_count: int, correct_count: int, 
                            avg_duration: float, last_learning_date: Optional[str]) -> float:
        """
        计算熟练度评分 (0-100)
        根据data_model.md: 正确率(40%) + 回答用时(30%) + 学习间隔(30%)
        """
        if learning_count == 0:
            return 0.0
        
        # 维度1: 正确率评分 (40%)
        accuracy_score = (correct_count / learning_count) * 100 * 0.4
        
        # 维度2: 回答用时评分 (30%) - 5-15秒最优
        duration_score = 0.0
        if avg_duration > 0:
            if 5 <= avg_duration <= 15:
                duration_score = 100  # 最优时间段
            elif avg_duration < 5:
                duration_score = 80   # 过快可能是猜测
            elif avg_duration <= 30:
                duration_score = max(0, 100 - (avg_duration - 15) * 3)  # 逐渐下降
            else:
                duration_score = 20   # 过慢
        duration_score *= 0.3
        
        # 维度3: 学习间隔评分 (30%) - 越近越高
        interval_score = 0.0
        if last_learning_date:
            try:
                last_date = datetime.strptime(last_learning_date, '%Y-%m-%d').date()
                days_diff = (date.today() - last_date).days
                
                if days_diff == 0:
                    interval_score = 100  # 当天学习
                elif days_diff <= 3:
                    interval_score = 90   # 3天内
                elif days_diff <= 7:
                    interval_score = 70   # 一周内
                elif days_diff <= 30:
                    interval_score = 50   # 一月内
                else:
                    interval_score = 20   # 超过一月
            except:
                interval_score = 50
        interval_score *= 0.3
        
        total_score = accuracy_score + duration_score + interval_score
        return min(100.0, max(0.0, total_score))

class LearningPlanService:
    """学习计划服务"""
    
    @staticmethod
    def generate_daily_plan(user_id: int, target_date: date = None) -> bool:
        """
        生成每日学习计划
        基于data_model.md的计划生成规则：7新词 + 18复习词
        """
        if target_date is None:
            target_date = date.today()
        
        try:
            # 检查是否已有计划
            if LearningPlan.exists_for_date(user_id, target_date):
                return True
            
            # 1. 选择7个新词
            new_words = UserWord.get_new_words(user_id, Config.NEW_WORDS_COUNT)
            
            # 2. 选择18个复习词
            review_words = UserWord.get_review_words(user_id, Config.REVIEW_WORDS_COUNT)
            
            # 3. 处理不足情况
            total_new_needed = Config.NEW_WORDS_COUNT
            total_review_needed = Config.REVIEW_WORDS_COUNT
            
            # 新词不足时用复习词补充
            if len(new_words) < total_new_needed:
                shortage = total_new_needed - len(new_words)
                extra_review = UserWord.get_review_words(user_id, Config.REVIEW_WORDS_COUNT + shortage)
                # 将额外的复习词作为新词位置，但类型保持review
                for i in range(len(review_words), min(len(extra_review), len(review_words) + shortage)):
                    word = extra_review[i]
                    LearningPlan.create(user_id, word['id'], target_date, 'review', 
                                      Config.DEFAULT_REVIEW_STAR_LEVEL)
            
            # 复习词不足时用生词本和新词补充
            if len(review_words) < total_review_needed:
                shortage = total_review_needed - len(review_words)
                # 先尝试使用attention状态的单词
                attention_words = UserWord.get_attention_words(user_id)
                used_attention = 0
                
                for word in attention_words:
                    if used_attention >= shortage:
                        break
                    LearningPlan.create(user_id, word['word_id'], target_date, 'new', 
                                      Config.DEFAULT_NEW_STAR_LEVEL)
                    used_attention += 1
                
                # 如果还不够，用新词补充
                remaining_shortage = shortage - used_attention
                if remaining_shortage > 0:
                    extra_new_words = UserWord.get_new_words(user_id, 
                                                           Config.NEW_WORDS_COUNT + remaining_shortage)
                    for i in range(len(new_words), min(len(extra_new_words), 
                                                     len(new_words) + remaining_shortage)):
                        word = extra_new_words[i]
                        LearningPlan.create(user_id, word['id'], target_date, 'new', 
                                          Config.DEFAULT_NEW_STAR_LEVEL)
            
            # 4. 创建新词计划
            for word in new_words[:total_new_needed]:
                LearningPlan.create(user_id, word['id'], target_date, 'new', 
                                  Config.DEFAULT_NEW_STAR_LEVEL)
            
            # 5. 创建复习词计划
            for word in review_words[:total_review_needed]:
                LearningPlan.create(user_id, word['id'], target_date, 'review', 
                                  Config.DEFAULT_REVIEW_STAR_LEVEL)
            
            return True
            
        except Exception as e:
            print(f"Error generating daily plan: {e}")
            return False
    
    @staticmethod
    def get_daily_plan(user_id: int, plan_date: date = None) -> List[Dict]:
        """获取每日学习计划"""
        if plan_date is None:
            plan_date = date.today()
        
        # 确保有学习计划
        LearningPlanService.generate_daily_plan(user_id, plan_date)
        
        plan_items = LearningPlan.get_daily_plan(user_id, plan_date)
        
        result = []
        for item in plan_items:
            result.append({
                'word_id': item['word_id'],
                'english_word': item['english_word'],
                'chinese_meaning': item['chinese_meaning'],
                'section': item['section'],
                'item_type': item['item_type'],
                'star_level': item['star_level'],
                'planned_date': item['planned_date']
            })
        
        return result

class LearningService:
    """学习服务"""
    
    @staticmethod
    def submit_answer(user_id: int, word_id: int, user_input: str, 
                     duration_seconds: float, plan_date: date = None) -> Dict[str, Any]:
        """
        提交答案并更新相关数据
        基于data_model.md的学习过程数据更新流程
        """
        if plan_date is None:
            plan_date = date.today()
        
        try:
            # 1. 获取正确答案
            word = Word.get_by_id(word_id)
            if not word:
                return {'success': False, 'message': '单词不存在'}
            
            correct_answer = word['english_word']
            is_correct = user_input.strip().lower() == correct_answer.lower()
            
            # 2. 记录到word_record表
            WordRecord.create(user_id, word_id, duration_seconds, 
                            user_input, correct_answer, is_correct)
            
            # 3. 更新learning_plan.star_level
            current_plan = LearningPlan.get_daily_plan(user_id, plan_date)
            current_star = 3  # 默认值
            
            for plan_item in current_plan:
                if plan_item['word_id'] == word_id:
                    current_star = plan_item['star_level']
                    break
            
            # 星级变化规则：答对+1星，答错-2星
            if is_correct:
                new_star = min(Config.MAX_STAR_LEVEL, current_star + 1)
                points_change = Config.CORRECT_ANSWER_POINTS
            else:
                new_star = max(Config.MIN_STAR_LEVEL, current_star - 2)
                points_change = -Config.WRONG_ANSWER_PENALTY
            
            LearningPlan.update_star_level(user_id, word_id, plan_date, new_star)
            
            # 4. 更新用户积分
            User.update_points(user_id, points_change)
            
            return {
                'success': True,
                'is_correct': is_correct,
                'correct_answer': correct_answer,
                'new_star_level': new_star,
                'points_change': points_change
            }
            
        except Exception as e:
            return {'success': False, 'message': f'提交答案失败: {str(e)}'}
    
    @staticmethod
    def finish_learning_session(user_id: int, session_date: date = None):
        """
        结束学习会话，更新统计数据
        基于data_model.md: 退出学习时统计word_record数据，更新user_word
        """
        if session_date is None:
            session_date = date.today()
        
        try:
            # 1. 获取当日学习记录
            today_records = WordRecord.get_today_records(user_id)
            
            # 2. 按单词分组统计
            word_stats = {}
            for record in today_records:
                word_id = record['word_id']
                if word_id not in word_stats:
                    word_stats[word_id] = []
                word_stats[word_id].append(record)
            
            # 3. 更新每个单词的user_word统计
            for word_id, records in word_stats.items():
                # 获取该单词的总体统计
                total_stats = WordRecord.get_user_word_stats(user_id, word_id)
                
                # 计算熟练度
                proficiency = ProficiencyCalculator.calculate_proficiency(
                    total_stats['learning_count'],
                    total_stats['correct_count'],
                    total_stats['avg_duration'],
                    total_stats['last_learning_date']
                )
                
                # 更新user_word表
                UserWord.update_learning_stats(
                    user_id, word_id,
                    total_stats['learning_count'],
                    total_stats['correct_count'],
                    proficiency
                )
                
                # 根据熟练度更新状态
                if proficiency >= 80:
                    new_status = 'review'
                elif proficiency >= 50:
                    new_status = 'review'
                else:
                    # 保持原状态或设为attention如果表现很差
                    current_relation = UserWord.get_by_user_and_word(user_id, word_id)
                    if current_relation and proficiency < 30:
                        new_status = 'attention'
                    else:
                        new_status = 'review'
                
                UserWord.update_status(user_id, word_id, new_status)
            
            # 4. 检查当日计划是否全部完成
            if LearningPlan.check_daily_completion(user_id, session_date):
                # 生成次日学习计划
                tomorrow = session_date + timedelta(days=1)
                LearningPlanService.generate_daily_plan(user_id, tomorrow)
                return {'daily_completed': True, 'next_day_plan_generated': True}
            
            return {'daily_completed': False, 'next_day_plan_generated': False}
            
        except Exception as e:
            print(f"Error finishing learning session: {e}")
            return {'daily_completed': False, 'next_day_plan_generated': False}
    
    @staticmethod
    def add_to_vocabulary_book(user_id: int, word_id: int) -> bool:
        """添加单词到生词本"""
        try:
            # 获取或创建用户单词关系
            user_word = UserWord.get_or_create(user_id, word_id)
            
            # 更新状态为attention
            UserWord.update_status(user_id, word_id, 'attention')
            
            return True
        except Exception as e:
            print(f"Error adding to vocabulary book: {e}")
            return False
    
    @staticmethod
    def remove_from_vocabulary_book(user_id: int, word_id: int) -> bool:
        """从生词本移除单词"""
        try:
            # 获取用户单词关系
            user_word = UserWord.get_by_user_and_word(user_id, word_id)
            if user_word and user_word['status'] == 'attention':
                # 根据熟练度决定新状态
                if user_word['proficiency'] > 0:
                    UserWord.update_status(user_id, word_id, 'review')
                else:
                    UserWord.update_status(user_id, word_id, 'new')
            
            return True
        except Exception as e:
            print(f"Error removing from vocabulary book: {e}")
            return False

class StatisticsService:
    """统计服务"""
    
    @staticmethod
    def get_user_statistics(user_id: int) -> Dict[str, Any]:
        """获取用户学习统计数据"""
        try:
            # 获取今日学习进度
            today_plan = LearningPlan.get_daily_plan(user_id, date.today())
            completed_today = sum(1 for item in today_plan if item['star_level'] == 5)
            
            # 获取今日学习记录
            today_records = WordRecord.get_today_records(user_id)
            today_correct = sum(1 for record in today_records if record['is_correct'])
            
            # 获取用户信息
            user = User.get_by_id(user_id)
            
            # 获取已学习单词数（有学习记录的单词）
            words_actually_learned = WordRecord.get_learned_words_count(user_id)
            
            # 获取数据库总单词数
            total_words_in_db = Word.get_total_count()
            
            # 获取熟练度分布
            proficiency_distribution = StatisticsService.get_proficiency_distribution(user_id)
            
            # 统计新词和复习词数量
            new_words_count = sum(1 for item in today_plan if item['item_type'] == 'new')
            review_words_count = sum(1 for item in today_plan if item['item_type'] == 'review')
            
            # 计算注册天数
            days_since_registration = 0
            if user and user['registration_date']:
                try:
                    reg_date = datetime.strptime(user['registration_date'], '%Y-%m-%d').date()
                    days_since_registration = (date.today() - reg_date).days
                except:
                    days_since_registration = 0
            
            return {
                'total_words_today': len(today_plan),
                'completed_today': completed_today,
                'total_attempts_today': len(today_records),
                'correct_today': today_correct,
                'accuracy_today': (today_correct / len(today_records) * 100) if today_records else 0,
                'points': user['points'] if user else 0,
                'vouchers': user['vouchers'] if user else 0,
                'daily_completion_rate': (completed_today / len(today_plan) * 100) if today_plan else 0,
                'words_actually_learned': words_actually_learned,
                'total_words_in_db': total_words_in_db,
                'proficiency_distribution': proficiency_distribution,
                'new_words_count': new_words_count,
                'review_words_count': review_words_count,
                'current_date': date.today().strftime('%Y-%m-%d'),
                'days_since_registration': days_since_registration
            }
        except Exception as e:
            print(f"Error getting user statistics: {e}")
            return {
                'words_actually_learned': 0,
                'total_words_in_db': 0,
                'proficiency_distribution': StatisticsService._default_proficiency_distribution(),
                'new_words_count': 0,
                'review_words_count': 0,
                'current_date': date.today().strftime('%Y-%m-%d'),
                'days_since_registration': 0
            }
    
    @staticmethod
    def get_vocabulary_book(user_id: int) -> List[Dict]:
        """获取生词本"""
        try:
            attention_words = UserWord.get_attention_words(user_id)
            
            result = []
            for word in attention_words:
                result.append({
                    'word_id': word['word_id'],
                    'english_word': word['english_word'],
                    'chinese_meaning': word['chinese_meaning'],
                    'section': word['section'],
                    'proficiency': word['proficiency'],
                    'learning_count': word['learning_count'],
                    'correct_count': word['correct_count'],
                    'last_learning_date': word['last_learning_date']
                })
            
            return result
        except Exception as e:
            print(f"Error getting vocabulary book: {e}")
            return []
    

    
    @staticmethod
    def get_proficiency_distribution(user_id: int) -> Dict[str, Dict]:
        """获取熟练度分布"""
        try:
            # 获取用户所有学习过的单词的熟练度
            user_words = UserWord.get_user_words_with_proficiency(user_id)
            
            # 按熟练度分组统计
            distribution = {
                'expert': {'count': 0, 'range': '95-100'},
                'proficient': {'count': 0, 'range': '80-94'},
                'intermediate': {'count': 0, 'range': '65-79'},
                'basic': {'count': 0, 'range': '50-64'},
                'beginner': {'count': 0, 'range': '25-49'},
                'unfamiliar': {'count': 0, 'range': '0-24'}
            }
            
            for word in user_words:
                proficiency = word['proficiency'] if word['proficiency'] is not None else 0
                if proficiency >= 95:
                    distribution['expert']['count'] += 1
                elif proficiency >= 80:
                    distribution['proficient']['count'] += 1
                elif proficiency >= 65:
                    distribution['intermediate']['count'] += 1
                elif proficiency >= 50:
                    distribution['basic']['count'] += 1
                elif proficiency >= 25:
                    distribution['beginner']['count'] += 1
                else:
                    distribution['unfamiliar']['count'] += 1
            
            return distribution
        except Exception as e:
            print(f"Error getting proficiency distribution: {e}")
            return StatisticsService._default_proficiency_distribution()
    
    @staticmethod
    def _default_proficiency_distribution():
        """默认熟练度分布"""
        return {
            'expert': {'count': 0, 'range': '95-100'},
            'proficient': {'count': 0, 'range': '80-94'},
            'intermediate': {'count': 0, 'range': '65-79'},
            'basic': {'count': 0, 'range': '50-64'},
            'beginner': {'count': 0, 'range': '25-49'},
            'unfamiliar': {'count': 0, 'range': '0-24'}
        }