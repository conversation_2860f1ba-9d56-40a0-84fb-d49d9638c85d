"""
用户认证服务
提供用户注册、登录、密码验证等核心认证功能
"""
from typing import Tuple, Optional, Dict
import bcrypt
from werkzeug.security import check_password_hash

from ...models import User


class AuthService:
    """用户认证服务类"""
    
    @staticmethod
    def register(username: str, password: str) -> Tuple[bool, str, Optional[int]]:
        """
        用户注册

        Args:
            username: 用户名
            password: 密码

        Returns:
            Tuple[bool, str, Optional[int]]: (成功标志, 消息, 用户ID)
        """
        try:
            # 检查用户名是否已存在
            existing_user = User.get_by_username(username)
            if existing_user:
                return False, "用户名已存在", None

            # 创建用户
            user_id = User.create(username, password)

            # 🔧 修复：为新用户初始化所有单词的user_word关系
            AuthService._initialize_user_words(user_id)

            return True, "注册成功", user_id

        except Exception as e:
            return False, f"注册失败: {str(e)}", None

    @staticmethod
    def _initialize_user_words(user_id: int) -> None:
        """
        为新用户初始化所有单词的user_word关系

        Args:
            user_id: 用户ID
        """
        from ...core.database import db

        try:
            # 获取所有单词
            words = db.execute_query("SELECT id FROM word ORDER BY id")

            if not words:
                print(f"⚠️ 警告：单词表为空，无法为用户 {user_id} 初始化单词关系")
                return

            # 批量插入user_word关系
            insert_query = """
            INSERT INTO user_word (user_id, word_id, learning_count, correct_count, status, proficiency)
            VALUES (?, ?, 0, 0, 'new', 0.0)
            """

            # 使用批量插入提高性能
            word_data = [(user_id, word['id']) for word in words]

            for user_id_val, word_id in word_data:
                db.execute_insert(insert_query, (user_id_val, word_id))

            print(f"✅ 为用户 {user_id} 初始化了 {len(words)} 个单词关系")

        except Exception as e:
            print(f"❌ 初始化用户单词关系失败: user_id={user_id}, error={e}")
            # 不抛出异常，避免影响用户注册流程
    
    @staticmethod
    def login(username: str, password: str) -> Tuple[bool, str, Optional[Dict]]:
        """
        用户登录
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Tuple[bool, str, Optional[Dict]]: (成功标志, 消息, 用户信息)
        """
        try:
            user = User.get_by_username(username)
            if not user:
                return False, "用户不存在", None
            
            # 使用密码验证函数
            if not AuthService.verify_password(user['password'], password):
                return False, "密码错误", None
            
            user_info = {
                'id': user['id'],
                'username': user['username'],
                'points': user['points'],
                'vouchers': user['vouchers']
            }
            
            return True, "登录成功", user_info
            
        except Exception as e:
            return False, f"登录失败: {str(e)}", None
    
    @staticmethod
    def verify_password(stored_password: str, provided_password: str) -> bool:
        """
        验证密码
        支持多种密码格式：bcrypt、scrypt、SHA256、明文（测试用）

        Args:
            stored_password: 存储的密码（可能是加密的）
            provided_password: 用户提供的密码

        Returns:
            bool: 密码是否正确
        """
        try:
            # 检查bcrypt格式（$2b$开头）
            if stored_password.startswith('$2b$'):
                return bcrypt.checkpw(
                    provided_password.encode('utf-8'),
                    stored_password.encode('utf-8')
                )

            # 检查scrypt格式（scrypt:开头）
            elif stored_password.startswith('scrypt:'):
                return check_password_hash(stored_password, provided_password)

            # 检查SHA256格式（64位十六进制字符串）
            elif len(stored_password) == 64 and all(c in '0123456789abcdef' for c in stored_password):
                import hashlib
                provided_hash = hashlib.sha256(provided_password.encode()).hexdigest()
                return stored_password == provided_hash

            # 检查明文密码（测试用）
            else:
                return stored_password == provided_password

        except Exception as e:
            print(f"Password verification error: {e}")
            return False
    
    @staticmethod
    def change_password(user_id: int, old_password: str, new_password: str) -> Tuple[bool, str]:
        """
        修改密码
        
        Args:
            user_id: 用户ID
            old_password: 旧密码
            new_password: 新密码
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            # 获取用户信息
            user = User.get_by_id(user_id)
            if not user:
                return False, "用户不存在"
            
            # 验证旧密码
            if not AuthService.verify_password(user['password'], old_password):
                return False, "旧密码错误"
            
            # 更新密码
            User.update_password(user_id, new_password)
            return True, "密码修改成功"
            
        except Exception as e:
            return False, f"密码修改失败: {str(e)}"
    
    @staticmethod
    def reset_password(username: str, new_password: str) -> Tuple[bool, str]:
        """
        重置密码（管理员功能）
        
        Args:
            username: 用户名
            new_password: 新密码
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            user = User.get_by_username(username)
            if not user:
                return False, "用户不存在"
            
            User.update_password(user['id'], new_password)
            return True, "密码重置成功"
            
        except Exception as e:
            return False, f"密码重置失败: {str(e)}"
    
    @staticmethod
    def validate_username(username: str) -> Tuple[bool, str]:
        """
        验证用户名格式
        
        Args:
            username: 用户名
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误消息)
        """
        if not username:
            return False, "用户名不能为空"
        
        if len(username) < 3:
            return False, "用户名长度不能少于3个字符"
        
        if len(username) > 20:
            return False, "用户名长度不能超过20个字符"
        
        # 检查字符是否合法（字母、数字、下划线）
        if not username.replace('_', '').isalnum():
            return False, "用户名只能包含字母、数字和下划线"
        
        return True, ""
    
    @staticmethod
    def validate_password(password: str) -> Tuple[bool, str]:
        """
        验证密码强度
        
        Args:
            password: 密码
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误消息)
        """
        if not password:
            return False, "密码不能为空"
        
        if len(password) < 6:
            return False, "密码长度不能少于6个字符"
        
        if len(password) > 50:
            return False, "密码长度不能超过50个字符"
        
        return True, ""
