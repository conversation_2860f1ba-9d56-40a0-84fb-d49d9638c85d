"""
统计服务
提供用户学习数据统计、分析和报告功能
"""
from datetime import date, datetime, timedelta
from typing import Dict, List, Any

from ...models import User, Word, UserWord, WordRecord, LearningPlan


class StatisticsService:
    """统计服务类"""
    
    @staticmethod
    def get_user_statistics(user_id: int) -> Dict[str, Any]:
        """
        获取用户学习统计数据 - 性能优化版本
        使用单次查询获取多个统计数据，减少数据库访问次数

        Args:
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 用户统计数据
        """
        try:
            # 🚀 性能优化：使用单次复合查询获取多个统计数据
            stats = StatisticsService._get_user_statistics_batch(user_id)

            return {
                'total_words_today': stats['total_words_today'],
                'completed_today': stats['completed_today'],
                'total_attempts_today': stats['total_attempts_today'],
                'correct_today': stats['correct_today'],
                'accuracy_today': stats['accuracy_today'],
                'points': stats['points'],
                'vouchers': stats['vouchers'],
                'daily_completion_rate': stats['daily_completion_rate'],
                'words_actually_learned': stats['words_actually_learned'],
                'total_words_in_db': stats['total_words_in_db'],
                'proficiency_distribution': stats['proficiency_distribution'],
                'new_words_count': stats['new_words_count'],
                'review_words_count': stats['review_words_count'],
                'current_date': date.today().strftime('%Y-%m-%d'),
                'days_since_registration': stats['days_since_registration']
            }
            
            # 统计新词和复习词数量
            new_words_count = sum(1 for item in today_plan if item['item_type'] == 'new')
            review_words_count = sum(1 for item in today_plan if item['item_type'] == 'review')
            
            # 计算注册天数
            days_since_registration = 0
            if user and user['registration_date']:
                try:
                    reg_date = datetime.strptime(user['registration_date'], '%Y-%m-%d').date()
                    days_since_registration = (date.today() - reg_date).days
                except:
                    days_since_registration = 0
            
            return {
                'total_words_today': len(today_plan),
                'completed_today': completed_today,
                'total_attempts_today': len(today_records),
                'correct_today': today_correct,
                'accuracy_today': (today_correct / len(today_records) * 100) if today_records else 0,
                'points': user['points'] if user else 0,
                'vouchers': user['vouchers'] if user else 0,
                'daily_completion_rate': (completed_today / len(today_plan) * 100) if today_plan else 0,
                'words_actually_learned': words_actually_learned,
                'total_words_in_db': total_words_in_db,
                'proficiency_distribution': proficiency_distribution,
                'new_words_count': new_words_count,
                'review_words_count': review_words_count,
                'current_date': date.today().strftime('%Y-%m-%d'),
                'days_since_registration': days_since_registration
            }
        except Exception as e:
            print(f"Error getting user statistics: {e}")
            return {
                'total_words_today': 0,
                'completed_today': 0,
                'total_attempts_today': 0,
                'correct_today': 0,
                'accuracy_today': 0,
                'points': 0,
                'vouchers': 0,
                'daily_completion_rate': 0,
                'words_actually_learned': 0,
                'total_words_in_db': 0,
                'proficiency_distribution': StatisticsService._default_proficiency_distribution(),
                'new_words_count': 0,
                'review_words_count': 0,
                'current_date': date.today().strftime('%Y-%m-%d'),
                'days_since_registration': 0
            }

    @staticmethod
    def _get_user_statistics_batch(user_id: int) -> Dict[str, Any]:
        """
        批量获取用户统计数据 - 性能优化
        使用单次复合查询减少数据库访问次数

        Args:
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 统计数据
        """
        from ...models.base import db

        today = date.today()

        # 🚀 性能优化：单次复合查询获取所有需要的统计数据
        query = """
        WITH user_info AS (
            SELECT points, vouchers, registration_date
            FROM user WHERE id = ?
        ),
        today_plan AS (
            SELECT
                COUNT(*) as total_words_today,
                SUM(CASE WHEN star_level = 5 THEN 1 ELSE 0 END) as completed_today,
                SUM(CASE WHEN item_type = 'new' THEN 1 ELSE 0 END) as new_words_count,
                SUM(CASE WHEN item_type = 'review' THEN 1 ELSE 0 END) as review_words_count
            FROM learning_plan
            WHERE user_id = ? AND planned_date = ?
        ),
        today_records AS (
            SELECT
                COUNT(*) as total_attempts_today,
                SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_today
            FROM word_record
            WHERE user_id = ? AND date = ?
        ),
        learning_stats AS (
            SELECT
                COUNT(DISTINCT word_id) as words_actually_learned
            FROM word_record
            WHERE user_id = ?
        ),
        word_count AS (
            SELECT COUNT(*) as total_words_in_db FROM word
        )
        SELECT
            ui.points, ui.vouchers, ui.registration_date,
            tp.total_words_today, tp.completed_today, tp.new_words_count, tp.review_words_count,
            tr.total_attempts_today, tr.correct_today,
            ls.words_actually_learned,
            wc.total_words_in_db
        FROM user_info ui, today_plan tp, today_records tr, learning_stats ls, word_count wc
        """

        results = db.execute_query(query, (user_id, user_id, today, user_id, today, user_id))

        if not results:
            return StatisticsService._get_default_stats()

        row = results[0]

        # 计算衍生统计数据
        total_attempts = row['total_attempts_today'] or 0
        correct_attempts = row['correct_today'] or 0
        accuracy_today = (correct_attempts / total_attempts * 100) if total_attempts > 0 else 0

        total_words = row['total_words_today'] or 0
        completed = row['completed_today'] or 0
        daily_completion_rate = (completed / total_words * 100) if total_words > 0 else 0

        # 计算注册天数
        reg_date = row['registration_date']
        if reg_date:
            if isinstance(reg_date, str):
                reg_date = datetime.strptime(reg_date, '%Y-%m-%d').date()
            days_since_registration = (today - reg_date).days
        else:
            days_since_registration = 0

        # 获取熟练度分布（使用缓存优化）
        proficiency_distribution = StatisticsService.get_proficiency_distribution(user_id)

        return {
            'total_words_today': total_words,
            'completed_today': completed,
            'total_attempts_today': total_attempts,
            'correct_today': correct_attempts,
            'accuracy_today': accuracy_today,
            'points': row['points'] or 0,
            'vouchers': row['vouchers'] or 0,
            'daily_completion_rate': daily_completion_rate,
            'words_actually_learned': row['words_actually_learned'] or 0,
            'total_words_in_db': row['total_words_in_db'] or 0,
            'proficiency_distribution': proficiency_distribution,
            'new_words_count': row['new_words_count'] or 0,
            'review_words_count': row['review_words_count'] or 0,
            'days_since_registration': days_since_registration
        }

    @staticmethod
    def _get_default_stats() -> Dict[str, Any]:
        """获取默认统计数据"""
        return {
            'total_words_today': 0,
            'completed_today': 0,
            'total_attempts_today': 0,
            'correct_today': 0,
            'accuracy_today': 0,
            'points': 0,
            'vouchers': 0,
            'daily_completion_rate': 0,
            'words_actually_learned': 0,
            'total_words_in_db': 0,
            'proficiency_distribution': StatisticsService._default_proficiency_distribution(),
            'new_words_count': 0,
            'review_words_count': 0,
            'days_since_registration': 0
        }
    
    @staticmethod
    def get_vocabulary_book(user_id: int) -> List[Dict]:
        """
        获取生词本
        
        Args:
            user_id: 用户ID
            
        Returns:
            List[Dict]: 生词本单词列表
        """
        try:
            attention_words = UserWord.get_attention_words(user_id)
            
            result = []
            for word in attention_words:
                result.append({
                    'word_id': word['word_id'],
                    'english_word': word['english_word'],
                    'chinese_meaning': word['chinese_meaning'],
                    'section': word['section'],
                    'proficiency': word['proficiency'],
                    'learning_count': word['learning_count'],
                    'correct_count': word['correct_count'],
                    'last_learning_date': word['last_learning_date']
                })
            
            return result
        except Exception as e:
            print(f"Error getting vocabulary book: {e}")
            return []
    
    @staticmethod
    def get_proficiency_distribution(user_id: int) -> Dict[str, Dict]:
        """
        获取熟练度分布
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Dict]: 熟练度分布统计
        """
        try:
            # 获取用户所有学习过的单词的熟练度
            user_words = UserWord.get_user_words_with_proficiency(user_id)
            
            # 按熟练度分组统计
            distribution = {
                'expert': {'count': 0, 'range': '95-100'},
                'proficient': {'count': 0, 'range': '80-94'},
                'intermediate': {'count': 0, 'range': '65-79'},
                'basic': {'count': 0, 'range': '50-64'},
                'beginner': {'count': 0, 'range': '25-49'},
                'unfamiliar': {'count': 0, 'range': '0-24'}
            }
            
            for word in user_words:
                proficiency = word['proficiency'] if word['proficiency'] is not None else 0
                if proficiency >= 95:
                    distribution['expert']['count'] += 1
                elif proficiency >= 80:
                    distribution['proficient']['count'] += 1
                elif proficiency >= 65:
                    distribution['intermediate']['count'] += 1
                elif proficiency >= 50:
                    distribution['basic']['count'] += 1
                elif proficiency >= 25:
                    distribution['beginner']['count'] += 1
                else:
                    distribution['unfamiliar']['count'] += 1
            
            return distribution
        except Exception as e:
            print(f"Error getting proficiency distribution: {e}")
            return StatisticsService._default_proficiency_distribution()
    


    @staticmethod
    def get_learning_history(user_id: int, days: int = 30) -> Dict:
        """
        获取学习历史数据

        Args:
            user_id: 用户ID
            days: 查询天数

        Returns:
            Dict: 学习历史数据
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days-1)

            # 获取指定时间范围内的学习记录
            records = WordRecord.get_records_by_date_range(user_id, start_date, end_date)

            # 按日期分组统计
            daily_stats = {}
            for record in records:
                record_date = record['created_date']
                if record_date not in daily_stats:
                    daily_stats[record_date] = {
                        'total_attempts': 0,
                        'correct_attempts': 0,
                        'total_duration': 0,
                        'unique_words': set()
                    }

                daily_stats[record_date]['total_attempts'] += 1
                if record['is_correct']:
                    daily_stats[record_date]['correct_attempts'] += 1
                daily_stats[record_date]['total_duration'] += record['duration_seconds']
                daily_stats[record_date]['unique_words'].add(record['word_id'])

            # 转换为列表格式
            history = []
            current_date = start_date
            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')
                if date_str in daily_stats:
                    stats = daily_stats[date_str]
                    accuracy = (stats['correct_attempts'] / stats['total_attempts'] * 100) if stats['total_attempts'] > 0 else 0
                    avg_duration = stats['total_duration'] / stats['total_attempts'] if stats['total_attempts'] > 0 else 0

                    history.append({
                        'date': date_str,
                        'total_attempts': stats['total_attempts'],
                        'correct_attempts': stats['correct_attempts'],
                        'accuracy': accuracy,
                        'avg_duration': avg_duration,
                        'unique_words': len(stats['unique_words'])
                    })
                else:
                    history.append({
                        'date': date_str,
                        'total_attempts': 0,
                        'correct_attempts': 0,
                        'accuracy': 0,
                        'avg_duration': 0,
                        'unique_words': 0
                    })

                current_date += timedelta(days=1)

            return {
                'history': history,
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d'),
                'total_days': days
            }
        except Exception as e:
            print(f"Error getting learning history: {e}")
            return {
                'history': [],
                'start_date': '',
                'end_date': '',
                'total_days': 0
            }

    @staticmethod
    def _default_proficiency_distribution():
        """
        默认熟练度分布

        Returns:
            Dict: 默认分布数据
        """
        return {
            'expert': {'count': 0, 'range': '95-100'},
            'proficient': {'count': 0, 'range': '80-94'},
            'intermediate': {'count': 0, 'range': '65-79'},
            'basic': {'count': 0, 'range': '50-64'},
            'beginner': {'count': 0, 'range': '25-49'},
            'unfamiliar': {'count': 0, 'range': '0-24'}
        }
