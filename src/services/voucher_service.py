"""
购物券服务模块
实现购物券的即时更新机制，与积分系统保持一致
"""
from datetime import date, datetime
from typing import Dict, Any, Optional, List
from ..models.base import db
from ..models.user import User


class VoucherService:
    """购物券服务 - 即时更新机制"""

    # 配置常量
    DAILY_VOUCHER_LIMIT = 2  # 每日最多获得2张购物券

    # 奖励条件
    STREAK_REWARD_THRESHOLD = 10      # 连击奖励：连续答对10题
    SESSION_EXCELLENT_WORDS = 20      # 优秀表现：20个单词
    SESSION_EXCELLENT_ACCURACY = 0.8  # 优秀表现：80%正确率
    SESSION_GOOD_WORDS = 15           # 良好表现：15个单词
    SESSION_GOOD_ACCURACY = 0.8       # 良好表现：80%正确率

    @staticmethod
    def get_daily_vouchers_earned(user_id: int,
                                  target_date: date = None) -> int:
        """
        获取用户指定日期已获得的购物券数量
        使用数据库记录，不依赖Session
        """
        if target_date is None:
            target_date = date.today()
            
        try:
            query = """
            SELECT COALESCE(SUM(vouchers_change), 0) as total_earned
            FROM voucher_log 
            WHERE user_id = ? AND date = ? AND vouchers_change > 0
            """
            results = db.execute_query(query, (user_id, target_date))
            return results[0]['total_earned'] if results else 0
            
        except Exception as e:
            print(f"获取每日购物券数量失败: {e}")
            return 0
    
    @staticmethod
    def get_daily_vouchers_remaining(user_id: int,
                                     target_date: date = None) -> int:
        """获取用户今日剩余可获得购物券数量"""
        earned = VoucherService.get_daily_vouchers_earned(user_id, target_date)
        return max(0, VoucherService.DAILY_VOUCHER_LIMIT - earned)
    
    @staticmethod
    def award_vouchers(user_id: int, vouchers_count: int, reason: str,
                       trigger_type: str, target_date: date = None) -> bool:
        """
        发放购物券 - 即时更新机制
        
        Args:
            user_id: 用户ID
            vouchers_count: 购物券数量
            reason: 发放原因
            trigger_type: 触发类型 (answer_streak, session_complete, ai_help等)
            target_date: 目标日期
            
        Returns:
            bool: 是否发放成功
        """
        if target_date is None:
            target_date = date.today()
            
        try:
            # 检查每日限制
            remaining = VoucherService.get_daily_vouchers_remaining(user_id, target_date)
            if remaining <= 0:
                print(f"用户 {user_id} 今日购物券已达上限")
                return False
            
            # 确保不超过每日限制
            actual_vouchers = min(vouchers_count, remaining)
            
            # 使用事务确保数据一致性
            with db.get_connection() as conn:
                cursor = conn.cursor()
                
                # 1. 更新用户购物券总数
                cursor.execute("""
                    UPDATE user 
                    SET vouchers = vouchers + ?, updated_at = ?
                    WHERE id = ?
                """, (actual_vouchers, datetime.now(), user_id))
                
                # 2. 记录购物券日志
                cursor.execute("""
                    INSERT INTO voucher_log (user_id, vouchers_change, reason, trigger_type, date)
                    VALUES (?, ?, ?, ?, ?)
                """, (user_id, actual_vouchers, reason, trigger_type, target_date))
                
                conn.commit()
            
            # 清理用户缓存
            User._clear_user_cache(user_id)
            
            print(f"✅ 用户 {user_id} 获得 {actual_vouchers} 张购物券: {reason}")
            return True
            
        except Exception as e:
            print(f"发放购物券失败: {e}")
            return False
    
    @staticmethod
    def consume_vouchers(user_id: int, vouchers_count: int, reason: str,
                         trigger_type: str) -> bool:
        """
        消耗购物券
        
        Args:
            user_id: 用户ID
            vouchers_count: 消耗数量
            reason: 消耗原因
            trigger_type: 触发类型
            
        Returns:
            bool: 是否消耗成功
        """
        try:
            # 检查用户购物券是否足够
            user = User.get_by_id(user_id)
            if not user or user['vouchers'] < vouchers_count:
                print(f"用户 {user_id} 购物券不足: 需要{vouchers_count}张，当前{user['vouchers'] if user else 0}张")
                return False
            
            # 使用事务确保数据一致性
            with db.get_connection() as conn:
                cursor = conn.cursor()
                
                # 1. 扣除用户购物券
                cursor.execute("""
                    UPDATE user 
                    SET vouchers = vouchers - ?, updated_at = ?
                    WHERE id = ?
                """, (vouchers_count, datetime.now(), user_id))
                
                # 2. 记录消耗日志
                cursor.execute("""
                    INSERT INTO voucher_log (user_id, vouchers_change, reason, trigger_type, date)
                    VALUES (?, ?, ?, ?, ?)
                """, (user_id, -vouchers_count, reason, trigger_type, date.today()))
                
                conn.commit()
            
            # 清理用户缓存
            User._clear_user_cache(user_id)
            
            print(f"✅ 用户 {user_id} 消耗 {vouchers_count} 张购物券: {reason}")
            return True
            
        except Exception as e:
            print(f"消耗购物券失败: {e}")
            return False
    
    @staticmethod
    def check_streak_reward(user_id: int, consecutive_correct: int) -> Dict[str, Any]:
        """
        检查连击奖励条件
        每次答对时调用
        """
        result = {
            'vouchers_earned': 0,
            'reason': '',
            'triggered': False
        }
        
        # 连击奖励：连续答对10题
        if consecutive_correct >= VoucherService.STREAK_REWARD_THRESHOLD:
            remaining = VoucherService.get_daily_vouchers_remaining(user_id)
            if remaining > 0:
                success = VoucherService.award_vouchers(
                    user_id, 1, 
                    f"连续答对{consecutive_correct}题奖励", 
                    "answer_streak"
                )
                if success:
                    result['vouchers_earned'] = 1
                    result['reason'] = f"连击奖励：连续答对{consecutive_correct}题"
                    result['triggered'] = True
        
        return result
    
    @staticmethod
    def check_session_reward(user_id: int, words_completed: int, 
                           correct_count: int) -> Dict[str, Any]:
        """
        检查会话完成奖励条件
        会话结束时调用
        """
        result = {
            'vouchers_earned': 0,
            'reason': '',
            'triggered': False
        }
        
        if words_completed == 0:
            return result
            
        accuracy_rate = correct_count / words_completed
        remaining = VoucherService.get_daily_vouchers_remaining(user_id)
        
        if remaining <= 0:
            return result
        
        # 优秀表现：20个单词，80%正确率
        if (words_completed >= VoucherService.SESSION_EXCELLENT_WORDS and 
            accuracy_rate >= VoucherService.SESSION_EXCELLENT_ACCURACY):
            vouchers_to_award = min(2, remaining)
            success = VoucherService.award_vouchers(
                user_id, vouchers_to_award,
                f"优秀表现：{words_completed}词{accuracy_rate:.1%}正确率",
                "session_excellent"
            )
            if success:
                result['vouchers_earned'] = vouchers_to_award
                result['reason'] = "优秀表现奖励"
                result['triggered'] = True
                
        # 良好表现：15个单词，80%正确率
        elif (words_completed >= VoucherService.SESSION_GOOD_WORDS and 
              accuracy_rate >= VoucherService.SESSION_GOOD_ACCURACY):
            vouchers_to_award = min(1, remaining)
            success = VoucherService.award_vouchers(
                user_id, vouchers_to_award,
                f"良好表现：{words_completed}词{accuracy_rate:.1%}正确率",
                "session_good"
            )
            if success:
                result['vouchers_earned'] = vouchers_to_award
                result['reason'] = "良好表现奖励"
                result['triggered'] = True
        
        return result
    
    @staticmethod
    def check_session_reward_fixed(user_id: int, words_completed: int, 
                                  total_attempts: int, correct_count: int) -> Dict[str, Any]:
        """
        检查会话完成奖励条件 - 修复版本
        使用正确的正确率计算方式
        
        Args:
            user_id: 用户ID
            words_completed: 完成的单词数（去重）
            total_attempts: 总尝试次数
            correct_count: 正确次数
        """
        result = {
            'vouchers_earned': 0,
            'reason': '',
            'triggered': False
        }
        
        if total_attempts == 0:
            return result
            
        # 🔧 修复：使用正确的正确率计算公式
        accuracy_rate = correct_count / total_attempts
        remaining = VoucherService.get_daily_vouchers_remaining(user_id)
        
        if remaining <= 0:
            return result
        
        # 优秀表现：20个单词，80%正确率
        if (words_completed >= VoucherService.SESSION_EXCELLENT_WORDS and 
            accuracy_rate >= VoucherService.SESSION_EXCELLENT_ACCURACY):
            vouchers_to_award = min(2, remaining)
            success = VoucherService.award_vouchers(
                user_id, vouchers_to_award,
                f"优秀表现：{words_completed}词{accuracy_rate:.1%}正确率",
                "session_excellent"
            )
            if success:
                result['vouchers_earned'] = vouchers_to_award
                result['reason'] = "优秀表现奖励"
                result['triggered'] = True
                
        # 良好表现：15个单词，80%正确率
        elif (words_completed >= VoucherService.SESSION_GOOD_WORDS and 
              accuracy_rate >= VoucherService.SESSION_GOOD_ACCURACY):
            vouchers_to_award = min(1, remaining)
            success = VoucherService.award_vouchers(
                user_id, vouchers_to_award,
                f"良好表现：{words_completed}词{accuracy_rate:.1%}正确率",
                "session_good"
            )
            if success:
                result['vouchers_earned'] = vouchers_to_award
                result['reason'] = "良好表现奖励"
                result['triggered'] = True
        
        return result
    
    @staticmethod
    def get_voucher_history(user_id: int, days: int = 7) -> List[Dict[str, Any]]:
        """
        获取用户购物券历史记录
        
        Args:
            user_id: 用户ID
            days: 查询天数
            
        Returns:
            List[Dict]: 购物券历史记录
        """
        try:
            query = """
            SELECT vouchers_change, reason, trigger_type, date, created_at
            FROM voucher_log 
            WHERE user_id = ? AND date >= date('now', '-{} days')
            ORDER BY created_at DESC
            """.format(days)
            
            results = db.execute_query(query, (user_id,))
            return [dict(row) for row in results] if results else []
            
        except Exception as e:
            print(f"获取购物券历史失败: {e}")
            return []
