"""
学习计划服务
负责生成和管理用户的每日学习计划
严格按照data_model.md第274-297行规范实现
"""
from datetime import date
from typing import List, Dict

from ...models.planning import LearningPlan
from ...config.config import Config


class LearningPlanService:
    """学习计划服务类"""

    @staticmethod
    def generate_daily_plan(user_id: int, target_date: date = None) -> bool:
        """
        生成每日学习计划 - 严格按照data_model.md第274-297行规范实现

        基础分配: 按配置设定的单词数量分配 (Config.NEW_WORDS_COUNT个新词 + Config.REVIEW_WORDS_COUNT个复习词)

        Args:
            user_id: 用户ID
            target_date: 目标日期，默认为今天

        Returns:
            bool: 是否成功生成计划
        """
        if target_date is None:
            target_date = date.today()

        try:
            # 🔒 添加事务锁防止并发重复生成 - 双重检查锁定模式
            from src.core import db
            
            # 第一次检查（无锁）
            if LearningPlan.exists_for_date(user_id, target_date):
                return True
            
            # 第二次检查（无事务锁，避免锁定冲突）
            if LearningPlan.exists_for_date(user_id, target_date):
                return True

            # === 严格按照data_model.md第274-297行实现 ===

            # 1. 选择新词 (第277-279行规范)
            # 从status='new'且未在learning_plan中出现过的单词中选择
            # 排除已在任何学习计划中达到star_level>=2的单词
            new_words = LearningPlanService._select_new_words(user_id, count=Config.NEW_WORDS_COUNT)

            # 2. 新词不足时用复习词补充 (第280-284行规范)
            new_word_positions = []
            if len(new_words) < Config.NEW_WORDS_COUNT:
                # 使用`user_word.proficiency`倒序排列的复习词补充
                supplement_words = LearningPlanService._select_review_words_for_new_positions(
                    user_id, needed=Config.NEW_WORDS_COUNT-len(new_words)
                )
                # 补充的单词item_type保持为'review'
                new_word_positions = new_words + supplement_words
            else:
                new_word_positions = new_words[:Config.NEW_WORDS_COUNT]

            # 3. 选择复习词 (第285-288行规范)
            # 从status IN ('review', 'attention')的单词选择
            # 按proficiency ASC选择熟练度最低的单词
            # 🔧 排除已经在新词位置的单词，避免重复
            new_word_ids = [word['id'] for word in new_word_positions]
            review_words = LearningPlanService._select_review_words(
                user_id, count=Config.REVIEW_WORDS_COUNT, exclude_word_ids=new_word_ids
            )

            # 4. 复习词不足时补充 (第289-295行规范)
            review_word_positions = []
            if len(review_words) < Config.REVIEW_WORDS_COUNT:
                # 优先使用状态为'attention'的单词补充
                # 再使用新单词补充至指定数量
                # 🔧 修复：传递已选单词列表，避免重复选择
                all_selected_word_ids = (new_word_ids + 
                                       [word['id'] for word in review_words])
                supplement_words = LearningPlanService._select_attention_or_new_words(
                    user_id, needed=Config.REVIEW_WORDS_COUNT-len(review_words), 
                    exclude_word_ids=all_selected_word_ids
                )
                # 补充的单词item_type保持为'new'
                review_word_positions = review_words + supplement_words
            else:
                review_word_positions = review_words[:Config.REVIEW_WORDS_COUNT]

            # 5. 创建学习计划记录 (第295-296行规范)
            # 新词位置3星，复习位置4星
            plan_records = LearningPlanService._create_plan_records(
                new_word_positions, review_word_positions, target_date, user_id
            )

            # 验证计划完整性
            if len(plan_records) != Config.DAILY_WORDS_COUNT:
                raise Exception(f"学习计划生成失败：应该{Config.DAILY_WORDS_COUNT}个单词，实际{len(plan_records)}个")

            return True

        except Exception as e:
            print(f"Error generating daily plan: {e}")
            print(f"Error type: {type(e).__name__}")
            import traceback
            traceback.print_exc()
            return False

    @staticmethod
    def get_daily_plan(user_id: int, plan_date: date = None) -> List[Dict]:
        """
        获取每日学习计划

        Args:
            user_id: 用户ID
            plan_date: 计划日期，默认为今天

        Returns:
            List[Dict]: 学习计划列表
        """
        if plan_date is None:
            plan_date = date.today()

        # 确保有学习计划
        LearningPlanService.generate_daily_plan(user_id, plan_date)

        plan_items = LearningPlan.get_daily_plan(user_id, plan_date)

        result = []
        for item in plan_items:
            result.append({
                'word_id': item['word_id'],
                'english_word': item['english_word'],
                'chinese_meaning': item['chinese_meaning'],
                'section': item['section'],
                'item_type': item['item_type'],
                'star_level': item['star_level'],
                'planned_date': item['planned_date']
            })

        return result

    @staticmethod
    def update_plan_progress(user_id: int, word_id: int, new_star_level: int,
                           plan_date: date = None) -> bool:
        """
        更新学习计划进度

        Args:
            user_id: 用户ID
            word_id: 单词ID
            new_star_level: 新的星级
            plan_date: 计划日期

        Returns:
            bool: 是否更新成功
        """
        if plan_date is None:
            plan_date = date.today()

        try:
            LearningPlan.update_star_level(user_id, word_id, plan_date, new_star_level)
            return True
        except Exception as e:
            print(f"Error updating plan progress: {e}")
            return False

    @staticmethod
    def check_daily_completion(user_id: int, check_date: date = None) -> bool:
        """
        检查每日计划是否完成

        Args:
            user_id: 用户ID
            check_date: 检查日期

        Returns:
            bool: 是否完成
        """
        if check_date is None:
            check_date = date.today()

        try:
            return LearningPlan.check_daily_completion(user_id, check_date)
        except Exception as e:
            print(f"Error checking daily completion: {e}")
            return False

    @staticmethod
    def get_plan_statistics(user_id: int, start_date: date = None, end_date: date = None) -> Dict:
        """
        获取学习计划统计信息

        Args:
            user_id: 用户ID
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Dict: 统计信息
        """
        try:
            if start_date is None:
                start_date = date.today()
            if end_date is None:
                end_date = date.today()

            # 获取指定日期范围内的计划
            plans = LearningPlan.get_plans_by_date_range(user_id, start_date, end_date)

            total_words = len(plans)
            completed_words = sum(1 for plan in plans if plan['star_level'] == 5)
            new_words = sum(1 for plan in plans if plan['item_type'] == 'new')
            review_words = sum(1 for plan in plans if plan['item_type'] == 'review')

            return {
                'total_words': total_words,
                'completed_words': completed_words,
                'completion_rate': (completed_words / total_words * 100) if total_words > 0 else 0,
                'new_words': new_words,
                'review_words': review_words,
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d')
            }
        except Exception as e:
            print(f"Error getting plan statistics: {e}")
            return {
                'total_words': 0,
                'completed_words': 0,
                'completion_rate': 0,
                'new_words': 0,
                'review_words': 0,
                'start_date': start_date.strftime('%Y-%m-%d') if start_date else '',
                'end_date': end_date.strftime('%Y-%m-%d') if end_date else ''
            }

    # === 严格按照data_model.md规范的辅助方法 ===

    @staticmethod
    def _select_new_words(user_id: int, count: int) -> List[Dict]:
        """
        选择新词 - 严格按照data_model.md第277-279行规范

        Args:
            user_id: 用户ID
            count: 需要的数量

        Returns:
            List[Dict]: 新词列表
        """
        from ...models.word import UserWord
        from ...core.database import db

        # 🔧 正确实现：所有用户都有user_word关系，使用INNER JOIN
        # 新词定义：status IN ('new', 'attention')的单词

        query = """
        SELECT w.* FROM word w
        INNER JOIN user_word uw ON w.id = uw.word_id
        WHERE uw.user_id = ?
          AND uw.status IN ('new', 'attention')
          AND NOT EXISTS (
            SELECT 1 FROM learning_plan lp
            WHERE lp.user_id = ?
              AND lp.word_id = w.id
              AND lp.star_level >= 2
          )
        ORDER BY w.priority ASC, w.id ASC
        LIMIT ?
        """
        results = db.execute_query(query, (user_id, user_id, count))
        return [dict(row) for row in results]

    @staticmethod
    def _select_review_words_for_new_positions(user_id: int, needed: int) -> List[Dict]:
        """
        为新词位置选择复习词补充 - 按proficiency倒序

        Args:
            user_id: 用户ID
            needed: 需要补充的数量

        Returns:
            List[Dict]: 复习词列表，item_type为'review'
        """
        from ...core.database import db

        query = """
        SELECT w.*, uw.proficiency FROM word w
        JOIN user_word uw ON w.id = uw.word_id
        WHERE uw.user_id = ? AND uw.status = 'review'
          AND (uw.skip_review_until IS NULL OR uw.skip_review_until <= date('now'))
        ORDER BY w.priority ASC, uw.proficiency DESC, w.id
        LIMIT ?
        """
        results = db.execute_query(query, (user_id, needed))
        words = [dict(row) for row in results]

        # 标记这些词在新词位置但类型为review
        for word in words:
            word['position_type'] = 'new'
            word['item_type'] = 'review'

        return words

    @staticmethod
    def _select_review_words(user_id: int, count: int, exclude_word_ids: List[int] = None) -> List[Dict]:
        """
        智能选择复习词 - 使用时间衰减和频率调节的智能算法
        
        替代原有的简单proficiency排序，解决重复学习同样单词的问题
        新算法考虑：基础熟练度 + 时间衰减 + 学习频率 + 加权随机

        Args:
            user_id: 用户ID
            count: 需要的数量
            exclude_word_ids: 要排除的单词ID列表

        Returns:
            List[Dict]: 复习词列表
        """
        from .smart_review_selector import SmartReviewSelector
        
        # 使用智能选择器代替传统的proficiency排序
        return SmartReviewSelector.select_review_words(user_id, count, exclude_word_ids)

    @staticmethod
    def _select_attention_or_new_words(user_id: int, needed: int, exclude_word_ids: List[int] = None) -> List[Dict]:
        """
        为复习位置选择attention或新词补充 - data_model.md第289-295行

        Args:
            user_id: 用户ID
            needed: 需要补充的数量
            exclude_word_ids: 要排除的单词ID列表

        Returns:
            List[Dict]: 补充词列表，item_type为'new'
        """
        from ...core.database import db

        words = []

        # 🔧 严格按照data_model.md第337行：优先使用未在learning_plan中出现过的状态为'attention'的单词补充
        if exclude_word_ids:
            placeholders = ','.join(['?' for _ in exclude_word_ids])
            attention_query = f"""
            SELECT w.*, uw.proficiency FROM word w
            JOIN user_word uw ON w.id = uw.word_id
            WHERE uw.user_id = ?
              AND uw.status = 'attention'
              AND w.id NOT IN ({placeholders})
              AND NOT EXISTS (
                SELECT 1 FROM learning_plan lp
                WHERE lp.user_id = ?
                  AND lp.word_id = w.id
                  AND lp.star_level >= 2
              )
            ORDER BY w.priority ASC, uw.proficiency ASC, uw.last_learning_date DESC
            LIMIT ?
            """
            attention_params = tuple([user_id] + exclude_word_ids + [user_id, needed])
        else:
            attention_query = """
            SELECT w.*, uw.proficiency FROM word w
            JOIN user_word uw ON w.id = uw.word_id
            WHERE uw.user_id = ?
              AND uw.status = 'attention'
              AND NOT EXISTS (
                SELECT 1 FROM learning_plan lp
                WHERE lp.user_id = ?
                  AND lp.word_id = w.id
                  AND lp.star_level >= 2
              )
            ORDER BY w.priority ASC, uw.proficiency ASC, uw.last_learning_date DESC
            LIMIT ?
            """
            attention_params = (user_id, user_id, needed)

        attention_results = db.execute_query(attention_query, attention_params)
        attention_words = [dict(row) for row in attention_results]

        for word in attention_words:
            word['position_type'] = 'review'
            word['item_type'] = 'new'  # 补充的单词item_type保持为'new'

        words.extend(attention_words)
        remaining_needed = needed - len(attention_words)

        # 如果还不够，使用新单词补充 - 严格按照data_model.md规范
        # "再使用新单词补充至18个" - 这里的新单词指user_word表中status='new'的单词
        if remaining_needed > 0:
            # 需要排除已经选择的单词（包括新词位置和attention补充的单词）
            all_exclude_ids = (exclude_word_ids or []) + [word['id'] for word in words]

            if all_exclude_ids:
                placeholders = ','.join(['?' for _ in all_exclude_ids])
                new_query = f"""
                SELECT w.* FROM word w
                INNER JOIN user_word uw ON w.id = uw.word_id
                WHERE uw.user_id = ?
                  AND uw.status = 'new'
                  AND w.id NOT IN ({placeholders})
                  AND NOT EXISTS (
                    SELECT 1 FROM learning_plan lp
                    WHERE lp.user_id = ?
                      AND lp.word_id = w.id
                      AND lp.star_level >= 2
                  )
                ORDER BY w.priority ASC, w.id ASC
                LIMIT ?
                """
                new_params = tuple([user_id] + all_exclude_ids + [user_id, remaining_needed])
            else:
                new_query = """
                SELECT w.* FROM word w
                INNER JOIN user_word uw ON w.id = uw.word_id
                WHERE uw.user_id = ?
                  AND uw.status = 'new'
                  AND NOT EXISTS (
                    SELECT 1 FROM learning_plan lp
                    WHERE lp.user_id = ?
                      AND lp.word_id = w.id
                      AND lp.star_level >= 2
                  )
                ORDER BY w.priority ASC, w.id ASC
                LIMIT ?
                """
                new_params = (user_id, user_id, remaining_needed)

            new_results = db.execute_query(new_query, new_params)
            new_words = [dict(row) for row in new_results]

            for word in new_words:
                word['position_type'] = 'review'
                word['item_type'] = 'new'  # 补充的单词item_type保持为'new'

            words.extend(new_words)

        return words

    @staticmethod
    def _create_plan_records(new_word_positions: List[Dict],
                           review_word_positions: List[Dict],
                           target_date: date, user_id: int) -> List[int]:
        """
        创建学习计划记录 - data_model.md第340行规范
        正确实现：按item_type分配星级
        - item_type='new'或'attention' → 3星
        - item_type='review' → 4星

        Args:
            new_word_positions: 新词位置的单词列表（前7个位置）
            review_word_positions: 复习位置的单词列表（后18个位置）
            target_date: 计划日期
            user_id: 用户ID

        Returns:
            List[int]: 创建的计划ID列表
        """
        from ...models.planning import LearningPlan

        plan_ids = []

        # 🔧 修复：按item_type分配星级，不按位置
        # 合并所有单词进行统一处理
        all_words = new_word_positions + review_word_positions
        
        # 批量创建计划记录，每个单词使用独立事务避免长时间锁定
        for word in all_words:
            try:
                item_type = word.get('item_type', 'new')
                # 按item_type决定星级：new/attention=3星，review=4星
                if item_type in ['new', 'attention']:
                    star_level = 3
                else:  # item_type == 'review'
                    star_level = 4
                
                plan_id = LearningPlan.create(
                    user_id, word['id'], target_date, item_type, star_level
                )
                plan_ids.append(plan_id)
                
            except Exception as e:
                print(f"Failed to create plan for word {word['id']}: {e}")
                # 如果单个单词插入失败，跳过继续处理其他单词
                continue

        return plan_ids
