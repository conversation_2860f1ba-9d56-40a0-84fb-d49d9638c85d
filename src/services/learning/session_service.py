"""
学习会话管理服务
将复杂的学习会话逻辑从前端迁移到后端
提供状态驱动的API，简化前端复杂度
"""
from src.utils.resource_finder import ResourceFinder
from datetime import date, datetime
from typing import Dict, Any, Optional, List
import uuid
import warnings

from ...models.planning import LearningPlan
from ...models.word import UserWord
from ...models.learning import WordRecord
from ...models import User
from ...core.database import db
from ...core.decorators import timing
from .learning_service import LearningService
from .status_service import StatusTransitionService


class LearningSessionService:
    """学习会话管理服务 - 减轻前端负载"""

    # 会话缓存 - 在内存中保存活跃会话状态
    _session_cache = {}

    @staticmethod
    def _check_api_compatibility(api_name: str, deprecated_api: str = None) -> Dict[str, Any]:
        """检查API兼容性并返回兼容性信息"""
        compatibility_info = {
            'api_name': api_name,
            'is_deprecated': deprecated_api is not None,
            'deprecated_api': deprecated_api,
            'recommended_api': api_name if deprecated_api else None
        }

        if deprecated_api:
            warnings.warn(f"API {deprecated_api} 已废弃，请使用 {api_name}",
                         DeprecationWarning, stacklevel=3)

        return compatibility_info

    @staticmethod
    @timing()
    def start_learning_session(user_id: int, session_date: date = None) -> Dict[str, Any]:
        """
        开始学习会话 - 后端处理所有初始化逻辑
        
        Args:
            user_id: 用户ID
            session_date: 会话日期
            
        Returns:
            Dict: 完整的会话初始状态
        """
        if session_date is None:
            session_date = date.today()
        
        try:
            # 生成会话ID
            session_id = str(uuid.uuid4())
            
            # 获取学习计划
            daily_plan_raw = LearningPlan.get_daily_plan(user_id, session_date)
            if not daily_plan_raw:
                return {
                    "success": False,
                    "error": "今日学习计划不存在",
                    "should_generate_plan": True
                }

            # 统一转换为字典格式
            daily_plan = []
            for item in daily_plan_raw:
                if hasattr(item, 'keys') and callable(getattr(item, 'keys')):
                    # 是sqlite3.Row对象，转换为字典
                    daily_plan.append(dict(item))
                elif isinstance(item, dict):
                    # 已经是字典
                    daily_plan.append(item)
                else:
                    # 其他格式，尝试转换
                    try:
                        daily_plan.append(dict(item))
                    except:
                        # 转换失败，跳过该项
                        continue
            
            # 获取用户信息
            user_raw = User.get_by_id(user_id)
            if not user_raw:
                return {
                    "success": False,
                    "error": "用户不存在"
                }

            # 统一转换为字典格式
            if hasattr(user_raw, 'keys') and callable(getattr(user_raw, 'keys')):
                # 是sqlite3.Row对象，转换为字典
                user = dict(user_raw)
            elif isinstance(user_raw, dict):
                # 已经是字典
                user = user_raw
            else:
                # 其他格式，尝试转换
                try:
                    user = dict(user_raw)
                except:
                    return {
                        "success": False,
                        "error": "用户数据格式错误"
                    }
            
            # 🔧 修复：学习会话逻辑
            # 问题：学习计划中的单词star_level可能已经是5，但这不意味着本次会话已完成
            # 解决：会话应该从第一个单词开始，不管star_level是多少
            total_words = len(daily_plan)

            # 暂时设置为从第一个单词开始学习
            completed_words = 0
            current_index = 0  # 从第一个单词开始
            
            # 获取当前单词
            current_word = None
            if current_index < total_words:
                current_word = daily_plan[current_index].copy()

                # 确保word_id存在
                if 'word_id' not in current_word or current_word['word_id'] is None:
                    current_word['word_id'] = current_index + 1  # 临时ID

                # 添加基础学习信息和音频支持
                current_word.update({
                    'difficulty_level': 'medium',
                    'hints_available': True,
                    'ai_help_available': True,
                    'audio_path': ResourceFinder.find_audio_file(current_word.get('english_word', '')) or f'/static/audio/{current_word.get("english_word", "")}.mp3',
                    'has_audio': True  # 假设所有单词都有音频，实际可以检查文件存在
                })
            else:
                # 如果没有更多单词，创建一个占位符
                current_word = {
                    'word_id': None,
                    'english_word': '',
                    'chinese_meaning': '',
                    'message': '所有单词已完成'
                }
            
            # 计算进度和预估时间
            progress = LearningSessionService._calculate_progress(
                completed_words, total_words, current_index
            )
            
            # 构建会话状态
            session_state = {
                "success": True,
                "session_id": session_id,
                "session_date": session_date.strftime('%Y-%m-%d'),
                "user_state": {
                    "user_id": user_id,
                    "points": user['points'],
                    "vouchers": user['vouchers']
                },
                "learning_state": {
                    "total_words": total_words,
                    "completed_words": completed_words,
                    "current_index": current_index,
                    "current_word": current_word,
                    "is_completed": current_index >= total_words
                },
                "progress": progress,
                "ui_hints": {
                    "show_welcome": completed_words == 0,
                    "show_progress_bar": True,
                    "enable_hints": user['points'] >= 10,
                    "enable_ai_help": user['points'] >= 50
                }
            }
            
            # 缓存会话状态 (简单实现，可以用Redis等)
            LearningSessionService._cache_session_state(session_id, session_state)
            
            return session_state
            
        except Exception as e:
            return {
                "success": False,
                "error": f"启动学习会话失败: {str(e)}"
            }
    
    @staticmethod
    @timing()
    def submit_answer_and_advance(session_id: str, user_id: int, 
                                answer_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交答案并推进到下一个状态 - 原子操作
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            answer_data: 答案数据
            
        Returns:
            Dict: 完整的下一个状态
        """
        try:
            # 获取会话状态
            session_state = LearningSessionService._get_session_state(session_id)
            if not session_state:
                return {
                    "success": False,
                    "error": "会话不存在或已过期"
                }
            
            current_word = session_state['learning_state']['current_word']
            if not current_word or not current_word.get('word_id'):
                return {
                    "success": False,
                    "error": "当前没有可学习的单词"
                }
            
            # 提交答案并获取结果
            answer_result = LearningService.submit_answer(
                user_id=user_id,
                word_id=current_word['word_id'],
                user_input=answer_data.get('user_input', ''),
                duration_seconds=answer_data.get('duration_seconds', 0.0)
            )

            # 🔧 跟踪已学习的单词
            if answer_result.get('success'):
                LearningSessionService.track_word_learned(session_id, current_word['word_id'])
            
            if not answer_result.get('success'):
                return {
                    "success": False,
                    "error": "答案提交失败",
                    "details": answer_result
                }
            
            # 更新会话状态
            updated_session = LearningSessionService._advance_session_state(
                session_state, answer_result
            )
            
            # 检查是否完成学习
            if updated_session['learning_state']['is_completed']:
                # 执行学习完成逻辑
                completion_result = LearningService.finish_learning_session(user_id)
                updated_session['completion_result'] = completion_result
            
            # 更新缓存
            LearningSessionService._cache_session_state(session_id, updated_session)
            
            return {
                "success": True,
                "answer_result": answer_result,
                "session_state": updated_session,
                "next_action": "continue" if not updated_session['learning_state']['is_completed'] else "completed"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"提交答案失败: {str(e)}"
            }
    
    @staticmethod
    def _enrich_word_data(word_data: Dict, user_id: int) -> Dict:
        """丰富单词数据，添加学习相关信息"""
        try:
            # 获取用户单词关系
            user_word = UserWord.get_by_user_and_word(user_id, word_data['word_id'])
            
            enriched = word_data.copy()
            enriched.update({
                'proficiency': user_word['proficiency'] if user_word else 0.0,
                'learning_count': user_word['learning_count'] if user_word else 0,
                'last_learning_date': user_word['last_learning_date'] if user_word else None,
                'difficulty_level': LearningSessionService._calculate_difficulty(word_data),
                'hints_available': True,  # 根据用户积分等计算
                'ai_help_available': True  # 根据用户积分等计算
            })
            
            return enriched
            
        except Exception:
            return word_data
    
    @staticmethod
    def _calculate_progress(completed: int, total: int, current_index: int) -> Dict:
        """计算学习进度和预估信息"""
        if total == 0:
            return {
                "percentage": 0.0,
                "completed": 0,
                "total": 0,
                "remaining": 0,
                "estimated_time_minutes": 0
            }
        
        percentage = (completed / total) * 100
        remaining = total - completed
        
        # 预估剩余时间 (假设每个单词平均1分钟)
        estimated_time = remaining * 1.0
        
        return {
            "percentage": round(percentage, 1),
            "completed": completed,
            "total": total,
            "remaining": remaining,
            "estimated_time_minutes": round(estimated_time, 1),
            "current_index": current_index
        }
    
    @staticmethod
    def _calculate_difficulty(word_data: Dict) -> str:
        """计算单词难度级别"""
        star_level = word_data.get('star_level', 3)
        
        if star_level >= 4:
            return 'easy'
        elif star_level >= 2:
            return 'medium'
        else:
            return 'hard'
    
    @staticmethod
    def _advance_session_state(session_state: Dict, answer_result: Dict) -> Dict:
        """推进会话状态到下一个单词"""
        updated_state = session_state.copy()
        
        # 更新用户状态 (积分等)
        if 'new_points_total' in answer_result:
            updated_state['user_state']['points'] = answer_result['new_points_total']
        
        # 推进学习状态
        learning_state = updated_state['learning_state']
        learning_state['current_index'] += 1
        
        # 重新计算完成状态
        if learning_state['current_index'] >= learning_state['total_words']:
            learning_state['is_completed'] = True
            learning_state['current_word'] = None
        else:
            # 获取下一个单词 (简化实现)
            # 实际应该从数据库重新获取
            learning_state['current_word'] = {
                "word_id": learning_state['current_index'] + 1,
                "message": "下一个单词数据需要重新获取"
            }
        
        # 更新进度
        completed_words = learning_state['current_index']
        updated_state['progress'] = LearningSessionService._calculate_progress(
            completed_words, learning_state['total_words'], learning_state['current_index']
        )
        
        return updated_state
    
    @staticmethod
    def _cache_session_state(session_id: str, state: Dict):
        """缓存会话状态 (简单实现)"""
        # 这里可以用Redis等缓存系统
        # 目前简单存储在内存中
        if not hasattr(LearningSessionService, '_session_cache'):
            LearningSessionService._session_cache = {}

        # 🔧 增强会话缓存，添加元数据
        LearningSessionService._session_cache[session_id] = {
            'state': state,
            'created_at': datetime.now(),
            'last_accessed': datetime.now(),
            'words_learned': set(),  # 跟踪已学习的单词
            'auto_save_enabled': True  # 自动保存标志
        }

    @staticmethod
    def _get_session_state(session_id: str) -> Optional[Dict]:
        """获取会话状态"""
        if not hasattr(LearningSessionService, '_session_cache'):
            return None

        cached_session = LearningSessionService._session_cache.get(session_id)
        if not cached_session:
            return None

        # 🔧 检查会话是否过期 (24小时)
        if datetime.now() - cached_session['created_at'] > timedelta(hours=24):
            # 会话过期前尝试自动保存
            LearningSessionService._auto_save_session_on_expire(session_id, cached_session)
            del LearningSessionService._session_cache[session_id]
            return None

        # 更新最后访问时间
        cached_session['last_accessed'] = datetime.now()
        return cached_session['state']

    @staticmethod
    def _auto_save_session_on_expire(session_id: str, cached_session: Dict[str, Any]):
        """会话过期时自动保存数据"""
        try:
            if not cached_session.get('auto_save_enabled', True):
                return

            session_state = cached_session['state']
            user_id = session_state.get('user_state', {}).get('user_id')
            words_learned = cached_session.get('words_learned', set())

            if user_id and words_learned:
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"会话{session_id}过期，自动保存{len(words_learned)}个单词的数据")

                # 这里可以添加额外的数据保存逻辑
                # 例如：保存学习进度、更新统计等

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"会话{session_id}自动保存失败: {e}")

    @staticmethod
    def track_word_learned(session_id: str, word_id: int):
        """跟踪已学习的单词"""
        if not hasattr(LearningSessionService, '_session_cache'):
            return

        cached_session = LearningSessionService._session_cache.get(session_id)
        if cached_session:
            cached_session['words_learned'].add(word_id)

    @staticmethod
    def end_session_gracefully(session_id: str, user_id: int) -> Dict[str, Any]:
        """优雅地结束会话"""
        try:
            cached_session = LearningSessionService._session_cache.get(session_id)
            if not cached_session:
                return {
                    'success': False,
                    'message': '会话不存在或已过期'
                }

            session_state = cached_session['state']
            words_learned = cached_session.get('words_learned', set())

            # 生成会话总结
            session_summary = {
                'session_id': session_id,
                'user_id': user_id,
                'start_time': cached_session['created_at'].isoformat(),
                'end_time': datetime.now().isoformat(),
                'duration_minutes': (datetime.now() - cached_session['created_at']).total_seconds() / 60,
                'words_learned_count': len(words_learned),
                'words_learned_ids': list(words_learned),
                'status': 'completed_gracefully'
            }

            # 清理会话缓存
            del LearningSessionService._session_cache[session_id]

            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"会话{session_id}优雅结束，学习了{len(words_learned)}个单词")

            return {
                'success': True,
                'session_summary': session_summary,
                'message': '会话结束成功'
            }

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"结束会话{session_id}时出错: {e}")
            return {
                'success': False,
                'message': f'结束会话失败: {str(e)}'
            }

    @staticmethod
    def set_learning_mode(session_id: str, learning_mode: str) -> Dict[str, Any]:
        """设置学习模式"""
        try:
            session_state = LearningSessionService._get_session_state(session_id)
            if not session_state:
                return {
                    "success": False,
                    "error": "会话不存在或已过期"
                }

            # 更新学习模式
            session_state['learning_mode'] = learning_mode
            LearningSessionService._cache_session_state(session_id, session_state)

            return {
                "success": True,
                "data": {
                    "learning_mode": learning_mode,
                    "session_id": session_id
                },
                "message": f"学习模式已切换为: {learning_mode}"
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"设置学习模式失败: {str(e)}"
            }

    @staticmethod
    def get_daily_report(user_id: int, report_date: str) -> Dict[str, Any]:
        """获取今日学习报告"""
        try:
            from datetime import datetime
            from ...models.learning import WordRecord

            # 解析日期
            target_date = datetime.strptime(report_date, '%Y-%m-%d').date()

            # 获取当日学习记录
            records = WordRecord.get_records_by_date(user_id, target_date)

            # 统计数据 - 修复sqlite3.Row访问方式
            total_attempts = len(records)
            correct_attempts = sum(1 for r in records if dict(r).get('is_correct'))
            accuracy_rate = (correct_attempts / total_attempts * 100) if total_attempts > 0 else 0

            # 获取学习时长
            total_duration = sum(dict(r).get('duration_seconds', 0) for r in records)

            # 获取学习的单词数量
            unique_words = len(set(dict(r).get('word_id') for r in records))

            return {
                "success": True,
                "data": {
                    "date": report_date,
                    "total_attempts": total_attempts,
                    "correct_attempts": correct_attempts,
                    "accuracy_rate": round(accuracy_rate, 1),
                    "total_duration_seconds": total_duration,
                    "unique_words_learned": unique_words,
                    "average_time_per_word": round(total_duration / max(unique_words, 1), 1)
                }
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"获取学习报告失败: {str(e)}"
            }

    @staticmethod
    def get_learning_statistics(user_id: int, period: str) -> Dict[str, Any]:
        """获取学习统计数据"""
        try:
            from datetime import datetime, timedelta
            from ...models.learning import WordRecord

            # 计算时间范围
            end_date = datetime.now().date()
            if period == 'week':
                start_date = end_date - timedelta(days=7)
            elif period == 'month':
                start_date = end_date - timedelta(days=30)
            else:  # all
                start_date = datetime(2020, 1, 1).date()

            # 获取时间范围内的学习记录
            records = WordRecord.get_records_by_date_range(user_id, start_date, end_date)

            # 统计数据 - 修复sqlite3.Row访问方式
            total_words = len(set(dict(r).get('word_id') for r in records))
            total_sessions = len(set(dict(r).get('date') for r in records))
            total_time = sum(dict(r).get('duration_seconds', 0) for r in records)

            # 按星级分布统计
            star_distribution = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
            # 这里需要从learning_plan表获取星级分布

            return {
                "success": True,
                "data": {
                    "period": period,
                    "total_words_learned": total_words,
                    "total_learning_sessions": total_sessions,
                    "total_learning_time": total_time,
                    "average_session_time": round(total_time / max(total_sessions, 1), 1),
                    "star_distribution": star_distribution
                }
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"获取学习统计失败: {str(e)}"
            }

    @staticmethod
    def get_spelling_feedback(session_id: str, user_input: str, target_word: str, letter_position: int) -> Dict[str, Any]:
        """拼写模式字母级反馈"""
        try:
            # 字母级别的拼写检查
            letter_feedback = []
            target_word_lower = target_word.lower()
            user_input_lower = user_input.lower()

            for i, char in enumerate(user_input_lower):
                if i < len(target_word_lower):
                    is_correct = char == target_word_lower[i]
                    letter_feedback.append({
                        'position': i,
                        'letter': char,
                        'is_correct': is_correct,
                        'expected': target_word_lower[i]
                    })
                else:
                    # 超出目标单词长度
                    letter_feedback.append({
                        'position': i,
                        'letter': char,
                        'is_correct': False,
                        'expected': None
                    })

            # 计算进度百分比
            correct_letters = sum(1 for feedback in letter_feedback if feedback['is_correct'])
            progress_percentage = (correct_letters / len(target_word_lower)) * 100 if target_word_lower else 0

            # 检查是否完成
            is_complete = (len(user_input_lower) == len(target_word_lower) and
                          all(feedback['is_correct'] for feedback in letter_feedback))

            return {
                "success": True,
                "data": {
                    "letter_feedback": letter_feedback,
                    "progress_percentage": round(progress_percentage, 1),
                    "is_complete": is_complete,
                    "target_length": len(target_word_lower),
                    "current_length": len(user_input_lower)
                }
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"获取拼写反馈失败: {str(e)}"
            }

    @staticmethod
    def get_plan_details(user_id: int, plan_date: str) -> Dict[str, Any]:
        """获取学习计划详情"""
        try:
            from datetime import datetime
            from ...models.planning import LearningPlan

            # 解析日期
            target_date = datetime.strptime(plan_date, '%Y-%m-%d').date()

            # 获取学习计划
            plan = LearningPlan.get_daily_plan(user_id, target_date)

            # 按星级统计
            star_counts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
            overdue_count = 0
            total_words = len(plan)

            for word in plan:
                word_dict = dict(word)
                star_level = word_dict.get('star_level', 3)
                if star_level in star_counts:
                    star_counts[star_level] += 1

                # 检查是否过期（简化逻辑）
                if star_level <= 2:
                    overdue_count += 1

            return {
                "success": True,
                "data": {
                    "star_counts": star_counts,
                    "overdue_count": overdue_count,
                    "total_words": total_words,
                    "filters_available": {
                        "new_words": True,
                        "review_words": True,
                        "star_filter": True,
                        "five_star_review": True
                    },
                    "plan_date": plan_date
                }
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"获取学习计划详情失败: {str(e)}"
            }

    @staticmethod
    def get_learning_hint(word: str, hint_level: int) -> Dict[str, Any]:
        """多级提示系统"""
        try:
            max_level = 3
            hint_level = max(1, min(hint_level, max_level))

            # 根据提示级别生成不同的提示内容
            if hint_level == 1:
                # 第一级：长度提示
                hint_content = f"这个单词有 {len(word)} 个字母"
            elif hint_level == 2:
                # 第二级：首字母提示
                hint_content = f"这个单词以字母 '{word[0].upper()}' 开头"
            else:
                # 第三级：部分字母提示
                if len(word) <= 3:
                    hint_content = f"这个单词是: {word[0]}{'*' * (len(word)-2)}{word[-1] if len(word) > 1 else ''}"
                else:
                    hint_content = f"这个单词是: {word[:2]}{'*' * (len(word)-3)}{word[-1]}"

            return {
                "success": True,
                "data": {
                    "hint_content": hint_content,
                    "hint_level": hint_level,
                    "next_level_available": hint_level < max_level,
                    "max_level": max_level
                }
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"获取学习提示失败: {str(e)}"
            }

    @staticmethod
    def get_word_images(word: str) -> Dict[str, Any]:
        """获取单词图片资源"""
        try:
            # 构建多个可能的图片URL
            base_urls = [
                f"/static/images/words/{word.lower()}.jpg",
                f"/static/images/words/{word.lower()}.png",
                f"/static/images/words/{word.lower()}.webp"
            ]

            # 外部图片源（作为备选）
            fallback_urls = [
                f"https://api.unsplash.com/search/photos?query={word}&client_id=demo",
                f"https://pixabay.com/api/?key=demo&q={word}&image_type=photo"
            ]

            # 检查本地图片是否存在（简化版本，实际应该检查文件系统）
            available_urls = []
            for url in base_urls:
                # 这里应该检查文件是否存在，暂时假设都存在
                available_urls.append(url)

            has_images = len(available_urls) > 0

            return {
                "success": True,
                "data": {
                    "word": word,
                    "image_urls": available_urls,
                    "fallback_urls": fallback_urls,
                    "has_images": has_images,
                    "primary_image": available_urls[0] if available_urls else None
                }
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"获取单词图片失败: {str(e)}"
            }

    @staticmethod
    def get_memory_help(word: str, meaning: str, user_level: str) -> Dict[str, Any]:
        """AI记忆辅助功能"""
        try:
            # 基于单词特征生成记忆技巧
            memory_tips = []
            techniques = []
            examples = []

            # 根据单词长度和特征生成提示
            if len(word) <= 4:
                memory_tips.append(f"'{word}' 是一个短单词，可以整体记忆")
                techniques.append("整体记忆法")
            else:
                # 尝试分解单词
                if len(word) >= 6:
                    mid = len(word) // 2
                    part1, part2 = word[:mid], word[mid:]
                    memory_tips.append(f"可以分解为 '{part1}' + '{part2}' 来记忆")
                    techniques.append("分解记忆法")

            # 根据词汇特征生成联想
            if word.startswith('un'):
                memory_tips.append("以'un'开头，通常表示否定意思")
                techniques.append("前缀记忆法")
            elif word.endswith('ing'):
                memory_tips.append("以'ing'结尾，表示进行时态")
                techniques.append("后缀记忆法")
            elif word.endswith('ed'):
                memory_tips.append("以'ed'结尾，表示过去时态")
                techniques.append("后缀记忆法")

            # 生成例句
            examples.append(f"例句: The {word} is very important.")
            examples.append(f"记忆: {word} = {meaning}")

            # 根据用户水平调整内容
            if user_level == 'beginner':
                memory_tips.insert(0, f"初学者提示: '{word}' 的中文意思是 '{meaning}'")
            elif user_level == 'advanced':
                memory_tips.append(f"高级提示: 尝试在不同语境中使用 '{word}'")

            return {
                "success": True,
                "data": {
                    "word": word,
                    "meaning": meaning,
                    "memory_tips": memory_tips,
                    "techniques": techniques,
                    "examples": examples,
                    "user_level": user_level,
                    "tip_count": len(memory_tips)
                }
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"获取记忆辅助失败: {str(e)}"
            }
