"""
奖励系统服务
将积分和购物券逻辑从前端迁移到后端
提供统一的奖励计算和管理API
"""
from datetime import date, datetime
from typing import Dict, Any, Optional
import math

from ...models import User
from ...core.database import db
from ...core.decorators import timing


class RewardsSystemService:
    """奖励系统服务 - 后端处理所有奖励逻辑"""
    
    # 奖励配置常量
    BASE_POINTS_PER_WORD = 10
    CORRECT_BONUS_MULTIPLIER = 1.5
    SPEED_BONUS_THRESHOLD = 5.0  # 秒
    CONSECUTIVE_BONUS_THRESHOLD = 3
    DAILY_VOUCHER_LIMIT = 2
    
    @staticmethod
    @timing()
    def calculate_enhanced_rewards(user_id: int, action: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        增强版奖励计算 - 支持单次答题奖励

        Args:
            user_id: 用户ID
            action: 奖励动作类型 ('correct_answer', 'session_complete', etc.)
            context: 上下文数据

        Returns:
            Dict: 奖励计算结果
        """
        try:
            user = User.get_by_id(user_id)
            if not user:
                return {"success": False, "error": "用户不存在"}

            user_dict = dict(user)
            current_points = user_dict.get('points', 0)
            current_vouchers = user_dict.get('vouchers', 0)

            points_earned = 0
            vouchers_earned = 0
            bonus_details = []

            if action == 'correct_answer':
                # 单次答题奖励
                is_exact_match = context.get('is_exact_match', False)
                duration_seconds = context.get('duration_seconds', 0)
                star_level = context.get('star_level', 3)

                # 基础积分
                base_points = 10 if is_exact_match else 5
                points_earned += base_points
                bonus_details.append(f"基础答题: +{base_points}")

                # 速度加成
                if duration_seconds < 3:
                    speed_bonus = 3
                    points_earned += speed_bonus
                    bonus_details.append(f"快速回答: +{speed_bonus}")
                elif duration_seconds < 5:
                    speed_bonus = 1
                    points_earned += speed_bonus
                    bonus_details.append(f"较快回答: +{speed_bonus}")

                # 难度加成
                if star_level >= 4:
                    difficulty_bonus = 2
                    points_earned += difficulty_bonus
                    bonus_details.append(f"高难度: +{difficulty_bonus}")

            elif action == 'session_complete':
                # 会话完成奖励
                words_completed = context.get('words_completed', 0)
                correct_count = context.get('correct_count', 0)
                accuracy_rate = correct_count / max(words_completed, 1)

                # 完成奖励
                completion_bonus = words_completed * 2
                points_earned += completion_bonus
                bonus_details.append(f"完成奖励: +{completion_bonus}")

                # 准确率奖励
                if accuracy_rate >= 0.9:
                    accuracy_bonus = 20
                    points_earned += accuracy_bonus
                    bonus_details.append(f"高准确率: +{accuracy_bonus}")
                elif accuracy_rate >= 0.7:
                    accuracy_bonus = 10
                    points_earned += accuracy_bonus
                    bonus_details.append(f"良好准确率: +{accuracy_bonus}")

                # 购物券奖励检查
                if accuracy_rate >= 0.8 and words_completed >= 10:
                    today_vouchers = RewardsSystemService._get_today_voucher_count(user_id)
                    if today_vouchers < RewardsSystemService.DAILY_VOUCHER_LIMIT:
                        vouchers_earned = 1
                        bonus_details.append("购物券奖励: +1")

            # 更新用户数据
            new_points = current_points + points_earned
            new_vouchers = current_vouchers + vouchers_earned

            if points_earned > 0 or vouchers_earned > 0:
                User.update_points_and_vouchers(user_id, new_points, new_vouchers)

            return {
                "success": True,
                "rewards": {
                    "points_earned": points_earned,
                    "vouchers_earned": vouchers_earned,
                    "new_total_points": new_points,
                    "new_total_vouchers": new_vouchers,
                    "bonus_details": bonus_details
                }
            }

        except Exception as e:
            return {"success": False, "error": f"奖励计算失败: {str(e)}"}

    @staticmethod
    @timing()
    def calculate_session_rewards(user_id: int, learning_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算学习会话奖励
        
        Args:
            user_id: 用户ID
            learning_result: 学习结果数据
            
        Returns:
            Dict: 奖励计算结果
        """
        try:
            # 获取用户信息
            user = User.get_by_id(user_id)
            if not user:
                return {
                    "success": False,
                    "error": "用户不存在"
                }
            
            # 解析学习结果
            words_completed = learning_result.get('words_completed', 0)
            correct_count = learning_result.get('correct_count', 0)
            total_duration = learning_result.get('session_duration', 0)
            consecutive_correct = learning_result.get('consecutive_correct', 0)
            
            # 计算积分奖励
            points_calculation = RewardsSystemService._calculate_points(
                words_completed, correct_count, total_duration, consecutive_correct
            )
            
            # 计算购物券奖励
            vouchers_calculation = RewardsSystemService._calculate_vouchers(
                user_id, words_completed, correct_count, consecutive_correct
            )
            
            # 应用奖励
            old_points = user['points']
            old_vouchers = user['vouchers']
            
            new_points = old_points + points_calculation['total_points']
            new_vouchers = old_vouchers + vouchers_calculation['vouchers_earned']
            
            # 更新用户数据
            User.update_points(user_id, points_calculation['total_points'])
            if vouchers_calculation['vouchers_earned'] > 0:
                User.update_vouchers(user_id, vouchers_calculation['vouchers_earned'])
            
            return {
                "success": True,
                "rewards": {
                    "points_earned": points_calculation['total_points'],
                    "points_breakdown": points_calculation['breakdown'],
                    "vouchers_earned": vouchers_calculation['vouchers_earned'],
                    "voucher_reason": vouchers_calculation['reason'],
                    "bonus_achievements": points_calculation.get('achievements', [])
                },
                "updated_totals": {
                    "total_points": new_points,
                    "total_vouchers": new_vouchers,
                    "points_change": points_calculation['total_points'],
                    "vouchers_change": vouchers_calculation['vouchers_earned']
                },
                "daily_limits": {
                    "vouchers_remaining": RewardsSystemService._get_daily_vouchers_remaining(user_id),
                    "points_cap_reached": False  # 暂时不实现积分上限
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"奖励计算失败: {str(e)}"
            }
    
    @staticmethod
    def _calculate_points(words_completed: int, correct_count: int, 
                         total_duration: float, consecutive_correct: int) -> Dict[str, Any]:
        """计算积分奖励"""
        breakdown = []
        total_points = 0
        achievements = []
        
        # 基础积分 (每个单词基础分)
        base_points = words_completed * RewardsSystemService.BASE_POINTS_PER_WORD
        total_points += base_points
        breakdown.append({
            "type": "base",
            "description": f"完成{words_completed}个单词",
            "points": base_points
        })
        
        # 正确率奖励
        if words_completed > 0:
            accuracy_rate = correct_count / words_completed
            if accuracy_rate >= 0.9:
                accuracy_bonus = int(base_points * 0.5)
                total_points += accuracy_bonus
                breakdown.append({
                    "type": "accuracy",
                    "description": f"正确率{accuracy_rate:.1%}奖励",
                    "points": accuracy_bonus
                })
                achievements.append("高准确率达人")
        
        # 速度奖励 (平均每词用时少于阈值)
        if words_completed > 0 and total_duration > 0:
            avg_time_per_word = total_duration / words_completed
            if avg_time_per_word <= RewardsSystemService.SPEED_BONUS_THRESHOLD:
                speed_bonus = int(words_completed * 5)
                total_points += speed_bonus
                breakdown.append({
                    "type": "speed",
                    "description": f"快速答题奖励(平均{avg_time_per_word:.1f}秒/词)",
                    "points": speed_bonus
                })
                achievements.append("闪电答题手")
        
        # 连续正确奖励
        if consecutive_correct >= RewardsSystemService.CONSECUTIVE_BONUS_THRESHOLD:
            consecutive_bonus = consecutive_correct * 3
            total_points += consecutive_bonus
            breakdown.append({
                "type": "consecutive",
                "description": f"连续答对{consecutive_correct}题",
                "points": consecutive_bonus
            })
            achievements.append("连击大师")
        
        return {
            "total_points": total_points,
            "breakdown": breakdown,
            "achievements": achievements
        }
    
    @staticmethod
    def _calculate_vouchers(user_id: int, words_completed: int, 
                           correct_count: int, consecutive_correct: int) -> Dict[str, Any]:
        """计算购物券奖励 - 统一使用VoucherService"""
        from ...voucher_service import VoucherService
        
        vouchers_earned = 0
        reason = ""
        
        # 🔧 修复：使用VoucherService统一的每日限制检查
        remaining = VoucherService.get_daily_vouchers_remaining(user_id)
        
        if remaining <= 0:
            return {
                "vouchers_earned": 0,
                "reason": f"今日购物券已达上限({VoucherService.DAILY_VOUCHER_LIMIT}张)",
                "daily_limit_reached": True
            }
        
        # 🔧 使用VoucherService的会话奖励检查
        accuracy_rate = correct_count / max(words_completed, 1)
        session_result = VoucherService.check_session_reward(
            user_id, words_completed, correct_count
        )
        
        if session_result['triggered']:
            return {
                "vouchers_earned": session_result['vouchers_earned'],
                "reason": session_result['reason'],
                "daily_limit_reached": False
            }
        
        # 检查连击奖励
        if consecutive_correct >= 10:
            streak_result = VoucherService.check_streak_reward(
                user_id, consecutive_correct
            )
            if streak_result['triggered']:
                return {
                    "vouchers_earned": streak_result['vouchers_earned'],
                    "reason": streak_result['reason'],
                    "daily_limit_reached": False
                }
        
        return {
            "vouchers_earned": 0,
            "reason": "未达到购物券奖励条件",
            "daily_limit_reached": False
        }
    
    @staticmethod
    def check_purchase_eligibility(user_id: int, item_type: str, cost: int) -> Dict[str, Any]:
        """检查购买资格"""
        try:
            user = User.get_by_id(user_id)
            if not user:
                return {
                    "success": False,
                    "error": "用户不存在"
                }
            
            user_points = user['points']
            can_purchase = user_points >= cost
            
            return {
                "success": True,
                "can_purchase": can_purchase,
                "cost": cost,
                "user_points": user_points,
                "remaining_after": user_points - cost if can_purchase else user_points,
                "shortage": max(0, cost - user_points)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"购买资格检查失败: {str(e)}"
            }
    
    @staticmethod
    def get_user_rewards_summary(user_id: int) -> Dict[str, Any]:
        """获取用户奖励摘要 - 统一使用VoucherService"""
        from ...voucher_service import VoucherService
        
        try:
            user = User.get_by_id(user_id)
            if not user:
                return {
                    "success": False,
                    "error": "用户不存在"
                }
            
            return {
                "success": True,
                "summary": {
                    "total_points": user['points'],
                    "total_vouchers": user['vouchers'],
                    "daily_vouchers_earned": VoucherService.get_daily_vouchers_earned(user_id),
                    "daily_vouchers_remaining": VoucherService.get_daily_vouchers_remaining(user_id),
                    "daily_limit": VoucherService.DAILY_VOUCHER_LIMIT
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"获取奖励摘要失败: {str(e)}"
            }
