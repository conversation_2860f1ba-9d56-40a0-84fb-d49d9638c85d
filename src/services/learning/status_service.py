"""
状态转换服务
严格按照data_model.md第337-344行规范实现单词状态转换逻辑
"""
from datetime import date
from typing import Dict, Any

from ...core.database import db
from ...core.decorators import timing
from ...models.word import UserWord


class StatusTransitionService:
    """单词状态转换服务 - 严格按照data_model.md规范"""

    @staticmethod
    @timing()
    def update_word_status_by_proficiency(user_id: int, word_id: int) -> bool:
        """
        基于熟练度更新单词状态 - 主要规则
        严格按照data_model.md第337-344行规范

        主要规则:
        - proficiency >= 80: status = 'review'
        - proficiency >= 50: status = 'review'
        - proficiency < 30: status = 'attention'
        
        🔧 重要修复：保护用户主动设置的attention状态（生词本）

        Args:
            user_id: 用户ID
            word_id: 单词ID

        Returns:
            bool: 是否更新了状态
        """
        try:
            # 获取用户单词关系和熟练度
            user_word = UserWord.get_by_user_and_word(user_id, word_id)
            if not user_word:
                # 如果不存在关系，创建新的
                user_word = UserWord.get_or_create(user_id, word_id)

            current_proficiency = user_word['proficiency'] or 0.0
            current_status = user_word['status'] or 'new'
            new_status = current_status

            # 🔧 关键修复：保护用户主动设置的attention状态
            # attention状态表示用户主动加入生词本，不应被自动转换
            if current_status == 'attention':
                print(f"🛡️ 保护生词本状态: word_id={word_id}, "
                      f"proficiency={current_proficiency:.1f}, "
                      f"状态保持为 attention（用户生词本）")
                return True  # 不修改attention状态

            # 🔧 修复关键问题：完善状态转换逻辑，覆盖所有proficiency范围
            # 应用主要规则（仅适用于非attention状态）
            if current_proficiency >= 50:
                new_status = 'review'
            elif current_proficiency < 30:
                new_status = 'attention'
            else:  # 30 <= proficiency < 50
                # 🔧 关键修复：中等proficiency的单词应该从new转为review
                # 因为已经有学习记录，不应该保持new状态
                if current_status == 'new':
                    new_status = 'review'
                else:
                    new_status = current_status  # 保持现有状态

            # 如果状态需要更新
            if new_status != current_status:
                success = UserWord.update_status(user_id, word_id, new_status)
                if success:
                    print(f"✅ 状态更新成功: word_id={word_id}, "
                          f"proficiency={current_proficiency:.1f}, "
                          f"{current_status} → {new_status}")
                return success

            return True  # 状态无需更新也算成功

        except Exception as e:
            print(f"❌ 基于熟练度更新状态失败: user_id={user_id}, "
                  f"word_id={word_id}, error={e}")
            return False

    @staticmethod
    @timing()
    def update_word_status_by_star_level(user_id: int, word_id: int,
                                       plan_date: date = None) -> bool:
        """
        基于星级更新单词状态 - 兜底规则
        严格按照data_model.md第343行规范

        兜底规则:
        - 针对new状态的单词，如果star_level >= 2，自动转为'review'状态
        - 理由：新词从3星开始，达到2星说明用户已有学习尝试

        Args:
            user_id: 用户ID
            word_id: 单词ID
            plan_date: 计划日期，默认为今天

        Returns:
            bool: 是否更新了状态
        """
        if plan_date is None:
            plan_date = date.today()

        try:
            # 获取用户单词关系
            user_word = UserWord.get_by_user_and_word(user_id, word_id)
            if not user_word:
                return True  # 不存在关系，无需处理

            current_status = user_word['status'] or 'new'

            # 兜底规则只适用于new状态的单词
            if current_status != 'new':
                return True

            # 获取学习计划中的星级
            query = """
            SELECT star_level FROM learning_plan
            WHERE user_id = ? AND word_id = ? AND planned_date = ?
            """
            results = db.execute_query(query, (user_id, word_id, plan_date))

            if not results:
                return True  # 没有学习计划，无需处理

            star_level = results[0]['star_level']

            # 应用兜底规则：star_level >= 2 → 'review'状态
            if star_level >= 2:
                success = UserWord.update_status(user_id, word_id, 'review')
                if success:
                    print(f"✅ 兜底规则更新成功: word_id={word_id}, "
                          f"star_level={star_level}, new → review")
                return success

            return True  # 条件不满足，无需更新

        except Exception as e:
            print(f"❌ 基于星级更新状态失败: user_id={user_id}, "
                  f"word_id={word_id}, error={e}")
            return False

    @staticmethod
    @timing()
    def batch_update_status_on_learning_exit(user_id: int,
                                           plan_date: date = None) -> Dict[str, Any]:
        """
        学习退出时批量更新状态
        结合主要规则和兜底规则，确保状态一致性

        Args:
            user_id: 用户ID
            plan_date: 计划日期，默认为今天

        Returns:
            Dict[str, Any]: 更新结果统计
        """
        if plan_date is None:
            plan_date = date.today()

        try:
            # 获取当日学习计划中的所有单词
            query = """
            SELECT DISTINCT word_id FROM learning_plan
            WHERE user_id = ? AND planned_date = ?
            """
            results = db.execute_query(query, (user_id, plan_date))

            if not results:
                return {'total': 0, 'updated_by_proficiency': 0,
                       'updated_by_star_level': 0, 'errors': 0}

            total_words = len(results)
            updated_by_proficiency = 0
            updated_by_star_level = 0
            errors = 0

            for row in results:
                word_id = row['word_id']

                try:
                    # 先尝试基于熟练度更新（主要规则）
                    if StatusTransitionService.update_word_status_by_proficiency(
                        user_id, word_id
                    ):
                        updated_by_proficiency += 1

                    # 再尝试基于星级更新（兜底规则）
                    if StatusTransitionService.update_word_status_by_star_level(
                        user_id, word_id, plan_date
                    ):
                        updated_by_star_level += 1

                except Exception as e:
                    print(f"❌ 批量更新单词状态失败: word_id={word_id}, error={e}")
                    errors += 1

            result = {
                'total': total_words,
                'updated_by_proficiency': updated_by_proficiency,
                'updated_by_star_level': updated_by_star_level,
                'errors': errors,
                'success_rate': round((total_words - errors) / max(total_words, 1), 3)
            }

            print(f"📊 批量状态更新完成: {result}")
            return result

        except Exception as e:
            print(f"❌ 批量状态更新失败: user_id={user_id}, error={e}")
            return {'total': 0, 'updated_by_proficiency': 0,
                   'updated_by_star_level': 0, 'errors': 1}

    @staticmethod
    @timing()
    def get_status_transition_stats(user_id: int, days: int = 7) -> Dict[str, Any]:
        """
        获取状态转换统计信息

        Args:
            user_id: 用户ID
            days: 统计天数

        Returns:
            Dict[str, Any]: 状态转换统计
        """
        try:
            query = """
            SELECT
                status,
                COUNT(*) as count
            FROM user_word
            WHERE user_id = ?
            GROUP BY status
            """
            results = db.execute_query(query, (user_id,))

            stats = {
                'new': 0,
                'review': 0,
                'attention': 0,
                'mastered': 0,
                'total': 0
            }

            for row in results:
                status = row['status'] or 'new'
                count = row['count'] or 0
                if status in stats:
                    stats[status] = count
                stats['total'] += count

            # 计算比例
            total = max(stats['total'], 1)
            stats['new_rate'] = round(stats['new'] / total, 3)
            stats['review_rate'] = round(stats['review'] / total, 3)
            stats['attention_rate'] = round(stats['attention'] / total, 3)
            stats['mastered_rate'] = round(stats['mastered'] / total, 3)

            return stats

        except Exception as e:
            print(f"❌ 获取状态转换统计失败: user_id={user_id}, error={e}")
            return {
                'new': 0, 'review': 0, 'attention': 0, 'mastered': 0, 'total': 0,
                'new_rate': 0.0, 'review_rate': 0.0, 'attention_rate': 0.0,
                'mastered_rate': 0.0
            }
