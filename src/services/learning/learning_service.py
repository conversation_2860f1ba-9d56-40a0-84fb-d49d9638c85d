"""
学习服务
负责处理学习过程中的核心业务逻辑
集成状态转换服务，严格按照data_model.md规范
"""
from datetime import date, timedelta
from typing import Dict, Any, List, Optional

from ...models.word import UserWord
from ...models.learning import WordRecord
from ...models.planning import LearningPlan
from ...config import Config
from ..proficiency.calculator import ProficiencyCalculator
from .plan_service import LearningPlanService
from .status_service import StatusTransitionService
from ...models.base import db
from ..feature_learning.feature_learning_service import FeatureLearningService


class LearningService:
    """学习服务类"""

    @staticmethod
    def submit_answer(user_id: int, word_id: int, user_input: str,
                     duration_seconds: float, plan_date: date = None) -> Dict[str, Any]:
        """
        提交答案并更新学习记录
        
        Args:
            user_id: 用户ID
            word_id: 单词ID
            user_input: 用户输入
            duration_seconds: 答题用时
            plan_date: 计划日期，默认为今天
            
        Returns:
            Dict[str, Any]: 提交结果
        """
        if plan_date is None:
            plan_date = date.today()

        try:
            # 🚀 性能优化：单次查询获取单词信息和当前星级
            word_and_plan = LearningPlan.get_word_with_current_star(user_id, word_id, plan_date)
            if not word_and_plan:
                return {'success': False, 'message': '单词或学习计划不存在'}

            # 转换sqlite3.Row为字典以便访问
            word_and_plan_dict = dict(word_and_plan)
            correct_answer = word_and_plan_dict['english_word']
            current_star = word_and_plan_dict['star_level'] or 3  # 默认值

            # 🚀 增强的答案验证逻辑 - 集成特征学习
            similarity_analysis = FeatureLearningService.enhanced_similarity_check(
                user_input, correct_answer
            )
            
            is_correct = similarity_analysis['exact_match']
            
            # 基于特征学习的正确性分析
            if similarity_analysis['exact_match']:
                correctness_type = "exact"
                feedback_message = similarity_analysis['suggested_feedback']
                suggestions = []
            elif similarity_analysis['is_close_match']:
                correctness_type = "spelling_error"
                feedback_message = similarity_analysis['suggested_feedback']
                suggestions = [f"正确拼写: {correct_answer}"]
            elif similarity_analysis['has_minor_errors']:
                correctness_type = "partial"
                feedback_message = similarity_analysis['suggested_feedback']
                suggestions = [f"正确答案是: {correct_answer}"]
            else:
                correctness_type = "incorrect"
                feedback_message = "答案差异较大，需要更多练习！"
                suggestions = [
                    f"正确答案: {correct_answer}",
                    f"中文意思: {word_and_plan_dict.get('chinese_meaning', '')}"
                ]

            # 星级变化规则：答对+1星，答错-2星
            if is_correct:
                new_star = min(Config.MAX_STAR_LEVEL, current_star + 1)
                points_change = Config.CORRECT_ANSWER_POINTS
            else:
                new_star = max(Config.MIN_STAR_LEVEL, current_star - 2)
                points_change = -Config.WRONG_ANSWER_PENALTY

            print(f"🌟 星级更新: {word_and_plan['chinese_meaning']} ({current_star}⭐ -> {new_star}⭐) {'✅答对' if is_correct else '❌答错'}")

            # 🚀 性能优化：批量更新操作
            LearningService._batch_update_learning_data(
                user_id, word_id, plan_date, duration_seconds,
                user_input, correct_answer, is_correct, new_star, points_change
            )

            # 🔧 修复：获取真实的连击数统计
            consecutive_correct = LearningService._get_consecutive_correct_count(user_id, plan_date)
            print(f"🔥 连击统计: 用户{user_id}当前连击数 = {consecutive_correct}")

            # 🎯 新增：即时检查购物券奖励条件
            voucher_result = LearningService._check_instant_voucher_rewards(
                user_id, is_correct, consecutive_correct
            )
            
            # 🧠 新增：特征学习处理 - 获取最新的记录ID后异步处理特征提取
            record_id = LearningService._get_latest_record_id(user_id, word_id, plan_date)
            if record_id:
                # 准备特征学习上下文
                feature_context = {
                    'word_length': len(correct_answer),
                    'word_difficulty': word_and_plan_dict.get('difficulty', 3),
                    'session_position': LearningService._get_session_position(user_id, plan_date),
                    'learning_stage': 'practice'
                }
                
                # 异步处理特征学习，不影响主流程性能
                FeatureLearningService.process_user_input(
                    user_id=user_id,
                    word_id=word_id,
                    record_id=record_id,
                    user_input=user_input,
                    correct_answer=correct_answer,
                    duration_seconds=duration_seconds,
                    context=feature_context,
                    async_processing=True
                )

            return {
                'success': True,
                'is_correct': is_correct,
                'correctness_type': correctness_type,
                'correct_answer': correct_answer,
                'user_input': user_input,
                'feedback_message': feedback_message,
                'suggestions': suggestions,
                'new_star_level': new_star,
                'points_change': points_change,
                'consecutive_correct': consecutive_correct,  # 返回连击数
                'voucher_result': voucher_result  # 返回购物券奖励结果
            }

        except Exception as e:
            return {'success': False, 'message': f'提交答案失败: {str(e)}'}

    @staticmethod
    def _check_instant_voucher_rewards(user_id: int, is_correct: bool,
                                       consecutive_correct: int) -> Dict[str, Any]:
        """
        即时检查购物券奖励条件
        每次答题后立即检查，达成条件立即发放
        """
        voucher_result = {
            'vouchers_earned': 0,
            'reason': '',
            'daily_limit_reached': False
        }

        try:
            from ...services.voucher_service import VoucherService

            # 只在答对时检查连击奖励
            if is_correct and consecutive_correct >= 10:
                streak_result = VoucherService.check_streak_reward(
                    user_id, consecutive_correct
                )
                if streak_result['triggered']:
                    voucher_result.update(streak_result)

            return voucher_result

        except Exception as e:
            print(f"检查即时购物券奖励失败: {e}")
            return voucher_result

    @staticmethod
    def _calculate_similarity(str1: str, str2: str) -> float:
        """计算两个字符串的相似度（简单的编辑距离算法）"""
        if not str1 or not str2:
            return 0.0

        len1, len2 = len(str1), len(str2)
        if len1 == 0:
            return 0.0 if len2 > 0 else 1.0
        if len2 == 0:
            return 0.0

        # 简单的相似度计算：共同字符数 / 最长字符串长度
        common_chars = sum(1 for c in str1 if c in str2)
        return common_chars / max(len1, len2)

    @staticmethod
    def _batch_update_learning_data(user_id: int, word_id: int, plan_date: date,
                                   duration_seconds: float, user_input: str,
                                   correct_answer: str, is_correct: bool,
                                   new_star: int, points_change: int):
        """
        批量更新学习数据 - 减少数据库操作次数
        🔧 修复数据不一致问题：添加实时user_word统计更新

        Args:
            user_id: 用户ID
            word_id: 单词ID
            plan_date: 计划日期
            duration_seconds: 答题用时
            user_input: 用户输入
            correct_answer: 正确答案
            is_correct: 是否正确
            new_star: 新星级
            points_change: 积分变化
        """
        # 使用事务确保数据一致性
        with db.get_connection() as conn:
            cursor = conn.cursor()

            # 1. 插入学习记录
            cursor.execute("""
                INSERT INTO word_record (user_id, word_id, date, duration_seconds,
                                       user_input, answer, is_correct)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (user_id, word_id, plan_date, duration_seconds,
                  user_input, correct_answer, is_correct))

            # 2. 更新学习计划星级
            cursor.execute("""
                UPDATE learning_plan
                SET star_level = ?
                WHERE user_id = ? AND word_id = ? AND planned_date = ?
            """, (new_star, user_id, word_id, plan_date))

            # 3. 更新用户积分
            cursor.execute("""
                UPDATE user
                SET points = points + ?
                WHERE id = ?
            """, (points_change, user_id))

            # 🔧 4. 实时更新user_word统计数据 - 修复数据不一致问题
            LearningService._update_user_word_stats_realtime(cursor, user_id, word_id, plan_date)

            conn.commit()

    @staticmethod
    def _update_user_word_stats_realtime(cursor, user_id: int, word_id: int, current_date: date):
        """
        实时更新user_word统计数据
        🔧 解决数据不一致的核心方法

        Args:
            cursor: 数据库游标（在事务中）
            user_id: 用户ID
            word_id: 单词ID
            current_date: 当前学习日期
        """
        # 1. 计算该单词的最新统计数据
        cursor.execute("""
            SELECT
                COUNT(*) as learning_count,
                SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
                AVG(duration_seconds) as avg_duration,
                MAX(date) as last_learning_date
            FROM word_record
            WHERE user_id = ? AND word_id = ?
        """, (user_id, word_id))

        stats = cursor.fetchone()
        learning_count = stats[0] or 0
        correct_count = stats[1] or 0
        avg_duration = stats[2] or 0.0
        last_learning_date = stats[3] or str(current_date)

        # 2. 计算熟练度
        from ...services.proficiency.calculator import ProficiencyCalculator
        proficiency = ProficiencyCalculator.calculate_proficiency(
            learning_count, correct_count, avg_duration, last_learning_date
        )

        # 3. 更新user_word表
        cursor.execute("""
            UPDATE user_word
            SET learning_count = ?,
                correct_count = ?,
                proficiency = ?,
                last_learning_date = ?
            WHERE user_id = ? AND word_id = ?
        """, (learning_count, correct_count, proficiency, last_learning_date, user_id, word_id))

        # 4. 如果user_word记录不存在，创建它
        if cursor.rowcount == 0:
            cursor.execute("""
                INSERT INTO user_word (user_id, word_id, learning_count, correct_count,
                                     proficiency, last_learning_date, status)
                VALUES (?, ?, ?, ?, ?, ?, 'new')
            """, (user_id, word_id, learning_count, correct_count, proficiency, last_learning_date))

        # 🔧 关键修复：在同一事务中进行状态转换
        # 避免嵌套事务导致的死锁问题
        LearningService._update_word_status_in_transaction(cursor, user_id, word_id, proficiency)

    @staticmethod
    def _update_word_status_in_transaction(cursor, user_id: int, word_id: int, proficiency: float):
        """
        在同一事务中更新单词状态
        避免嵌套事务导致的死锁问题

        Args:
            cursor: 数据库游标（在事务中）
            user_id: 用户ID
            word_id: 单词ID
            proficiency: 当前熟练度
        """
        try:
            # 获取当前状态
            cursor.execute("""
                SELECT status FROM user_word
                WHERE user_id = ? AND word_id = ?
            """, (user_id, word_id))

            result = cursor.fetchone()
            if not result:
                return  # 记录不存在，跳过状态转换

            current_status = result[0]

            # 应用状态转换逻辑（复制自StatusTransitionService）
            new_status = current_status

            # 跳过attention状态的单词（保持用户手动设置）
            if current_status == 'attention':
                return

            # 应用主要规则
            if proficiency >= 50:
                new_status = 'review'
            elif proficiency < 30:
                new_status = 'attention'
            else:  # 30 <= proficiency < 50
                if current_status == 'new':
                    new_status = 'review'  # 关键修复

            # 如果状态需要更新
            if new_status != current_status:
                cursor.execute("""
                    UPDATE user_word SET status = ?
                    WHERE user_id = ? AND word_id = ?
                """, (new_status, user_id, word_id))

                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"实时状态转换: user_id={user_id}, word_id={word_id}, "
                           f"{current_status} -> {new_status}, proficiency={proficiency:.1f}")

        except Exception as e:
            # 状态转换失败不应影响主流程，只记录错误
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"实时状态转换失败: user_id={user_id}, word_id={word_id}, error={e}")

    @staticmethod
    def finish_learning_session(user_id: int, session_date: date = None) -> Dict[str, Any]:
        """
        完成学习会话，批量更新用户数据
        实现data_model.md第320-344行规范

        Args:
            user_id: 用户ID
            session_date: 会话日期，默认为今天

        Returns:
            Dict[str, Any]: 更新结果
        """
        if session_date is None:
            session_date = date.today()

        try:
            # 1. 获取当日学习记录
            session_records = WordRecord.get_today_records(user_id)
            if not session_records:
                return {
                    'success': False,
                    'message': '没有找到今日学习记录'
                }

            # 2. 统计会话数据
            session_stats = LearningService._calculate_session_stats(session_records)
            
            # 3. 按单词分组统计更新数据
            word_stats = {}
            updated_words = 0

            for record in session_records:
                word_id = record['word_id']
                if word_id not in word_stats:
                    word_stats[word_id] = []
                word_stats[word_id].append(record)

            # 为每个单词更新统计数据
            for word_id, records in word_stats.items():
                # 获取该单词的总体统计
                total_stats = WordRecord.get_user_word_stats(user_id, word_id)

                # 计算熟练度 - 严格按照data_model.md第164-171行
                proficiency = ProficiencyCalculator.calculate_proficiency(
                    total_stats['learning_count'],
                    total_stats['correct_count'],
                    total_stats['avg_duration'],
                    total_stats['last_learning_date']
                )

                # 更新user_word表统计数据
                UserWord.update_learning_stats(
                    user_id, word_id,
                    total_stats['learning_count'],
                    total_stats['correct_count'],
                    proficiency
                )
                updated_words += 1

            # 4. 执行状态转换规则 - 严格按照data_model.md第337-344行
            status_update_result = StatusTransitionService.batch_update_status_on_learning_exit(
                user_id, session_date
            )

            # 5. 检查当日计划完成情况
            completion_stats = LearningPlan.check_daily_completion(user_id, session_date)
            is_daily_completed = completion_stats.get('is_completed', False)

            # 6. 如果完成，生成次日学习计划
            next_day_plan_generated = False
            if is_daily_completed:
                tomorrow = session_date + timedelta(days=1)
                if LearningPlanService.generate_daily_plan(user_id, tomorrow):
                    next_day_plan_generated = True

            # 🎯 新增：检查会话完成购物券奖励
            voucher_session_result = LearningService._check_session_voucher_rewards(
                user_id, session_stats, session_date
            )

            result = {
                'daily_completed': is_daily_completed,
                'next_day_plan_generated': next_day_plan_generated,
                'updated_words': updated_words,
                'status_updates': status_update_result,
                'completion_stats': completion_stats,
                'session_stats': session_stats,
                'voucher_session_result': voucher_session_result
            }

            return result

        except Exception as e:
            return {
                'success': False,
                'message': f'完成学习会话失败: {str(e)}'
            }

    @staticmethod
    def _calculate_session_stats(session_records: List) -> Dict[str, Any]:
        """计算会话统计数据"""
        if not session_records:
            return {
                'words_completed': 0,
                'correct_count': 0,
                'total_attempts': 0,
                'accuracy_rate': 0.0,
                'session_duration': 0.0
            }
        
        total_attempts = len(session_records)
        correct_count = sum(1 for record in session_records if record['is_correct'])
        accuracy_rate = correct_count / total_attempts if total_attempts > 0 else 0.0
        
        # 计算总学习时长
        session_duration = sum(record['duration_seconds'] for record in session_records)
        
        # 计算完成的单词数（去重）
        words_completed = len(set(record['word_id'] for record in session_records))
        
        return {
            'words_completed': words_completed,
            'correct_count': correct_count,
            'total_attempts': total_attempts,
            'accuracy_rate': accuracy_rate,
            'session_duration': session_duration
        }

    @staticmethod
    def _check_session_voucher_rewards(user_id: int, session_stats: Dict[str, Any], session_date: date) -> Dict[str, Any]:
        """
        检查会话完成时的购物券奖励
        在学习会话结束时触发优秀/良好表现奖励
        """
        try:
            from ...services.voucher_service import VoucherService
            
            words_completed = session_stats.get('words_completed', 0)
            # 🔧 修复：传入total_attempts而不是correct_count，避免正确率计算错误
            total_attempts = session_stats.get('total_attempts', 0)
            correct_count = session_stats.get('correct_count', 0)
            
            # 使用VoucherService检查会话奖励，传入正确的参数
            session_result = VoucherService.check_session_reward_fixed(
                user_id, words_completed, total_attempts, correct_count
            )
            
            if session_result['triggered']:
                print(f"🎉 会话奖励触发: 用户{user_id} 获得{session_result['vouchers_earned']}张购物券 - {session_result['reason']}")
            
            return session_result
            
        except Exception as e:
            print(f"检查会话购物券奖励失败: {e}")
            return {
                'vouchers_earned': 0,
                'reason': '',
                'triggered': False
            }

    @staticmethod
    def add_to_vocabulary_book(user_id: int, word_id: int) -> bool:
        """
        添加单词到生词本

        Args:
            user_id: 用户ID
            word_id: 单词ID

        Returns:
            bool: 是否添加成功
        """
        try:
            # 获取或创建用户单词关系
            user_word = UserWord.get_or_create(user_id, word_id)

            # 更新状态为attention
            UserWord.update_status(user_id, word_id, 'attention')

            return True
        except Exception as e:
            print(f"Error adding to vocabulary book: {e}")
            return False

    @staticmethod
    def remove_from_vocabulary_book(user_id: int, word_id: int) -> bool:
        """
        从生词本移除单词

        Args:
            user_id: 用户ID
            word_id: 单词ID

        Returns:
            bool: 是否移除成功
        """
        try:
            # 获取用户单词关系
            user_word = UserWord.get_by_user_and_word(user_id, word_id)
            if user_word and user_word['status'] == 'attention':
                # 根据熟练度决定新状态
                if user_word['proficiency'] > 0:
                    UserWord.update_status(user_id, word_id, 'review')
                else:
                    UserWord.update_status(user_id, word_id, 'new')

            return True
        except Exception as e:
            print(f"Error removing from vocabulary book: {e}")
            return False

    @staticmethod
    def _get_consecutive_correct_count(user_id: int, plan_date: date) -> int:
        """
        获取用户当日连续答对的题目数量
        基于word_record表的最新记录计算，健壮实现
        
        Args:
            user_id: 用户ID
            plan_date: 计划日期
            
        Returns:
            int: 连续答对的题目数量
        """
        try:
            # 先检查今日是否有学习记录
            count_query = """
            SELECT COUNT(*) as count FROM word_record 
            WHERE user_id = ? AND date = ?
            """
            count_result = db.execute_query(count_query, 
                                          (user_id, plan_date.strftime('%Y-%m-%d')))
            
            if not count_result or count_result[0]['count'] == 0:
                print(f"🔍 连击统计: 用户{user_id}今日无学习记录")
                return 0
            
            # 获取当日学习记录，按ID倒序（最新记录在前）
            query = """
            SELECT is_correct FROM word_record 
            WHERE user_id = ? AND date = ?
            ORDER BY id DESC
            LIMIT 50
            """
            results = db.execute_query(query, 
                                     (user_id, plan_date.strftime('%Y-%m-%d')))
            
            if not results:
                print(f"🔍 连击统计: 查询结果为空")
                return 0
            
            # 从最新记录开始计算连续正确数
            consecutive_count = 0
            for i, record in enumerate(results):
                is_correct = record['is_correct']
                # SQLite中布尔值可能是0/1
                if is_correct == 1 or is_correct == True:
                    consecutive_count += 1
                    # 只显示连击范围内的记录，避免误导
                    if i < 5:  # 只显示前5条记录的详情
                        print(f"  最近记录{i+1}: ✅正确")
                else:
                    # 遇到错误记录，连击中断
                    if consecutive_count > 0:
                        print(f"  连击在第{consecutive_count}题后中断（遇到历史错误记录）")
                    break  # 遇到错误记录就停止计算
            
            print(f"🔥 连击统计完成: 用户{user_id}当前连击数={consecutive_count}")
            return consecutive_count
            
        except Exception as e:
            print(f"❌ 获取连击数异常: {e}")
            import traceback
            traceback.print_exc()
            return 0
    
    @staticmethod
    def _get_latest_record_id(user_id: int, word_id: int, plan_date: date) -> Optional[int]:
        """
        获取最新插入的学习记录ID
        用于特征学习的记录关联
        """
        try:
            query = """
            SELECT id FROM word_record 
            WHERE user_id = ? AND word_id = ? AND date = ?
            ORDER BY id DESC 
            LIMIT 1
            """
            results = db.execute_query(query, 
                                     (user_id, word_id, plan_date.strftime('%Y-%m-%d')))
            
            if results:
                return results[0]['id']
            return None
            
        except Exception as e:
            print(f"❌ 获取最新记录ID失败: {e}")
            return None
    
    @staticmethod
    def _get_session_position(user_id: int, plan_date: date) -> int:
        """
        获取当前答题在本次学习会话中的位置
        """
        try:
            query = """
            SELECT COUNT(*) as count FROM word_record 
            WHERE user_id = ? AND date = ?
            """
            results = db.execute_query(query, 
                                     (user_id, plan_date.strftime('%Y-%m-%d')))
            
            if results:
                return results[0]['count']
            return 0
            
        except Exception as e:
            print(f"❌ 获取会话位置失败: {e}")
            return 0
