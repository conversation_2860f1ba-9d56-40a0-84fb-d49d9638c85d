"""
智能复习单词选择器
解决传统proficiency排序导致的重复学习问题
基于时间衰减、学习频率和间隔重复理论的综合评分算法
"""

import random
from datetime import date, timedelta
from typing import List, Dict, Tuple
from ...core.database import db


class SmartReviewSelector:
    """智能复习单词选择器"""
    
    # 算法参数配置
    TIME_DECAY_WEIGHTS = {
        'recent_study_penalty': 0.3,    # 最近学习惩罚权重
        'interval_bonus': 0.4,          # 间隔奖励权重
        'frequency_penalty': 0.3        # 频率惩罚权重
    }
    
    TARGET_INTERVALS = [
        # 根据熟练度确定目标复习间隔（天）
        ((0, 50), 1),      # 低熟练度：1天间隔
        ((50, 75), 3),     # 中等熟练度：3天间隔
        ((75, 100), 7)     # 高熟练度：7天间隔
    ]
    
    @classmethod
    def select_review_words(cls, user_id: int, count: int, 
                          exclude_word_ids: List[int] = None) -> List[Dict]:
        """
        智能选择复习单词
        
        算法核心思想：
        1. 基础分数：基于熟练度，越低分数越高
        2. 时间衰减：最近学过的单词降低优先级
        3. 频率调节：过度学习的单词降低优先级
        4. 加权随机：避免完全固化的学习模式
        
        Args:
            user_id: 用户ID
            count: 需要选择的单词数量
            exclude_word_ids: 要排除的单词ID列表
            
        Returns:
            List[Dict]: 选择的复习单词列表
        """
        # 获取候选单词
        candidates = cls._get_candidate_words(user_id, exclude_word_ids)
        
        if not candidates:
            return []
        
        # 计算每个单词的智能评分
        scored_words = []
        for word in candidates:
            score = cls._calculate_smart_score(word, user_id)
            scored_words.append((word, score))
        
        # 加权随机选择
        selected_words = cls._weighted_random_selection(scored_words, count)
        
        # 标记类型
        for word in selected_words:
            word['position_type'] = 'review'
            word['item_type'] = 'review'
        
        return selected_words
    
    @classmethod
    def _get_candidate_words(cls, user_id: int, exclude_word_ids: List[int] = None) -> List[Dict]:
        """
        获取候选复习单词
        排除：
        1. 不在复习状态的单词
        2. 明确排除的单词
        3. 在免复习期内的单词
        """
        
        if exclude_word_ids:
            placeholders = ','.join(['?' for _ in exclude_word_ids])
            query = f"""
            SELECT w.*, uw.proficiency, uw.learning_count, uw.correct_count, 
                   uw.last_learning_date, uw.status, uw.skip_review_until
            FROM word w
            JOIN user_word uw ON w.id = uw.word_id
            WHERE uw.user_id = ?
              AND uw.status IN ('review', 'attention')
              AND w.id NOT IN ({placeholders})
              AND (uw.skip_review_until IS NULL OR uw.skip_review_until <= date('now'))
            ORDER BY w.priority ASC, w.id ASC
            """
            params = [user_id] + exclude_word_ids
        else:
            query = """
            SELECT w.*, uw.proficiency, uw.learning_count, uw.correct_count, 
                   uw.last_learning_date, uw.status, uw.skip_review_until
            FROM word w
            JOIN user_word uw ON w.id = uw.word_id
            WHERE uw.user_id = ? 
              AND uw.status IN ('review', 'attention')
              AND (uw.skip_review_until IS NULL OR uw.skip_review_until <= date('now'))
            ORDER BY w.priority ASC, w.id ASC
            """
            params = [user_id]
        
        results = db.execute_query(query, params)
        return [dict(row) for row in results]
    
    @classmethod
    def _calculate_smart_score(cls, word: Dict, user_id: int) -> float:
        """
        计算单词的智能评分
        
        评分公式：
        smart_score = base_score × time_factor × frequency_factor
        
        Args:
            word: 单词信息字典
            user_id: 用户ID
            
        Returns:
            float: 智能评分（越高越优先）
        """
        # 1. 基础分数：基于熟练度
        proficiency = float(word.get('proficiency', 0))
        base_score = (100 - proficiency) / 100  # 越低熟练度分数越高
        
        # 2. 时间衰减因子
        time_factor = cls._calculate_time_factor(word, proficiency)
        
        # 3. 频率调节因子
        frequency_factor = cls._calculate_frequency_factor(word, user_id)
        
        # 综合评分
        smart_score = base_score * time_factor * frequency_factor
        
        return max(0.01, smart_score)  # 确保最小值，避免完全排除
    
    @classmethod
    def _calculate_time_factor(cls, word: Dict, proficiency: float) -> float:
        """
        计算时间衰减因子
        
        考虑因素：
        1. 距离上次学习的天数
        2. 基于熟练度的目标间隔
        3. 最近学习的惩罚
        """
        last_learning_date = word.get('last_learning_date')
        if not last_learning_date:
            return 1.5  # 从未学过，给予奖励
        
        try:
            # 解析日期（可能包含时间）
            if ' ' in last_learning_date:
                last_date = date.fromisoformat(last_learning_date.split(' ')[0])
            else:
                last_date = date.fromisoformat(last_learning_date)
            
            days_since_last = max(0, (date.today() - last_date).days)
        except:
            return 1.0  # 日期解析失败，使用默认值
        
        # 根据熟练度确定目标间隔
        target_interval = cls._get_target_interval(proficiency)
        
        # 计算时间因子
        if days_since_last == 0:
            # 今天刚学过，重度惩罚
            time_factor = 0.1
        elif days_since_last < target_interval:
            # 距离目标间隔还太近，轻度惩罚
            time_factor = 0.3 + 0.4 * (days_since_last / target_interval)
        elif days_since_last == target_interval:
            # 刚好到目标间隔，最佳时机
            time_factor = 1.0
        else:
            # 超过目标间隔，逐渐增加权重，但有上限
            excess_days = days_since_last - target_interval
            time_factor = min(2.0, 1.0 + excess_days * 0.1)
        
        return time_factor
    
    @classmethod
    def _calculate_frequency_factor(cls, word: Dict, user_id: int) -> float:
        """
        计算频率调节因子
        
        考虑最近7天的学习频率，避免过度学习某些单词
        """
        word_id = word['id']
        
        # 查询最近7天的学习次数
        query = """
        SELECT COUNT(*) as recent_count
        FROM word_record
        WHERE user_id = ? AND word_id = ? 
          AND date >= date('now', '-7 days')
        """
        
        result = db.execute_query(query, (user_id, word_id))
        recent_count = result[0]['recent_count'] if result else 0
        
        # 计算频率因子
        if recent_count == 0:
            frequency_factor = 1.2  # 最近没学过，给予奖励
        elif recent_count <= 2:
            frequency_factor = 1.0  # 正常频率
        elif recent_count <= 4:
            frequency_factor = 0.7  # 频率较高，轻度惩罚
        else:
            frequency_factor = 0.4  # 频率过高，重度惩罚
        
        return frequency_factor
    
    @classmethod
    def _get_target_interval(cls, proficiency: float) -> int:
        """根据熟练度获取目标复习间隔"""
        for (min_prof, max_prof), interval in cls.TARGET_INTERVALS:
            if min_prof <= proficiency < max_prof:
                return interval
        return 7  # 默认间隔
    
    @classmethod
    def _weighted_random_selection(cls, scored_words: List[Tuple[Dict, float]], 
                                 count: int) -> List[Dict]:
        """
        基于评分进行加权随机选择
        
        避免完全固化的学习模式，引入适度随机性
        """
        if len(scored_words) <= count:
            return [word for word, _ in scored_words]
        
        # 提取单词和权重
        words = [word for word, _ in scored_words]
        weights = [score ** 1.5 for _, score in scored_words]  # 平方根放大差异
        
        # 加权随机选择
        selected_words = []
        available_indices = list(range(len(words)))
        available_weights = weights.copy()
        
        for _ in range(count):
            if not available_indices:
                break
            
            # 归一化权重
            total_weight = sum(available_weights)
            if total_weight <= 0:
                # 如果所有权重都为0，使用均匀分布
                selected_local_idx = random.randint(0, len(available_indices) - 1)
            else:
                normalized_weights = [w / total_weight for w in available_weights]
                selected_local_idx = random.choices(range(len(available_indices)), weights=normalized_weights)[0]
            
            # 获取实际的单词索引
            actual_idx = available_indices[selected_local_idx]
            selected_words.append(words[actual_idx])
            
            # 移除已选择的单词
            available_indices.pop(selected_local_idx)
            available_weights.pop(selected_local_idx)
        
        return selected_words
    
    @classmethod
    def get_selection_analysis(cls, user_id: int, selected_words: List[Dict]) -> Dict:
        """
        获取选择分析信息，用于调试和优化
        
        Returns:
            Dict: 包含选择统计和分析信息
        """
        if not selected_words:
            return {'total': 0, 'analysis': []}
        
        analysis = []
        for word in selected_words:
            score = cls._calculate_smart_score(word, user_id)
            proficiency = float(word.get('proficiency', 0))
            last_date = word.get('last_learning_date', 'Never')
            
            analysis.append({
                'word': word.get('english_word', 'Unknown'),
                'proficiency': proficiency,
                'smart_score': round(score, 3),
                'last_learning_date': last_date,
                'status': word.get('status', 'Unknown')
            })
        
        return {
            'total': len(selected_words),
            'analysis': analysis,
            'avg_proficiency': sum(float(w.get('proficiency', 0)) for w in selected_words) / len(selected_words),
            'score_range': {
                'min': min(cls._calculate_smart_score(w, user_id) for w in selected_words),
                'max': max(cls._calculate_smart_score(w, user_id) for w in selected_words)
            }
        }