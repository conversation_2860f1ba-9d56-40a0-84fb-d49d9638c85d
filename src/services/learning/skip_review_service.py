"""
免复习选择服务
支持用户在完成每日学习后选择哪些词汇可以免复习
"""

from datetime import date, timedelta
from typing import List, Dict, Any, Optional
import logging

from ...core.database import db
from ...models.word import UserWord

logger = logging.getLogger(__name__)

class SkipReviewService:
    """免复习选择服务"""
    
    @staticmethod
    def get_today_learned_words(user_id: int, learning_date: date = None) -> List[Dict[str, Any]]:
        """
        获取用户今日学习的单词列表，用于免复习选择
        
        Args:
            user_id: 用户ID
            learning_date: 学习日期，默认为今天
            
        Returns:
            List[Dict]: 今日学习的单词列表，包含熟练度等信息
        """
        if learning_date is None:
            learning_date = date.today()
            
        query = """
        SELECT DISTINCT 
            w.id as word_id,
            w.english_word,
            w.chinese_meaning,
            uw.proficiency,
            uw.learning_count,
            uw.correct_count,
            uw.status,
            uw.skip_review_until,
            -- 计算今日学习次数
            COUNT(wr.id) as today_learning_count,
            -- 计算今日正确次数
            SUM(CASE WHEN wr.is_correct = 1 THEN 1 ELSE 0 END) as today_correct_count
        FROM word w
        JOIN user_word uw ON w.id = uw.word_id
        JOIN word_record wr ON w.id = wr.word_id AND wr.user_id = uw.user_id
        WHERE uw.user_id = ? 
          AND wr.date = ?
        GROUP BY w.id, w.english_word, w.chinese_meaning, 
                 uw.proficiency, uw.learning_count, uw.correct_count, 
                 uw.status, uw.skip_review_until
        ORDER BY uw.proficiency DESC, w.english_word ASC
        """
        
        try:
            results = db.execute_query(query, (user_id, learning_date))
            words = []
            
            for row in results:
                # 计算今日学习表现
                today_accuracy = 0.0
                if row['today_learning_count'] > 0:
                    today_accuracy = (row['today_correct_count'] / row['today_learning_count']) * 100
                
                # 判断是否已在免复习期
                is_currently_skipped = False
                if row['skip_review_until']:
                    try:
                        skip_until = date.fromisoformat(row['skip_review_until'])
                        is_currently_skipped = skip_until > date.today()
                    except:
                        pass
                
                word_info = {
                    'word_id': row['word_id'],
                    'english_word': row['english_word'],
                    'chinese_meaning': row['chinese_meaning'],
                    'proficiency': float(row['proficiency'] or 0),
                    'total_learning_count': row['learning_count'] or 0,
                    'total_correct_count': row['correct_count'] or 0,
                    'status': row['status'],
                    'today_learning_count': row['today_learning_count'],
                    'today_correct_count': row['today_correct_count'],
                    'today_accuracy': round(today_accuracy, 1),
                    'is_currently_skipped': is_currently_skipped,
                    'skip_review_until': row['skip_review_until'],
                    # 推荐是否免复习（基于熟练度和今日表现）
                    'recommend_skip': SkipReviewService._should_recommend_skip(
                        float(row['proficiency'] or 0), 
                        today_accuracy,
                        row['today_learning_count']
                    )
                }
                words.append(word_info)
            
            logger.info(f"获取用户{user_id}在{learning_date}的学习单词: {len(words)}个")
            return words
            
        except Exception as e:
            logger.error(f"获取今日学习单词失败: user_id={user_id}, date={learning_date}, error={e}")
            return []
    
    @staticmethod
    def _should_recommend_skip(proficiency: float, today_accuracy: float, today_count: int) -> bool:
        """
        根据熟练度和今日表现推荐是否应该免复习
        
        Args:
            proficiency: 总体熟练度
            today_accuracy: 今日正确率
            today_count: 今日学习次数
            
        Returns:
            bool: 是否推荐免复习
        """
        # 推荐免复习的条件：
        # 1. 熟练度较高（>=80%）且今日表现良好（>=80%）
        # 2. 或者熟练度非常高（>=90%）
        # 3. 今日至少学习过1次
        
        if today_count == 0:
            return False
            
        if proficiency >= 90:
            return True
        elif proficiency >= 80 and today_accuracy >= 80:
            return True
        elif proficiency >= 70 and today_accuracy >= 90:
            return True
        
        return False
    
    @staticmethod
    def set_skip_review(user_id: int, word_ids: List[int], skip_days: int = 7) -> Dict[str, Any]:
        """
        设置指定单词的免复习期
        
        Args:
            user_id: 用户ID
            word_ids: 要免复习的单词ID列表
            skip_days: 免复习天数，默认7天
            
        Returns:
            Dict: 操作结果
        """
        if not word_ids:
            return {'success': False, 'message': '未选择任何单词'}
        
        if skip_days <= 0:
            return {'success': False, 'message': '免复习天数必须大于0'}
        
        # 计算免复习截止日期
        skip_until = date.today() + timedelta(days=skip_days)
        
        try:
            # 批量更新单词的免复习期
            placeholders = ','.join(['?' for _ in word_ids])
            query = f"""
            UPDATE user_word 
            SET skip_review_until = ?, updated_at = CURRENT_TIMESTAMP
            WHERE user_id = ? AND word_id IN ({placeholders})
            """
            
            params = [skip_until, user_id] + word_ids
            affected_rows = db.execute_update(query, params)
            
            if affected_rows > 0:
                logger.info(f"设置免复习成功: user_id={user_id}, "
                           f"words={len(word_ids)}, days={skip_days}, until={skip_until}")
                
                return {
                    'success': True,
                    'message': f'成功设置{affected_rows}个单词免复习{skip_days}天',
                    'affected_count': affected_rows,
                    'skip_until': skip_until.isoformat(),
                    'skip_days': skip_days
                }
            else:
                return {
                    'success': False,
                    'message': '没有找到匹配的单词记录'
                }
                
        except Exception as e:
            logger.error(f"设置免复习失败: user_id={user_id}, word_ids={word_ids}, error={e}")
            return {
                'success': False,
                'message': f'设置失败: {str(e)}'
            }
    
    @staticmethod
    def clear_skip_review(user_id: int, word_ids: List[int] = None) -> Dict[str, Any]:
        """
        清除指定单词的免复习期（或清除所有）
        
        Args:
            user_id: 用户ID
            word_ids: 要清除免复习的单词ID列表，None表示清除所有
            
        Returns:
            Dict: 操作结果
        """
        try:
            if word_ids:
                # 清除指定单词的免复习期
                placeholders = ','.join(['?' for _ in word_ids])
                query = f"""
                UPDATE user_word 
                SET skip_review_until = NULL, updated_at = CURRENT_TIMESTAMP
                WHERE user_id = ? AND word_id IN ({placeholders})
                """
                params = [user_id] + word_ids
            else:
                # 清除用户所有单词的免复习期
                query = """
                UPDATE user_word 
                SET skip_review_until = NULL, updated_at = CURRENT_TIMESTAMP
                WHERE user_id = ? AND skip_review_until IS NOT NULL
                """
                params = [user_id]
            
            affected_rows = db.execute_update(query, params)
            
            if affected_rows > 0:
                logger.info(f"清除免复习成功: user_id={user_id}, "
                           f"affected_count={affected_rows}")
                
                return {
                    'success': True,
                    'message': f'成功清除{affected_rows}个单词的免复习设置',
                    'affected_count': affected_rows
                }
            else:
                return {
                    'success': False,
                    'message': '没有找到需要清除的免复习设置'
                }
                
        except Exception as e:
            logger.error(f"清除免复习失败: user_id={user_id}, word_ids={word_ids}, error={e}")
            return {
                'success': False,
                'message': f'清除失败: {str(e)}'
            }
    
    @staticmethod
    def get_skip_review_statistics(user_id: int) -> Dict[str, Any]:
        """
        获取用户的免复习统计信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict: 统计信息
        """
        try:
            query = """
            SELECT 
                COUNT(*) as total_words,
                COUNT(skip_review_until) as skip_review_words,
                COUNT(CASE WHEN skip_review_until > date('now') THEN 1 END) as active_skip_words,
                MIN(skip_review_until) as earliest_expiry,
                MAX(skip_review_until) as latest_expiry
            FROM user_word
            WHERE user_id = ?
            """
            
            result = db.execute_query(query, (user_id,))
            
            if result:
                row = result[0]
                return {
                    'total_words': row['total_words'] or 0,
                    'skip_review_words': row['skip_review_words'] or 0,
                    'active_skip_words': row['active_skip_words'] or 0,
                    'earliest_expiry': row['earliest_expiry'],
                    'latest_expiry': row['latest_expiry']
                }
            else:
                return {
                    'total_words': 0,
                    'skip_review_words': 0,
                    'active_skip_words': 0,
                    'earliest_expiry': None,
                    'latest_expiry': None
                }
                
        except Exception as e:
            logger.error(f"获取免复习统计失败: user_id={user_id}, error={e}")
            return {
                'total_words': 0,
                'skip_review_words': 0,
                'active_skip_words': 0,
                'earliest_expiry': None,
                'latest_expiry': None
            }