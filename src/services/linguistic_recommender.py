"""
语言学专家推荐服务
基于专家语言学标注提供准确的相似单词推荐，解决传统字符串匹配的误导问题
"""

import sqlite3
import json
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from src.core import get_logger
from .pattern.recommendation_config import recommendation_config_manager

logger = get_logger(__name__)


@dataclass
class LinguisticSimilarWord:
    """基于语言学的相似单词"""
    word_id: int
    english_word: str
    chinese_meaning: str
    similarity_reason: str
    similarity_type: str  # 'prefix', 'semantic', 'etymology', 'morphology'
    confidence: float
    proficiency: float
    learning_status: str


@dataclass
class LinguisticRecommendation:
    """语言学推荐结果"""
    category: str
    category_name: str
    explanation: str
    linguistic_principle: str
    similar_words: List[LinguisticSimilarWord]
    educational_value: str


class LinguisticRecommenderService:
    """基于语言学专家标注的推荐服务"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self._max_recommendations_per_category = 5
        self._min_words_per_category = 2
    
    def get_linguistic_recommendations(self, user_id: int, word_id: int, 
                                     context: str = 'learning') -> List[LinguisticRecommendation]:
        """获取基于语言学专家标注的推荐
        
        Args:
            user_id: 用户ID
            word_id: 目标单词ID
            context: 使用场景 'learning'(学习) 或 'testing'(答题)
            
        Returns:
            按语言学价值排序的推荐列表
        """
        try:
            # 获取目标单词的语言学标注
            target_annotation = self._get_word_annotation(word_id)
            if not target_annotation:
                logger.info(f"单词{word_id}没有语言学标注")
                return []
            
            # 获取用户已学词汇
            user_words = self._get_user_learned_words(user_id)
            if not user_words:
                logger.info(f"用户{user_id}没有已学词汇")
                return []
            
            recommendations = []
            
            # 1. 真前缀推荐（最高优先级）
            if target_annotation.get('has_true_prefix'):
                prefix_rec = self._get_prefix_recommendations(
                    target_annotation, user_words, word_id
                )
                if prefix_rec:
                    recommendations.append(prefix_rec)
            
            # 2. 语义组推荐已移除 - 不再提供语义分类推荐

            # 3. 学习集群推荐已移除 - 不再提供主题学习法推荐

            # 4. 后缀推荐
            suffix_rec = self._get_suffix_recommendations(
                target_annotation, user_words, word_id
            )
            if suffix_rec:
                recommendations.append(suffix_rec)

            # 5. 反义词/对比推荐
            contrast_recs = self._get_contrast_recommendations(
                target_annotation, user_words, word_id
            )
            recommendations.extend(contrast_recs)
            
            # 按教育价值排序并限制数量
            recommendations = self._rank_by_educational_value(recommendations, user_id)
            config = recommendation_config_manager.get_user_config(user_id)
            max_recs = config.get_max_recommendations(context)
            return recommendations[:max_recs]
            
        except Exception as e:
            logger.error(f"获取语言学推荐失败: {e}")
            return []
    
    def _get_word_annotation(self, word_id: int) -> Optional[Dict]:
        """获取单词的语言学标注"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT wla.*, w.english_word, w.chinese_meaning
                FROM word_linguistic_annotations wla
                JOIN word w ON wla.word_id = w.id
                WHERE wla.word_id = ?
            """, (word_id,))
            
            row = cursor.fetchone()
            conn.close()
            
            if not row:
                return None
            
            columns = [desc[0] for desc in cursor.description]
            annotation = dict(zip(columns, row))
            
            # 解析JSON字段
            for json_field in ['linguistic_tags', 'semantic_groups', 'learning_clusters']:
                if annotation.get(json_field):
                    try:
                        annotation[json_field] = json.loads(annotation[json_field])
                    except json.JSONDecodeError:
                        annotation[json_field] = []
            
            return annotation
            
        except Exception as e:
            logger.error(f"获取单词标注失败: {e}")
            return None
    
    def _get_user_learned_words(self, user_id: int) -> List[Dict]:
        """获取用户已学词汇及其标注"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT w.id, w.english_word, w.chinese_meaning,
                       uw.status, uw.proficiency,
                       wla.linguistic_tags, wla.semantic_groups,
                       wla.learning_clusters, wla.has_true_prefix, wla.prefix, wla.suffix
                FROM user_word uw
                JOIN word w ON uw.word_id = w.id
                LEFT JOIN word_linguistic_annotations wla ON w.id = wla.word_id
                WHERE uw.user_id = ? AND uw.status != 'new'
            """, (user_id,))
            
            words = []
            for row in cursor.fetchall():
                word_info = {
                    'word_id': row[0],
                    'english_word': row[1],
                    'chinese_meaning': row[2],
                    'status': row[3],
                    'proficiency': row[4] or 0.0,
                    'has_annotation': row[5] is not None
                }
                
                # 解析JSON字段
                if row[5]:  # 有标注
                    try:
                        word_info['linguistic_tags'] = json.loads(row[5]) if row[5] else []
                        word_info['semantic_groups'] = json.loads(row[6]) if row[6] else []
                        word_info['learning_clusters'] = []  # 学习集群已移除
                        word_info['has_true_prefix'] = bool(row[8])
                        word_info['prefix'] = row[9]
                        word_info['suffix'] = row[10]
                    except json.JSONDecodeError:
                        word_info['linguistic_tags'] = []
                        word_info['semantic_groups'] = []
                        word_info['learning_clusters'] = []
                        word_info['suffix'] = None
                
                words.append(word_info)
            
            conn.close()
            return words
            
        except Exception as e:
            logger.error(f"获取用户已学词汇失败: {e}")
            return []
    
    def _get_prefix_recommendations(self, target_annotation: Dict, 
                                  user_words: List[Dict], 
                                  exclude_word_id: int) -> Optional[LinguisticRecommendation]:
        """获取真前缀推荐"""
        target_prefix = target_annotation.get('prefix')
        if not target_prefix:
            return None
        
        prefix_words = []
        for word in user_words:
            if (word.get('has_annotation') and 
                word.get('has_true_prefix') and 
                word.get('prefix') == target_prefix and
                word['word_id'] != exclude_word_id):
                
                similar_word = LinguisticSimilarWord(
                    word_id=word['word_id'],
                    english_word=word['english_word'],
                    chinese_meaning=word['chinese_meaning'],
                    similarity_reason=f'都含有{target_prefix}前缀',
                    similarity_type='prefix',
                    confidence=0.95,
                    proficiency=word['proficiency'],
                    learning_status=word['status']
                )
                prefix_words.append(similar_word)
        
        if len(prefix_words) < self._min_words_per_category:
            return None
        
        # 前缀的教育价值说明
        prefix_explanations = {
            'un-': 'un-前缀表示否定，是英语中最常用的否定前缀。掌握这个规律后，遇到新的un-单词就能猜出意思！',
            're-': 're-前缀表示\"重新、再次\"，学会后能理解很多表示重复动作的词汇。',
            'pre-': 'pre-前缀表示\"预先、在...之前\"，多用于正式词汇中。',
            'dis-': 'dis-前缀表示\"不、分离\"，是构成反义词的重要方法。',
            'mis-': 'mis-前缀表示\"错误、不当\"，掌握后能精确理解词汇含义。',
            'over-': 'over-前缀表示\"超过、过度\"，常用于描述过量的概念。',
            'under-': 'under-前缀表示\"不足、在...下\"，与over-形成对比。',
            'uni-': 'uni-前缀表示\"一、统一\"，来自拉丁语，多见于学术词汇。'
        }
        
        explanation = prefix_explanations.get(
            target_prefix, 
            f'{target_prefix}前缀有固定的语法意义，掌握词缀规律是高效词汇学习的关键'
        )
        
        return LinguisticRecommendation(
            category='true_prefix',
            category_name=f'{target_prefix}前缀词汇',
            explanation=explanation,
            linguistic_principle=f'词缀学习法：{target_prefix}前缀的语法功能',
            similar_words=prefix_words[:self._max_recommendations_per_category],
            educational_value='高价值：掌握前缀规律能举一反三'
        )
    
    # 语义组推荐方法已移除 - 不再提供语义分类功能
    
    # 学习集群推荐方法已移除 - 不再提供主题学习法推荐

    def _get_suffix_recommendations(self, target_annotation: Dict,
                                  user_words: List[Dict],
                                  exclude_word_id: int) -> Optional[LinguisticRecommendation]:
        """获取后缀推荐"""
        target_suffix = target_annotation.get('suffix')
        if not target_suffix:
            return None

        suffix_words = []
        for word in user_words:
            if (word.get('has_annotation') and
                word.get('suffix') == target_suffix and
                word['word_id'] != exclude_word_id):

                similar_word = LinguisticSimilarWord(
                    word_id=word['word_id'],
                    english_word=word['english_word'],
                    chinese_meaning=word['chinese_meaning'],
                    similarity_reason=f'都有{target_suffix}后缀',
                    similarity_type='suffix',
                    confidence=0.85,
                    proficiency=word['proficiency'],
                    learning_status=word['status']
                )
                suffix_words.append(similar_word)

        if len(suffix_words) < self._min_words_per_category:
            return None

        # 后缀的教育价值说明
        suffix_explanations = {
            '-y': '形容词后缀-y表示"具有...性质的"，是最常见的形容词构成方式。',
            '-ly': '副词后缀-ly表示"以...方式"，将形容词转换为副词。',
            '-ing': '现在分词后缀-ing表示"正在进行的"或"令人...的"。',
            '-ed': '过去分词后缀-ed表示"已完成的"或"感到...的"。',
            '-er': '比较级后缀-er表示"更..."，用于形容词和副词的比较。',
            '-est': '最高级后缀-est表示"最..."，用于形容词和副词的最高级。'
        }

        explanation = suffix_explanations.get(target_suffix,
                                            f'{target_suffix}后缀词汇有共同的语法特征，一起学习能提高语法敏感度。')

        return LinguisticRecommendation(
            category='suffix',
            category_name=f'{target_suffix}后缀词汇',
            explanation=explanation,
            linguistic_principle='词缀学习法：相同后缀的词汇有相似的语法功能',
            similar_words=suffix_words[:self._max_recommendations_per_category],
            educational_value='高价值：语法规律掌握'
        )

    def _get_contrast_recommendations(self, target_annotation: Dict,
                                    user_words: List[Dict], 
                                    exclude_word_id: int) -> List[LinguisticRecommendation]:
        """获取对比词推荐（反义词等）"""
        target_tags = target_annotation.get('linguistic_tags', [])
        
        # 寻找反义词对
        contrast_pairs = {
            'happy': ['sad', 'unhappy'],
            'good': ['bad'],
            'big': ['small', 'little'],
            'hot': ['cold'],
            'fast': ['slow'],
            'new': ['old'],
            'young': ['old'],
            'long': ['short'],
            'black': ['white'],
            'run': ['walk']
        }
        
        target_word = target_annotation.get('english_word', '').lower()
        
        recommendations = []
        
        # 直接查找反义词
        if target_word in contrast_pairs:
            contrast_words = []
            for word in user_words:
                if (word['english_word'].lower() in contrast_pairs[target_word] and
                    word['word_id'] != exclude_word_id):
                    
                    similar_word = LinguisticSimilarWord(
                        word_id=word['word_id'],
                        english_word=word['english_word'],
                        chinese_meaning=word['chinese_meaning'],
                        similarity_reason=f'与{target_word}构成对比概念',
                        similarity_type='contrast',
                        confidence=0.90,
                        proficiency=word['proficiency'],
                        learning_status=word['status']
                    )
                    contrast_words.append(similar_word)
            
            if contrast_words:
                recommendation = LinguisticRecommendation(
                    category='contrast',
                    category_name='对比概念词汇',
                    explanation=f'对比学习法是最有效的记忆方法之一。通过对比{target_word}与其反义词，能加深对两个概念的理解。',
                    linguistic_principle='对比学习法：反义词对比记忆',
                    similar_words=contrast_words,
                    educational_value='高价值：对比记忆加深理解'
                )
                recommendations.append(recommendation)
        
        return recommendations
    
    # 语义组信息方法已移除 - 不再需要语义组相关信息
    
    # 学习集群信息方法已移除 - 不再需要学习集群相关信息
    
    def _rank_by_educational_value(self, recommendations: List[LinguisticRecommendation], user_id: int) -> List[LinguisticRecommendation]:
        """按教育价值对推荐进行排序 - 优化版：降低语义推荐权重"""
        
        # 获取用户配置并应用权重调整
        config = recommendation_config_manager.get_user_config(user_id)
        
        # 基础优先级权重 - 语音优先策略
        base_priority_weights = {
            # 语音相关 - 最高优先级（直接影响拼写和发音）
            'silent_letter': 100,     # 不发音字母规律 - 最重要
            'phonetic': 95,          # 发音规律（双元音等）
            'rhyme': 90,             # 押韵模式（有意义的词缀）
            'syllable': 88,          # 音节结构（开音节等）
            
            # 词缀相关 - 次优先级（有助于词汇扩展）
            'true_prefix': 85,       # 真前缀 - 构词规律
            'suffix': 80,            # 后缀 - 词性变化
            'morphology': 75,        # 词根词缀
            'root': 73,              # 词根
            
            # 语法相关 - 中等优先级
            'grammar': 70,           # 语法变形
            'plural_forms': 68,      # 复数形式
            'adjective_forms': 65,   # 形容词变化
            
            # 对比学习 - 保留但降权
            'contrast': 60,          # 对比概念

            # 语义推荐已移除 - 不再包含语义相关权重
            # 学习集群推荐已移除 - 不再包含主题学习法权重
        }
        
        # 应用用户配置的权重调整
        config = recommendation_config_manager.get_user_config(user_id)
        priority_weights = config.apply_weights(base_priority_weights)
        
        def get_priority(rec: LinguisticRecommendation) -> int:
            return priority_weights.get(rec.category, 50)
        
        # 按优先级排序，同优先级按相似词数量排序
        recommendations.sort(
            key=lambda r: (get_priority(r), len(r.similar_words)), 
            reverse=True
        )
        
        return recommendations
    
    def get_linguistic_statistics(self) -> Dict:
        """获取语言学标注统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 基础统计
            cursor.execute("SELECT COUNT(*) FROM word_linguistic_annotations")
            total_annotated = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM word")
            total_words = cursor.fetchone()[0]
            
            # 前缀统计
            cursor.execute("""
                SELECT prefix, COUNT(*) as count
                FROM word_linguistic_annotations 
                WHERE has_true_prefix = 1 AND prefix IS NOT NULL
                GROUP BY prefix
                ORDER BY count DESC
            """)
            prefix_stats = cursor.fetchall()
            
            # 语义组统计已移除

            conn.close()

            return {
                'total_words': total_words,
                'total_annotated': total_annotated,
                'annotation_rate': round(total_annotated / total_words * 100, 1),
                'prefix_distribution': prefix_stats
            }
            
        except Exception as e:
            logger.error(f"获取语言学统计失败: {e}")
            return {}