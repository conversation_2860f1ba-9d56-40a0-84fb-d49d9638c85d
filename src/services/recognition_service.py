"""
Recognition学习模式服务
负责生成选择题、验证答案、记录学习数据
"""
import random
from typing import Dict, Any, List
from datetime import date

from ..models.word import Word
from ..models.planning import LearningPlan
from ..models.base import db
from ..core import get_logger
from .learning.learning_service import LearningService

logger = get_logger(__name__)


class RecognitionService:
    """Recognition模式学习服务"""
    
    @staticmethod
    def generate_question(word_id: int, question_type: str = 'cn_to_en') -> Dict[str, Any]:
        """
        生成Recognition模式的选择题（支持多选）
        
        Args:
            word_id: 单词ID
            question_type: 题目类型 ('cn_to_en' 或 'en_to_cn')
            
        Returns:
            Dict[str, Any]: 选择题数据
        """
        try:
            # 获取目标单词
            target_word = Word.get_by_id(word_id)
            if not target_word:
                raise ValueError(f"单词不存在: {word_id}")
            
            # 🚀 新增：检查是否有同一单词的多个含义（重复单词检测）
            multiple_meanings = RecognitionService._get_multiple_meanings(target_word['english_word'])
            
            # 生成干扰项
            if len(multiple_meanings) > 1:
                # 有多个含义时，生成多选题
                logger.info(f"🎯 检测到重复单词，生成多选题: {target_word['english_word']}")
                return RecognitionService._generate_multiple_choice_question(
                    target_word, multiple_meanings, question_type
                )
            else:
                # 单一含义，生成普通单选题
                return RecognitionService._generate_single_choice_question(
                    target_word, question_type
                )
            
        except Exception as e:
            logger.error(f"❌ 生成Recognition题目失败: {e}")
            raise
    
    @staticmethod
    def _get_multiple_meanings(english_word: str) -> List[dict]:
        """
        获取同一英文单词的所有含义
        
        Args:
            english_word: 英文单词
            
        Returns:
            List[dict]: 该单词的所有含义记录
        """
        try:
            query = """
            SELECT id, english_word, chinese_meaning, learning_requirement 
            FROM word 
            WHERE english_word = ?
            ORDER BY id
            """
            results = db.execute_query(query, (english_word,))
            return [dict(row) for row in results]
        except Exception as e:
            logger.error(f"❌ 获取多重含义失败: {e}")
            return []
    
    @staticmethod
    def _generate_multiple_choice_question(target_word: dict, all_meanings: List[dict], 
                                         question_type: str) -> Dict[str, Any]:
        """
        生成多选题（当同一单词有多个含义时）
        
        Args:
            target_word: 目标单词
            all_meanings: 该单词的所有含义
            question_type: 题目类型
            
        Returns:
            Dict[str, Any]: 多选题数据
        """
        try:
            if question_type == 'cn_to_en':
                # 中译英多选题：显示中文，选择对应的英文（通常只有一个）
                question = target_word['chinese_meaning']
                correct_answers = [target_word['english_word']]  # 只有当前含义对应的英文
                
                # 添加其他英文单词作为干扰项
                distractors = RecognitionService._generate_distractors(target_word, question_type, count=3)
                options = correct_answers + distractors
                
            else:  # en_to_cn 
                # 英译中多选题：显示英文，选择所有可能的中文含义
                question = target_word['english_word']
                correct_answers = [meaning['chinese_meaning'] for meaning in all_meanings]
                
                # 添加其他中文含义作为干扰项  
                distractors = RecognitionService._generate_distractors(target_word, question_type, count=2)
                options = correct_answers + distractors
            
            # 随机打乱选项顺序
            random.shuffle(options)
            
            logger.info(f"✅ 生成多选题: {question} -> {correct_answers}")
            
            return {
                'word_id': target_word['id'],
                'question': question,
                'question_type': question_type,
                'options': options,
                'correct_answers': correct_answers,  # 🚀 新增：多个正确答案
                'is_multiple_choice': True,  # 🚀 新增：标识为多选题
                'total_meanings': len(all_meanings)  # 🚀 新增：总含义数
            }
            
        except Exception as e:
            logger.error(f"❌ 生成多选题失败: {e}")
            raise
    
    @staticmethod 
    def _generate_single_choice_question(target_word: dict, question_type: str) -> Dict[str, Any]:
        """
        生成普通单选题
        
        Args:
            target_word: 目标单词
            question_type: 题目类型
            
        Returns:
            Dict[str, Any]: 单选题数据
        """
        # 生成干扰项
        distractors = RecognitionService._generate_distractors(
            target_word, question_type, count=3
        )
        
        # 组合选项（正确答案 + 干扰项）
        if question_type == 'cn_to_en':
            question = target_word['chinese_meaning']
            correct_answer = target_word['english_word']
            options = [correct_answer] + distractors
        else:  # en_to_cn
            question = target_word['english_word']
            correct_answer = target_word['chinese_meaning']
            options = [correct_answer] + distractors
        
        # 随机打乱选项顺序
        random.shuffle(options)
        
        logger.info(f"✅ 生成单选题: {question} ({question_type})")
        
        return {
            'word_id': target_word['id'],
            'question': question,
            'question_type': question_type,
            'options': options,
            'correct_answers': [correct_answer],  # 🚀 统一格式：使用数组
            'is_multiple_choice': False,  # 🚀 新增：标识为单选题
            'total_meanings': 1
        }
    
    @staticmethod
    def _generate_distractors(target_word: dict, question_type: str, count: int = 3) -> List[str]:
        """
        生成智能干扰项
        
        Args:
            target_word: 目标单词数据
            question_type: 题目类型
            count: 干扰项数量
            
        Returns:
            List[str]: 干扰项列表
        """
        distractors = []
        
        if question_type == 'cn_to_en':
            # 中译英：生成英文干扰项
            distractors = RecognitionService._get_english_distractors(target_word, count)
        else:
            # 英译中：生成中文干扰项
            distractors = RecognitionService._get_chinese_distractors(target_word, count)
        
        # 如果干扰项不够，用随机单词补充
        if len(distractors) < count:
            additional = RecognitionService._get_random_distractors(
                target_word, question_type, count - len(distractors)
            )
            distractors.extend(additional)
        
        return distractors[:count]
    
    @staticmethod
    def _get_english_distractors(target_word: dict, count: int) -> List[str]:
        """获取英文干扰项（同词性、相似长度）"""
        target_english = target_word['english_word']
        target_length = len(target_english)
        
        # 查询策略：优先选择相似特征的单词
        query = """
        SELECT DISTINCT english_word FROM word 
        WHERE english_word != ?
        AND ABS(LENGTH(english_word) - ?) <= 2
        ORDER BY RANDOM()
        LIMIT ?
        """
        
        results = db.execute_query(query, (target_english, target_length, count * 2))
        distractors = [row['english_word'] for row in results]
        
        # 进一步筛选：避免过于相似的单词
        filtered_distractors = []
        for distractor in distractors:
            if len(filtered_distractors) >= count:
                break
            
            # 避免首字母相同且长度相同的单词（太相似）
            if (distractor[0].lower() == target_english[0].lower() and 
                len(distractor) == len(target_english)):
                continue
                
            filtered_distractors.append(distractor)
        
        return filtered_distractors
    
    @staticmethod
    def _get_chinese_distractors(target_word: dict, count: int) -> List[str]:
        """获取中文干扰项（相似语义领域）"""
        target_chinese = target_word['chinese_meaning']
        target_section = target_word['section'] if target_word['section'] else ''
        
        # 优先从同章节选择干扰项
        query = """
        SELECT DISTINCT chinese_meaning FROM word 
        WHERE chinese_meaning != ?
        AND section = ?
        ORDER BY RANDOM()
        LIMIT ?
        """
        
        results = db.execute_query(query, (target_chinese, target_section, count))
        distractors = [row['chinese_meaning'] for row in results]
        
        # 如果同章节干扰项不够，从其他章节补充
        if len(distractors) < count:
            additional_query = """
            SELECT DISTINCT chinese_meaning FROM word 
            WHERE chinese_meaning != ?
            AND section != ?
            ORDER BY RANDOM()
            LIMIT ?
            """
            additional_results = db.execute_query(
                additional_query, 
                (target_chinese, target_section, count - len(distractors))
            )
            distractors.extend([row['chinese_meaning'] for row in additional_results])
        
        return distractors
    
    @staticmethod
    def _get_random_distractors(target_word: dict, question_type: str, count: int) -> List[str]:
        """获取随机干扰项（兜底策略）"""
        if question_type == 'cn_to_en':
            field = 'english_word'
            target_value = target_word['english_word']
        else:
            field = 'chinese_meaning'
            target_value = target_word['chinese_meaning']
        
        query = f"""
        SELECT DISTINCT {field} FROM word 
        WHERE {field} != ?
        ORDER BY RANDOM()
        LIMIT ?
        """
        
        results = db.execute_query(query, (target_value, count))
        return [row[field] for row in results]
    
    @staticmethod
    def submit_answer(user_id: int, word_id: int, selected_options: List[str], 
                     correct_answers: List[str], duration_seconds: float, 
                     question_type: str, is_multiple_choice: bool = False, 
                     plan_date: date = None) -> Dict[str, Any]:
        """
        提交Recognition模式答案（支持多选）
        
        Args:
            user_id: 用户ID
            word_id: 单词ID
            selected_options: 选择的选项列表
            correct_answers: 正确答案列表
            duration_seconds: 答题用时
            question_type: 题目类型
            is_multiple_choice: 是否为多选题
            plan_date: 计划日期
            
        Returns:
            Dict[str, Any]: 提交结果
        """
        if plan_date is None:
            plan_date = date.today()
        
        try:
            # 🚀 验证答案（支持多选）
            is_correct = RecognitionService._validate_multiple_choice_answer(
                selected_options, correct_answers, is_multiple_choice
            )
            
            # 获取单词信息
            word = Word.get_by_id(word_id)
            if not word:
                return {'success': False, 'message': '单词不存在'}
            
            # 获取当前星级
            plan_data = LearningPlan.get_word_with_current_star(user_id, word_id, plan_date)
            if not plan_data:
                return {'success': False, 'message': '学习计划不存在'}
            
            current_star = plan_data['star_level'] or 3
            
            # 🚀 多选题星级计算逻辑
            if is_multiple_choice:
                # 多选题：部分正确有部分奖励，全对才加星
                correct_ratio = len(set(selected_options) & set(correct_answers)) / max(len(correct_answers), 1)
                
                if correct_ratio >= 1.0:  # 全对
                    new_star = min(5, current_star + 1)
                    points_change = 15  # 多选题全对额外奖励
                elif correct_ratio >= 0.5:  # 半对
                    new_star = current_star  # 保持不变
                    points_change = 5   # 部分正确奖励
                else:  # 错误过多
                    new_star = max(1, current_star - 1)
                    points_change = -10
            else:
                # 单选题：原有逻辑
                if is_correct:
                    new_star = min(5, current_star + 1)
                    points_change = 10
                else:
                    new_star = max(1, current_star - 2)
                    points_change = -20
            
            # 记录学习数据
            selected_option_str = ','.join(selected_options) if len(selected_options) > 1 else selected_options[0] if selected_options else ''
            LearningService._batch_update_learning_data(
                user_id, word_id, plan_date, duration_seconds,
                selected_option_str, word['english_word'], is_correct, new_star, 
                points_change
            )
            
            logger.info(f"✅ Recognition答案提交成功: 用户{user_id}, 单词{word_id}, "
                       f"{'正确' if is_correct else '错误'}, 多选:{is_multiple_choice}, "
                       f"星级{current_star}→{new_star}, 积分{points_change:+d}")
            
            return {
                'success': True,
                'data': {
                    'is_correct': is_correct,
                    'new_star_level': new_star,
                    'points_change': points_change,
                    'question_type': question_type,
                    'is_multiple_choice': is_multiple_choice,
                    'selected_options': selected_options,
                    'correct_answers': correct_answers
                }
            }
            
        except Exception as e:
            logger.error(f"❌ Recognition答案提交失败: {e}")
            # 🔧 确保错误情况下也返回标准格式，避免前端数据校验失败
            return {
                'success': False, 
                'message': str(e),
                'data': {
                    'is_correct': False,  # 🔧 确保is_correct字段存在且为boolean类型
                    'new_star_level': None,
                    'points_change': 0,
                    'question_type': question_type,
                    'is_multiple_choice': is_multiple_choice,
                    'selected_options': selected_options,
                    'correct_answers': correct_answers,
                    'error': True  # 标记这是错误情况
                }
            }
    
    @staticmethod
    def _validate_multiple_choice_answer(selected_options: List[str], 
                                       correct_answers: List[str], 
                                       is_multiple_choice: bool) -> bool:
        """
        验证多选题答案
        
        Args:
            selected_options: 用户选择的选项
            correct_answers: 正确答案列表
            is_multiple_choice: 是否为多选题
            
        Returns:
            bool: 是否完全正确
        """
        if not is_multiple_choice:
            # 单选题：只要选中的包含在正确答案中即可，忽略大小写
            if len(selected_options) != 1:
                return False
            selected_option_lower = selected_options[0].lower()
            correct_answers_lower = [answer.lower() for answer in correct_answers]
            return selected_option_lower in correct_answers_lower
        
        # 多选题：必须选中所有正确答案且不能有错误选项，忽略大小写
        selected_set = set(option.lower() for option in selected_options)
        correct_set = set(answer.lower() for answer in correct_answers)
        
        return selected_set == correct_set
    
    @staticmethod
    def get_recognition_words_count() -> Dict[str, int]:
        """
        获取Recognition类型单词的统计信息
        
        Returns:
            Dict[str, int]: 统计数据
        """
        try:
            query = """
            SELECT 
                COUNT(*) as total_recognition_words,
                SUM(CASE WHEN section LIKE '%小学%' THEN 1 ELSE 0 END) as primary_words,
                SUM(CASE WHEN section LIKE '%中学%' THEN 1 ELSE 0 END) as middle_words
            FROM word 
            WHERE learning_requirement = 'recognition'
            """
            
            results = db.execute_query(query)
            if results:
                row = results[0]
                return {
                    'total_recognition_words': row['total_recognition_words'] or 0,
                    'primary_words': row['primary_words'] or 0,
                    'middle_words': row['middle_words'] or 0,
                    'spelling_words': RecognitionService._get_spelling_words_count()
                }
            
            return {'total_recognition_words': 0, 'primary_words': 0, 'middle_words': 0, 'spelling_words': 0}
            
        except Exception as e:
            logger.error(f"❌ 获取Recognition统计失败: {e}")
            return {'total_recognition_words': 0, 'primary_words': 0, 'middle_words': 0, 'spelling_words': 0}
    
    @staticmethod
    def _get_spelling_words_count() -> int:
        """获取Spelling类型单词数量"""
        try:
            query = "SELECT COUNT(*) as count FROM word WHERE learning_requirement = 'spelling'"
            results = db.execute_query(query)
            return results[0]['count'] if results else 0
        except:
            return 0