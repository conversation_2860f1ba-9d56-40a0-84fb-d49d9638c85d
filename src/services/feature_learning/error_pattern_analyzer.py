"""
错误模式分析器
提供详细的错误分析和具体的改进建议
"""
from typing import Dict, List, Any, Tuple
from collections import Counter, defaultdict
import re
from ...models.base import db
from ...core import get_logger

logger = get_logger(__name__)


class ErrorPatternAnalyzer:
    """错误模式分析器"""
    
    @staticmethod
    def get_comprehensive_error_analysis(user_id: int) -> Dict[str, Any]:
        """
        获取全面的错误分析
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 详细的错误分析结果
        """
        try:
            # 获取用户的错误记录
            error_records = ErrorPatternAnalyzer._get_user_error_records(user_id)
            
            if not error_records:
                return {
                    'total_errors': 0,
                    'error_types': {},
                    'character_errors': {},
                    'position_errors': {},
                    'improvement_suggestions': [],
                    'practice_recommendations': []
                }
            
            # 分析错误类型分布
            error_type_analysis = ErrorPatternAnalyzer._analyze_error_types(error_records)
            
            # 分析字符级错误
            character_error_analysis = ErrorPatternAnalyzer._analyze_character_errors(error_records)
            
            # 分析位置错误模式
            position_error_analysis = ErrorPatternAnalyzer._analyze_position_errors(error_records)
            
            # 分析重复错误的单词
            repeat_error_analysis = ErrorPatternAnalyzer._analyze_repeat_errors(error_records)
            
            # 生成改进建议
            improvement_suggestions = ErrorPatternAnalyzer._generate_improvement_suggestions(
                error_type_analysis, character_error_analysis, position_error_analysis
            )
            
            # 生成练习建议
            practice_recommendations = ErrorPatternAnalyzer._generate_practice_recommendations(
                error_type_analysis, repeat_error_analysis
            )
            
            return {
                'total_errors': len(error_records),
                'error_types': error_type_analysis,
                'character_errors': character_error_analysis,
                'position_errors': position_error_analysis,
                'repeat_errors': repeat_error_analysis,
                'improvement_suggestions': improvement_suggestions,
                'practice_recommendations': practice_recommendations
            }
            
        except Exception as e:
            logger.error("获取错误分析失败", user_id=user_id, error=str(e))
            return {'error': str(e)}
    
    @staticmethod
    def _get_user_error_records(user_id: int, limit: int = 200) -> List[Dict[str, Any]]:
        """获取用户的错误记录"""
        query = """
        SELECT 
            wr.user_input,
            wr.answer as correct_answer,
            wr.duration_seconds,
            wr.date,
            w.english_word,
            w.chinese_meaning,
            w.section
        FROM word_record wr
        JOIN word w ON wr.word_id = w.id
        WHERE wr.user_id = ? AND wr.is_correct = 0 AND wr.user_input IS NOT NULL
        ORDER BY wr.date DESC
        LIMIT ?
        """
        
        results = db.execute_query(query, (user_id, limit))
        return [dict(row) for row in results]
    
    @staticmethod
    def _analyze_error_types(error_records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析错误类型分布"""
        error_types = Counter()
        total_errors = len(error_records)
        
        for record in error_records:
            user_input = (record['user_input'] or '').strip().lower()
            correct_answer = (record['correct_answer'] or '').strip().lower()
            
            # 分类错误类型
            error_type = ErrorPatternAnalyzer._classify_detailed_error_type(user_input, correct_answer)
            error_types[error_type] += 1
        
        # 计算百分比并排序
        error_distribution = {}
        for error_type, count in error_types.most_common():
            percentage = (count / total_errors) * 100
            error_distribution[error_type] = {
                'count': count,
                'percentage': round(percentage, 1),
                'description': ErrorPatternAnalyzer._get_error_type_description(error_type)
            }
        
        return error_distribution
    
    @staticmethod
    def _classify_detailed_error_type(user_input: str, correct_answer: str) -> str:
        """详细分类错误类型"""
        if not user_input:
            return 'empty_input'
        
        if user_input == correct_answer:
            return 'no_error'
        
        # 长度比较
        len_diff = len(user_input) - len(correct_answer)
        
        if len_diff < -2:
            return 'missing_letters'  # 缺少字母
        elif len_diff > 2:
            return 'extra_letters'    # 多余字母
        elif len_diff == -1:
            return 'one_letter_missing'
        elif len_diff == 1:
            return 'one_letter_extra'
        
        # 相同长度的错误
        if len(user_input) == len(correct_answer):
            # 检查是否是字母替换
            diff_count = sum(1 for a, b in zip(user_input, correct_answer) if a != b)
            if diff_count == 1:
                return 'single_letter_substitution'
            elif diff_count == 2:
                # 检查是否是相邻字母交换
                if ErrorPatternAnalyzer._is_adjacent_swap(user_input, correct_answer):
                    return 'adjacent_letter_swap'
                else:
                    return 'two_letter_substitution'
            elif diff_count > 2:
                return 'multiple_letter_substitution'
        
        # 检查是否是大小写错误
        if user_input.lower() == correct_answer.lower():
            return 'capitalization_error'
        
        # 检查相似度
        similarity = ErrorPatternAnalyzer._calculate_similarity(user_input, correct_answer)
        if similarity > 0.7:
            return 'similar_spelling'
        elif similarity > 0.3:
            return 'partial_match'
        else:
            return 'completely_different'
    
    @staticmethod
    def _is_adjacent_swap(user_input: str, correct_answer: str) -> bool:
        """检查是否是相邻字母交换"""
        if len(user_input) != len(correct_answer):
            return False
        
        for i in range(len(user_input) - 1):
            # 尝试交换相邻字母
            chars = list(user_input)
            chars[i], chars[i + 1] = chars[i + 1], chars[i]
            if ''.join(chars) == correct_answer:
                return True
        return False
    
    @staticmethod
    def _calculate_similarity(s1: str, s2: str) -> float:
        """计算字符串相似度"""
        if not s1 or not s2:
            return 0.0
        
        # 使用编辑距离计算相似度
        def edit_distance(a, b):
            m, n = len(a), len(b)
            dp = [[0] * (n + 1) for _ in range(m + 1)]
            
            for i in range(m + 1):
                dp[i][0] = i
            for j in range(n + 1):
                dp[0][j] = j
            
            for i in range(1, m + 1):
                for j in range(1, n + 1):
                    if a[i-1] == b[j-1]:
                        dp[i][j] = dp[i-1][j-1]
                    else:
                        dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
            
            return dp[m][n]
        
        distance = edit_distance(s1, s2)
        max_len = max(len(s1), len(s2))
        return 1 - (distance / max_len) if max_len > 0 else 0
    
    @staticmethod
    def _analyze_character_errors(error_records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析字符级错误"""
        missing_chars = Counter()
        extra_chars = Counter()
        substitution_pairs = Counter()
        
        for record in error_records:
            user_input = (record['user_input'] or '').strip().lower()
            correct_answer = (record['correct_answer'] or '').strip().lower()
            
            # 分析缺失和多余的字符
            user_chars = Counter(user_input)
            correct_chars = Counter(correct_answer)
            
            for char, count in correct_chars.items():
                if char.isalpha():  # 只分析字母
                    user_count = user_chars.get(char, 0)
                    if user_count < count:
                        missing_chars[char] += count - user_count
            
            for char, count in user_chars.items():
                if char.isalpha():
                    correct_count = correct_chars.get(char, 0)
                    if count > correct_count:
                        extra_chars[char] += count - correct_count
            
            # 分析字母替换模式
            if len(user_input) == len(correct_answer):
                for u_char, c_char in zip(user_input, correct_answer):
                    if u_char != c_char and u_char.isalpha() and c_char.isalpha():
                        substitution_pairs[f"{c_char}→{u_char}"] += 1
        
        return {
            'most_missing_letters': dict(missing_chars.most_common(10)),
            'most_extra_letters': dict(extra_chars.most_common(10)),
            'common_substitutions': dict(substitution_pairs.most_common(10))
        }
    
    @staticmethod
    def _analyze_position_errors(error_records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析位置错误模式"""
        position_errors = {'beginning': 0, 'middle': 0, 'end': 0}
        total_position_errors = 0
        
        for record in error_records:
            user_input = (record['user_input'] or '').strip().lower()
            correct_answer = (record['correct_answer'] or '').strip().lower()
            
            if len(user_input) == len(correct_answer):
                for i, (u_char, c_char) in enumerate(zip(user_input, correct_answer)):
                    if u_char != c_char:
                        total_position_errors += 1
                        word_len = len(correct_answer)
                        
                        if i < word_len * 0.3:
                            position_errors['beginning'] += 1
                        elif i > word_len * 0.7:
                            position_errors['end'] += 1
                        else:
                            position_errors['middle'] += 1
        
        # 计算百分比
        if total_position_errors > 0:
            for position in position_errors:
                count = position_errors[position]
                position_errors[position] = {
                    'count': count,
                    'percentage': round((count / total_position_errors) * 100, 1)
                }
        
        return position_errors
    
    @staticmethod
    def _analyze_repeat_errors(error_records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析重复错误的单词"""
        word_errors = defaultdict(list)
        
        for record in error_records:
            word = record['english_word']
            user_input = record['user_input'] or ''
            word_errors[word].append(user_input)
        
        # 找出错误次数最多的单词
        repeat_errors = {}
        for word, errors in word_errors.items():
            if len(errors) >= 2:  # 至少错误2次
                repeat_errors[word] = {
                    'error_count': len(errors),
                    'recent_errors': errors[:5],  # 最近5次错误
                    'chinese_meaning': next((r['chinese_meaning'] for r in error_records 
                                           if r['english_word'] == word), '')
                }
        
        # 按错误次数排序
        sorted_repeat_errors = dict(sorted(repeat_errors.items(), 
                                         key=lambda x: x[1]['error_count'], 
                                         reverse=True)[:10])
        
        return sorted_repeat_errors
    
    @staticmethod
    def _get_error_type_description(error_type: str) -> str:
        """获取错误类型的描述"""
        descriptions = {
            'missing_letters': '缺少字母（漏写了多个字母）',
            'extra_letters': '多余字母（多写了字母）',
            'one_letter_missing': '缺少一个字母',
            'one_letter_extra': '多写一个字母',
            'single_letter_substitution': '单个字母写错',
            'two_letter_substitution': '两个字母写错',
            'multiple_letter_substitution': '多个字母写错',
            'adjacent_letter_swap': '相邻字母顺序颠倒',
            'capitalization_error': '大小写错误',
            'similar_spelling': '拼写相似但有错误',
            'partial_match': '部分正确',
            'completely_different': '完全不匹配',
            'empty_input': '未输入内容'
        }
        return descriptions.get(error_type, error_type)
    
    @staticmethod
    def _generate_improvement_suggestions(error_types: Dict, character_errors: Dict, 
                                        position_errors: Dict) -> List[Dict[str, str]]:
        """生成改进建议"""
        suggestions = []
        
        # 基于最常见的错误类型生成建议
        if error_types:
            most_common_error = max(error_types.items(), key=lambda x: x[1]['count'])
            error_type, error_data = most_common_error
            
            if error_type in ['missing_letters', 'one_letter_missing']:
                suggestions.append({
                    'type': 'spelling_strategy',
                    'title': '注意单词完整性',
                    'description': f'您经常漏写字母（{error_data["percentage"]}%的错误）。建议：拼写时逐个字母检查，确保每个字母都写完整。',
                    'action': '练习时放慢速度，逐字母拼写'
                })
            
            elif error_type in ['extra_letters', 'one_letter_extra']:
                suggestions.append({
                    'type': 'spelling_strategy',
                    'title': '避免多余字母',
                    'description': f'您经常多写字母（{error_data["percentage"]}%的错误）。建议：拼写前先在心中默念单词，避免重复字母。',
                    'action': '练习前先看清单词长度'
                })
            
            elif error_type == 'single_letter_substitution':
                suggestions.append({
                    'type': 'letter_accuracy',
                    'title': '提高字母准确性',
                    'description': f'您经常写错单个字母（{error_data["percentage"]}%的错误）。建议：重点记忆容易混淆的字母。',
                    'action': '加强字母辨识练习'
                })
        
        # 基于字符错误生成建议
        if character_errors.get('most_missing_letters'):
            most_missing = list(character_errors['most_missing_letters'].keys())[0]
            suggestions.append({
                'type': 'character_focus',
                'title': f'重点关注字母 "{most_missing}"',
                'description': f'您最容易漏写字母"{most_missing}"。建议：遇到包含此字母的单词时特别注意。',
                'action': f'练习包含字母"{most_missing}"的单词'
            })
        
        # 基于位置错误生成建议
        if position_errors:
            max_position = max(position_errors.items(), 
                             key=lambda x: x[1]['count'] if isinstance(x[1], dict) else 0)
            if isinstance(max_position[1], dict) and max_position[1]['count'] > 0:
                position_name = max_position[0]
                position_data = max_position[1]
                
                position_names = {
                    'beginning': '开头',
                    'middle': '中间',
                    'end': '结尾'
                }
                
                suggestions.append({
                    'type': 'position_focus',
                    'title': f'注意单词{position_names[position_name]}部分',
                    'description': f'您在单词{position_names[position_name]}部分错误最多（{position_data["percentage"]}%）。建议：特别关注这个位置的字母。',
                    'action': f'练习时重点检查单词{position_names[position_name]}'
                })
        
        return suggestions[:5]  # 最多返回5个建议
    
    @staticmethod
    def _generate_practice_recommendations(error_types: Dict, repeat_errors: Dict) -> List[Dict[str, str]]:
        """生成练习建议"""
        recommendations = []
        
        # 基于重复错误的单词生成建议
        if repeat_errors:
            top_error_words = list(repeat_errors.items())[:3]
            for word, data in top_error_words:
                recommendations.append({
                    'type': 'word_practice',
                    'title': f'重点练习单词: {word}',
                    'description': f'该单词您已错误{data["error_count"]}次，需要重点练习。',
                    'action': f'建议每天练习"{word}"3-5次，直到完全掌握',
                    'word': word,
                    'meaning': data['chinese_meaning']
                })
        
        # 基于错误类型生成练习建议
        if error_types:
            most_common_errors = sorted(error_types.items(), 
                                      key=lambda x: x[1]['count'], 
                                      reverse=True)[:2]
            
            for error_type, error_data in most_common_errors:
                if error_type == 'single_letter_substitution':
                    recommendations.append({
                        'type': 'skill_practice',
                        'title': '字母准确性训练',
                        'description': '建议进行字母辨识和书写练习，提高单个字母的准确性。',
                        'action': '每天练习10个容易混淆的字母组合'
                    })
                elif error_type in ['missing_letters', 'extra_letters']:
                    recommendations.append({
                        'type': 'skill_practice',
                        'title': '单词完整性训练',
                        'description': '建议练习完整拼写，注意单词的每个字母。',
                        'action': '拼写时逐字母检查，确保完整性'
                    })
        
        return recommendations[:5]  # 最多返回5个建议
