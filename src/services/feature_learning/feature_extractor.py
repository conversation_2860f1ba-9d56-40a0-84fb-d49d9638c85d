"""
特征提取器
从用户输入中提取各种类型的学习特征
"""
import re
import time
from typing import Dict, List, Any, Tuple
from datetime import datetime
from ...core import get_logger

logger = get_logger(__name__)


class FeatureExtractor:
    """特征提取器类"""
    
    @staticmethod
    def extract_all_features(user_input: str, correct_answer: str, 
                           duration_seconds: float, 
                           context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        提取所有类型的特征
        
        Args:
            user_input: 用户输入
            correct_answer: 正确答案
            duration_seconds: 答题用时
            context: 上下文信息（单词信息、学习时间等）
            
        Returns:
            Dict[str, Any]: 完整的特征向量
        """
        context = context or {}
        
        features = {
            'text_features': FeatureExtractor.extract_text_features(user_input, correct_answer),
            'time_features': FeatureExtractor.extract_time_features(duration_seconds, correct_answer),
            'behavioral_features': FeatureExtractor.extract_behavioral_features(user_input, correct_answer),
            'context_features': FeatureExtractor.extract_context_features(context),
            'extracted_at': datetime.now().isoformat(),
            'feature_version': '1.0'
        }
        
        logger.info("特征提取完成", 
                   user_input_length=len(user_input),
                   answer_length=len(correct_answer),
                   duration=duration_seconds)
        
        return features
    
    @staticmethod
    def extract_text_features(user_input: str, correct_answer: str) -> Dict[str, Any]:
        """
        提取文本相关特征
        
        Args:
            user_input: 用户输入
            correct_answer: 正确答案
            
        Returns:
            Dict[str, Any]: 文本特征
        """
        user_clean = user_input.strip().lower()
        answer_clean = correct_answer.strip().lower()
        
        # 基础特征
        length_diff = len(user_clean) - len(answer_clean)
        length_ratio = len(user_clean) / len(answer_clean) if len(answer_clean) > 0 else 0
        
        # 相似度特征
        edit_distance = FeatureExtractor._calculate_edit_distance(user_clean, answer_clean)
        similarity_score = FeatureExtractor._calculate_similarity(user_clean, answer_clean)
        
        # 字符级特征
        char_features = FeatureExtractor._analyze_character_differences(user_clean, answer_clean)
        
        # 单词级特征（对于短语）
        word_features = FeatureExtractor._analyze_word_differences(user_clean, answer_clean)
        
        return {
            'length_difference': length_diff,
            'length_ratio': length_ratio,
            'edit_distance': edit_distance,
            'similarity_score': similarity_score,
            'exact_match': user_clean == answer_clean,
            'case_sensitive_match': user_input.strip() == correct_answer.strip(),
            'character_analysis': char_features,
            'word_analysis': word_features,
            'common_prefixes': FeatureExtractor._find_common_prefix(user_clean, answer_clean),
            'common_suffixes': FeatureExtractor._find_common_suffix(user_clean, answer_clean)
        }
    
    @staticmethod
    def extract_time_features(duration_seconds: float, correct_answer: str) -> Dict[str, Any]:
        """
        提取时间相关特征
        
        Args:
            duration_seconds: 答题用时
            correct_answer: 正确答案（用于计算预期时间）
            
        Returns:
            Dict[str, Any]: 时间特征
        """
        # 基于单词长度的预期时间（经验值：每个字符0.3-0.5秒）
        expected_time_min = len(correct_answer) * 0.3
        expected_time_max = len(correct_answer) * 0.8
        
        # 输入速度（字符/秒）
        input_speed = len(correct_answer) / duration_seconds if duration_seconds > 0 else 0
        
        # 时间特征分析
        is_too_fast = duration_seconds < expected_time_min
        is_too_slow = duration_seconds > expected_time_max * 3
        is_hesitant = duration_seconds > expected_time_max * 2
        
        return {
            'duration_seconds': duration_seconds,
            'expected_time_range': [expected_time_min, expected_time_max],
            'input_speed_chars_per_sec': input_speed,
            'time_efficiency_ratio': expected_time_max / duration_seconds if duration_seconds > 0 else 0,
            'is_too_fast': is_too_fast,
            'is_too_slow': is_too_slow,
            'is_hesitant': is_hesitant,
            'time_category': FeatureExtractor._categorize_time(duration_seconds, expected_time_min, expected_time_max)
        }
    
    @staticmethod
    def extract_behavioral_features(user_input: str, correct_answer: str) -> Dict[str, Any]:
        """
        提取行为相关特征
        
        Args:
            user_input: 用户输入
            correct_answer: 正确答案
            
        Returns:
            Dict[str, Any]: 行为特征
        """
        user_clean = user_input.strip().lower()
        answer_clean = correct_answer.strip().lower()
        
        # 错误类型分析
        error_types = FeatureExtractor._classify_error_types(user_clean, answer_clean)
        
        # 输入模式分析
        input_patterns = FeatureExtractor._analyze_input_patterns(user_input)
        
        # 拼写策略分析
        spelling_strategy = FeatureExtractor._analyze_spelling_strategy(user_clean, answer_clean)
        
        return {
            'error_types': error_types,
            'input_patterns': input_patterns,
            'spelling_strategy': spelling_strategy,
            'has_multiple_words': ' ' in correct_answer.strip(),
            'capitalization_errors': FeatureExtractor._check_capitalization_errors(user_input, correct_answer),
            'punctuation_errors': FeatureExtractor._check_punctuation_errors(user_input, correct_answer)
        }
    
    @staticmethod
    def extract_context_features(context: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取上下文特征
        
        Args:
            context: 上下文信息
            
        Returns:
            Dict[str, Any]: 上下文特征
        """
        current_time = datetime.now()
        
        return {
            'word_length': context.get('word_length', 0),
            'word_difficulty': context.get('word_difficulty', 0),
            'time_of_day': current_time.hour,
            'day_of_week': current_time.weekday(),
            'session_position': context.get('session_position', 0),
            'previous_attempts': context.get('previous_attempts', 0),
            'word_frequency': context.get('word_frequency', 0),
            'learning_stage': context.get('learning_stage', 'unknown')
        }
    
    # 辅助方法
    @staticmethod
    def _calculate_edit_distance(str1: str, str2: str) -> int:
        """计算编辑距离（Levenshtein距离）"""
        if len(str1) == 0:
            return len(str2)
        if len(str2) == 0:
            return len(str1)
        
        # 创建距离矩阵
        matrix = [[0] * (len(str2) + 1) for _ in range(len(str1) + 1)]
        
        # 初始化第一行和第一列
        for i in range(len(str1) + 1):
            matrix[i][0] = i
        for j in range(len(str2) + 1):
            matrix[0][j] = j
        
        # 填充矩阵
        for i in range(1, len(str1) + 1):
            for j in range(1, len(str2) + 1):
                if str1[i-1] == str2[j-1]:
                    cost = 0
                else:
                    cost = 1
                
                matrix[i][j] = min(
                    matrix[i-1][j] + 1,      # 删除
                    matrix[i][j-1] + 1,      # 插入
                    matrix[i-1][j-1] + cost  # 替换
                )
        
        return matrix[len(str1)][len(str2)]
    
    @staticmethod
    def _calculate_similarity(str1: str, str2: str) -> float:
        """计算相似度分数（0-1）"""
        if not str1 and not str2:
            return 1.0
        if not str1 or not str2:
            return 0.0
        
        edit_dist = FeatureExtractor._calculate_edit_distance(str1, str2)
        max_len = max(len(str1), len(str2))
        
        return 1.0 - (edit_dist / max_len) if max_len > 0 else 0.0
    
    @staticmethod
    def _analyze_character_differences(user_input: str, correct_answer: str) -> Dict[str, Any]:
        """分析字符级差异"""
        if not user_input or not correct_answer:
            return {'missing': [], 'extra': [], 'substituted': []}
        
        # 简化的字符差异分析
        user_chars = set(user_input)
        answer_chars = set(correct_answer)
        
        missing_chars = list(answer_chars - user_chars)
        extra_chars = list(user_chars - answer_chars)
        
        return {
            'missing_chars': missing_chars,
            'extra_chars': extra_chars,
            'missing_count': len(missing_chars),
            'extra_count': len(extra_chars)
        }
    
    @staticmethod
    def _analyze_word_differences(user_input: str, correct_answer: str) -> Dict[str, Any]:
        """分析单词级差异（用于短语）"""
        user_words = user_input.split()
        answer_words = correct_answer.split()
        
        return {
            'user_word_count': len(user_words),
            'answer_word_count': len(answer_words),
            'word_count_diff': len(user_words) - len(answer_words),
            'is_single_word': len(answer_words) == 1
        }
    
    @staticmethod
    def _find_common_prefix(str1: str, str2: str) -> str:
        """找到共同前缀"""
        common = ""
        for i in range(min(len(str1), len(str2))):
            if str1[i] == str2[i]:
                common += str1[i]
            else:
                break
        return common
    
    @staticmethod
    def _find_common_suffix(str1: str, str2: str) -> str:
        """找到共同后缀"""
        common = ""
        min_len = min(len(str1), len(str2))
        for i in range(1, min_len + 1):
            if str1[-i] == str2[-i]:
                common = str1[-i] + common
            else:
                break
        return common
    
    @staticmethod
    def _categorize_time(duration: float, expected_min: float, expected_max: float) -> str:
        """时间分类"""
        if duration < expected_min:
            return 'very_fast'
        elif duration <= expected_max:
            return 'normal'
        elif duration <= expected_max * 2:
            return 'slow'
        else:
            return 'very_slow'
    
    @staticmethod
    def _classify_error_types(user_input: str, correct_answer: str) -> List[str]:
        """分类错误类型"""
        if user_input == correct_answer:
            return ['no_error']
        
        error_types = []
        
        # 长度错误
        if len(user_input) != len(correct_answer):
            if len(user_input) < len(correct_answer):
                error_types.append('too_short')
            else:
                error_types.append('too_long')
        
        # 字符替换错误
        if len(user_input) == len(correct_answer):
            substitutions = sum(1 for a, b in zip(user_input, correct_answer) if a != b)
            if substitutions > 0:
                error_types.append('substitution')
        
        # 常见拼写错误模式
        if FeatureExtractor._has_transposition(user_input, correct_answer):
            error_types.append('transposition')
        
        if FeatureExtractor._has_double_letter_error(user_input, correct_answer):
            error_types.append('double_letter')
        
        if not error_types:
            error_types.append('other')
        
        return error_types
    
    @staticmethod
    def _analyze_input_patterns(user_input: str) -> Dict[str, Any]:
        """分析输入模式"""
        return {
            'has_spaces': ' ' in user_input,
            'has_numbers': bool(re.search(r'\d', user_input)),
            'has_special_chars': bool(re.search(r'[^a-zA-Z\s]', user_input)),
            'starts_with_capital': user_input and user_input[0].isupper(),
            'all_lowercase': user_input.islower(),
            'all_uppercase': user_input.isupper()
        }
    
    @staticmethod
    def _analyze_spelling_strategy(user_input: str, correct_answer: str) -> str:
        """分析拼写策略"""
        if user_input == correct_answer:
            return 'correct'
        
        similarity = FeatureExtractor._calculate_similarity(user_input, correct_answer)
        
        if similarity > 0.8:
            return 'close_attempt'
        elif similarity > 0.5:
            return 'partial_knowledge'
        else:
            return 'guess_or_confusion'
    
    @staticmethod
    def _check_capitalization_errors(user_input: str, correct_answer: str) -> bool:
        """检查大小写错误"""
        return user_input.lower() == correct_answer.lower() and user_input != correct_answer
    
    @staticmethod
    def _check_punctuation_errors(user_input: str, correct_answer: str) -> bool:
        """检查标点符号错误"""
        user_alphanum = re.sub(r'[^a-zA-Z0-9\s]', '', user_input)
        answer_alphanum = re.sub(r'[^a-zA-Z0-9\s]', '', correct_answer)
        return user_alphanum == answer_alphanum and user_input != correct_answer
    
    @staticmethod
    def _has_transposition(user_input: str, correct_answer: str) -> bool:
        """检查是否有字符换位错误"""
        if abs(len(user_input) - len(correct_answer)) > 1:
            return False
        
        # 简单的换位检测
        user_sorted = sorted(user_input)
        answer_sorted = sorted(correct_answer)
        
        return user_sorted == answer_sorted and user_input != correct_answer
    
    @staticmethod
    def _has_double_letter_error(user_input: str, correct_answer: str) -> bool:
        """检查是否有重复字母错误"""
        # 检查是否因为多了或少了重复字母导致错误
        for char in set(correct_answer):
            user_count = user_input.count(char)
            answer_count = correct_answer.count(char)
            if abs(user_count - answer_count) > 0:
                return True
        return False