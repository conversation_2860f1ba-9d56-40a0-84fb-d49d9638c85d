"""
单词级别分析器
提供详细的单词维度学习分析功能
"""
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import statistics

from ...models.base import db
from ...core import get_logger

logger = get_logger(__name__)


class WordLevelAnalyzer:
    """单词级别分析器"""
    
    @staticmethod
    def get_difficult_words_analysis(user_id: int, limit: int = 20) -> Dict[str, Any]:
        """
        获取困难单词详细分析
        
        Args:
            user_id: 用户ID
            limit: 返回数量限制
            
        Returns:
            Dict[str, Any]: 困难单词分析结果
        """
        query = """
        SELECT 
            w.id,
            w.english_word,
            w.chinese_meaning,
            w.section,
            LENGTH(w.english_word) as word_length,
            COUNT(*) as total_attempts,
            SUM(CASE WHEN wr.is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
            ROUND(
                CAST(SUM(CASE WHEN wr.is_correct = 1 THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*), 
                3
            ) as accuracy_rate,
            AVG(wr.duration_seconds) as avg_duration,
            MAX(wr.date) as last_learned,
            MIN(wr.date) as first_learned
        FROM word_record wr
        JOIN word w ON wr.word_id = w.id
        WHERE wr.user_id = ?
        GROUP BY w.id, w.english_word, w.chinese_meaning, w.section
        HAVING COUNT(*) >= 2  -- 至少学习2次
        ORDER BY accuracy_rate ASC, total_attempts DESC
        LIMIT ?
        """
        
        results = db.execute_query(query, (user_id, limit))
        
        difficult_words = []
        for row in results:
            word_dict = dict(row)
            
            # 计算学习天数
            if word_dict['first_learned'] and word_dict['last_learned']:
                # 处理可能包含时间的日期格式
                first_date_str = word_dict['first_learned']
                last_date_str = word_dict['last_learned']

                # 如果包含时间信息，只取日期部分
                if ' ' in first_date_str:
                    first_date_str = first_date_str.split(' ')[0]
                if ' ' in last_date_str:
                    last_date_str = last_date_str.split(' ')[0]

                first_date = datetime.strptime(first_date_str, '%Y-%m-%d')
                last_date = datetime.strptime(last_date_str, '%Y-%m-%d')
                learning_days = (last_date - first_date).days + 1
            else:
                learning_days = 1
                
            word_dict['learning_days'] = learning_days
            word_dict['attempts_per_day'] = word_dict['total_attempts'] / learning_days
            
            # 分析错误类型
            error_analysis = WordLevelAnalyzer._analyze_word_errors(user_id, word_dict['id'])
            word_dict['error_analysis'] = error_analysis
            
            difficult_words.append(word_dict)
        
        return {
            'difficult_words': difficult_words,
            'total_count': len(difficult_words),
            'analysis_summary': WordLevelAnalyzer._summarize_difficult_words(difficult_words)
        }
    
    @staticmethod
    def get_word_mastery_distribution(user_id: int) -> Dict[str, Any]:
        """
        获取单词掌握程度分布
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 掌握程度分布
        """
        query = """
        SELECT 
            CASE 
                WHEN accuracy_rate >= 0.9 THEN 'mastered'
                WHEN accuracy_rate >= 0.7 THEN 'good'
                WHEN accuracy_rate >= 0.5 THEN 'learning'
                ELSE 'struggling'
            END as mastery_level,
            COUNT(*) as count,
            AVG(accuracy_rate) as avg_accuracy,
            AVG(total_attempts) as avg_attempts
        FROM (
            SELECT 
                w.id,
                ROUND(
                    CAST(SUM(CASE WHEN wr.is_correct = 1 THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*), 
                    3
                ) as accuracy_rate,
                COUNT(*) as total_attempts
            FROM word_record wr
            JOIN word w ON wr.word_id = w.id
            WHERE wr.user_id = ?
            GROUP BY w.id
            HAVING COUNT(*) >= 2
        ) word_stats
        GROUP BY mastery_level
        ORDER BY 
            CASE mastery_level
                WHEN 'mastered' THEN 1
                WHEN 'good' THEN 2
                WHEN 'learning' THEN 3
                WHEN 'struggling' THEN 4
            END
        """
        
        results = db.execute_query(query, (user_id,))
        
        distribution = {}
        total_words = 0
        
        for row in results:
            level = row['mastery_level']
            count = row['count']
            distribution[level] = {
                'count': count,
                'avg_accuracy': round(row['avg_accuracy'], 3),
                'avg_attempts': round(row['avg_attempts'], 1)
            }
            total_words += count
        
        # 计算百分比
        for level in distribution:
            distribution[level]['percentage'] = round(
                (distribution[level]['count'] / total_words) * 100, 1
            ) if total_words > 0 else 0
        
        return {
            'distribution': distribution,
            'total_words': total_words,
            'summary': WordLevelAnalyzer._summarize_mastery_distribution(distribution, total_words)
        }
    
    @staticmethod
    def get_word_length_analysis(user_id: int) -> Dict[str, Any]:
        """
        按单词长度分析学习表现
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 长度分析结果
        """
        query = """
        SELECT 
            CASE 
                WHEN LENGTH(w.english_word) <= 4 THEN 'short'
                WHEN LENGTH(w.english_word) <= 7 THEN 'medium'
                ELSE 'long'
            END as length_category,
            LENGTH(w.english_word) as exact_length,
            COUNT(*) as total_attempts,
            SUM(CASE WHEN wr.is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
            ROUND(
                CAST(SUM(CASE WHEN wr.is_correct = 1 THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*), 
                3
            ) as accuracy_rate,
            AVG(wr.duration_seconds) as avg_duration
        FROM word_record wr
        JOIN word w ON wr.word_id = w.id
        WHERE wr.user_id = ?
        GROUP BY length_category, exact_length
        ORDER BY exact_length
        """
        
        results = db.execute_query(query, (user_id,))
        
        length_analysis = defaultdict(lambda: {
            'total_attempts': 0,
            'correct_count': 0,
            'accuracy_rate': 0.0,
            'avg_duration': 0.0,
            'lengths': []
        })
        
        for row in results:
            category = row['length_category']
            length_analysis[category]['total_attempts'] += row['total_attempts']
            length_analysis[category]['correct_count'] += row['correct_count']
            length_analysis[category]['lengths'].append({
                'length': row['exact_length'],
                'attempts': row['total_attempts'],
                'accuracy': row['accuracy_rate'],
                'duration': round(row['avg_duration'], 1)
            })
        
        # 计算每个类别的总体准确率
        for category in length_analysis:
            data = length_analysis[category]
            if data['total_attempts'] > 0:
                data['accuracy_rate'] = round(
                    data['correct_count'] / data['total_attempts'], 3
                )
                data['avg_duration'] = round(
                    sum(l['duration'] * l['attempts'] for l in data['lengths']) / data['total_attempts'], 1
                )
        
        return {
            'length_analysis': dict(length_analysis),
            'insights': WordLevelAnalyzer._generate_length_insights(length_analysis)
        }
    
    @staticmethod
    def get_category_analysis(user_id: int) -> Dict[str, Any]:
        """
        按单词类别分析学习表现
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 类别分析结果
        """
        query = """
        SELECT 
            w.section,
            COUNT(*) as total_attempts,
            COUNT(DISTINCT w.id) as unique_words,
            SUM(CASE WHEN wr.is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
            ROUND(
                CAST(SUM(CASE WHEN wr.is_correct = 1 THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*), 
                3
            ) as accuracy_rate,
            AVG(wr.duration_seconds) as avg_duration,
            AVG(LENGTH(w.english_word)) as avg_word_length
        FROM word_record wr
        JOIN word w ON wr.word_id = w.id
        WHERE wr.user_id = ?
        GROUP BY w.section
        HAVING COUNT(*) >= 3  -- 至少3次练习
        ORDER BY accuracy_rate DESC
        """
        
        results = db.execute_query(query, (user_id,))
        
        categories = []
        for row in results:
            category_dict = dict(row)
            category_dict['avg_duration'] = round(category_dict['avg_duration'], 1)
            category_dict['avg_word_length'] = round(category_dict['avg_word_length'], 1)
            category_dict['attempts_per_word'] = round(
                category_dict['total_attempts'] / category_dict['unique_words'], 1
            )
            categories.append(category_dict)
        
        return {
            'categories': categories,
            'insights': WordLevelAnalyzer._generate_category_insights(categories)
        }
    
    @staticmethod
    def _analyze_word_errors(user_id: int, word_id: int) -> Dict[str, Any]:
        """分析特定单词的错误模式"""
        query = """
        SELECT user_input, answer, duration_seconds
        FROM word_record 
        WHERE user_id = ? AND word_id = ? AND is_correct = 0
        ORDER BY date DESC
        LIMIT 10
        """
        
        results = db.execute_query(query, (user_id, word_id))
        
        if not results:
            return {'error_count': 0, 'patterns': []}
        
        error_patterns = []
        for row in results:
            user_input = row['user_input'] or ""
            correct_answer = row['answer'] or ""
            
            # 分析错误类型
            error_type = WordLevelAnalyzer._classify_error_type(user_input, correct_answer)
            error_patterns.append({
                'user_input': user_input,
                'correct_answer': correct_answer,
                'error_type': error_type,
                'duration': round(row['duration_seconds'], 1)
            })
        
        return {
            'error_count': len(error_patterns),
            'patterns': error_patterns,
            'common_errors': WordLevelAnalyzer._find_common_error_patterns(error_patterns)
        }
    
    @staticmethod
    def _classify_error_type(user_input: str, correct_answer: str) -> str:
        """分类错误类型"""
        if not user_input:
            return 'empty_input'
        
        user_clean = user_input.strip().lower()
        answer_clean = correct_answer.strip().lower()
        
        if len(user_clean) == 0:
            return 'empty_input'
        elif len(user_clean) < len(answer_clean) * 0.5:
            return 'too_short'
        elif len(user_clean) > len(answer_clean) * 1.5:
            return 'too_long'
        elif abs(len(user_clean) - len(answer_clean)) <= 2:
            return 'similar_length'
        else:
            return 'length_mismatch'
    
    @staticmethod
    def _find_common_error_patterns(error_patterns: List[Dict[str, Any]]) -> List[str]:
        """找出常见错误模式"""
        error_types = [p['error_type'] for p in error_patterns]
        type_counts = Counter(error_types)
        
        common_patterns = []
        for error_type, count in type_counts.most_common(3):
            if count >= 2:
                common_patterns.append(f"{error_type} ({count}次)")
        
        return common_patterns
    
    @staticmethod
    def _summarize_difficult_words(difficult_words: List[Dict[str, Any]]) -> Dict[str, Any]:
        """总结困难单词分析"""
        if not difficult_words:
            return {}
        
        avg_accuracy = statistics.mean([w['accuracy_rate'] for w in difficult_words])
        avg_attempts = statistics.mean([w['total_attempts'] for w in difficult_words])
        
        # 找出最常见的困难类别
        sections = [w['section'] for w in difficult_words]
        common_section = Counter(sections).most_common(1)[0] if sections else None
        
        return {
            'avg_accuracy': round(avg_accuracy, 3),
            'avg_attempts': round(avg_attempts, 1),
            'most_difficult_category': common_section[0] if common_section else None,
            'total_difficult_words': len(difficult_words)
        }
    
    @staticmethod
    def _summarize_mastery_distribution(distribution: Dict[str, Any], total_words: int) -> Dict[str, Any]:
        """总结掌握程度分布"""
        mastered_count = distribution.get('mastered', {}).get('count', 0)
        struggling_count = distribution.get('struggling', {}).get('count', 0)
        
        mastery_rate = (mastered_count / total_words) * 100 if total_words > 0 else 0
        struggle_rate = (struggling_count / total_words) * 100 if total_words > 0 else 0
        
        return {
            'mastery_rate': round(mastery_rate, 1),
            'struggle_rate': round(struggle_rate, 1),
            'total_words_analyzed': total_words,
            'dominant_level': max(distribution.keys(), key=lambda k: distribution[k]['count']) if distribution else None
        }
    
    @staticmethod
    def _generate_length_insights(length_analysis: Dict[str, Any]) -> List[str]:
        """生成长度分析洞察"""
        insights = []
        
        if 'short' in length_analysis and 'long' in length_analysis:
            short_acc = length_analysis['short']['accuracy_rate']
            long_acc = length_analysis['long']['accuracy_rate']
            
            if short_acc > long_acc + 0.1:
                insights.append("短单词掌握较好，长单词需要更多练习")
            elif long_acc > short_acc + 0.1:
                insights.append("长单词掌握不错，短单词可能需要注意细节")
            else:
                insights.append("各长度单词掌握程度相对均衡")
        
        return insights
    
    @staticmethod
    def _generate_category_insights(categories: List[Dict[str, Any]]) -> List[str]:
        """生成类别分析洞察"""
        insights = []
        
        if len(categories) >= 2:
            best_category = categories[0]
            worst_category = categories[-1]
            
            insights.append(f"最擅长的类别：{best_category['section']} (准确率 {best_category['accuracy_rate']:.1%})")
            insights.append(f"需要加强的类别：{worst_category['section']} (准确率 {worst_category['accuracy_rate']:.1%})")
            
            # 分析用时
            fast_categories = [c for c in categories if c['avg_duration'] < 3.0]
            if fast_categories:
                insights.append(f"反应最快的类别：{fast_categories[0]['section']}")
        
        return insights
