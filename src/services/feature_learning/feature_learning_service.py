"""
特征学习服务
整合特征提取和模式学习，提供统一的特征学习接口
"""
from typing import Dict, List, Any, Optional
from datetime import datetime
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor

from .feature_extractor import FeatureExtractor
from .pattern_learner import PatternLearner
from .word_level_analyzer import WordLevelAnalyzer
from .time_series_analyzer import TimeSeriesAnalyzer
from .error_pattern_analyzer import ErrorPatternAnalyzer
from ...models.feature_learning import UserInputFeatures, FeatureLearningConfig
from ...core import get_logger

logger = get_logger(__name__)


class FeatureLearningService:
    """特征学习服务类"""
    
    _executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="feature_learning")
    
    @staticmethod
    def process_user_input(user_id: int, word_id: int, record_id: int,
                          user_input: str, correct_answer: str, 
                          duration_seconds: float, 
                          context: Dict[str, Any] = None,
                          async_processing: bool = True) -> Dict[str, Any]:
        """
        处理用户输入，提取特征并进行模式学习
        
        Args:
            user_id: 用户ID
            word_id: 单词ID
            record_id: 学习记录ID
            user_input: 用户输入
            correct_answer: 正确答案
            duration_seconds: 答题用时
            context: 上下文信息
            async_processing: 是否异步处理
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 检查功能是否启用
            if not FeatureLearningConfig.is_feature_enabled('feature_extraction'):
                logger.info("特征学习功能未启用", user_id=user_id)
                return {'status': 'disabled', 'message': '特征学习功能未启用'}
            
            # 准备上下文信息
            enhanced_context = FeatureLearningService._prepare_context(
                user_id, word_id, correct_answer, context
            )
            
            if async_processing:
                # 异步处理，不阻塞主流程
                FeatureLearningService._executor.submit(
                    FeatureLearningService._process_features_async,
                    user_id, word_id, record_id, user_input, 
                    correct_answer, duration_seconds, enhanced_context
                )
                
                return {
                    'status': 'processing_async',
                    'message': '特征提取已提交异步处理'
                }
            else:
                # 同步处理
                return FeatureLearningService._process_features_sync(
                    user_id, word_id, record_id, user_input,
                    correct_answer, duration_seconds, enhanced_context
                )
                
        except Exception as e:
            logger.error("特征学习处理失败", user_id=user_id, error=str(e))
            return {'status': 'error', 'message': f'处理失败: {str(e)}'}
    
    @staticmethod
    def _process_features_sync(user_id: int, word_id: int, record_id: int,
                              user_input: str, correct_answer: str,
                              duration_seconds: float, context: Dict[str, Any]) -> Dict[str, Any]:
        """同步处理特征"""
        start_time = datetime.now()
        
        try:
            # 1. 提取特征
            features = FeatureExtractor.extract_all_features(
                user_input, correct_answer, duration_seconds, context
            )
            
            # 2. 保存特征数据
            feature_id = UserInputFeatures.create(
                user_id, word_id, record_id, features
            )
            
            # 3. 检查是否需要更新用户模式
            should_update_patterns = FeatureLearningService._should_update_patterns(user_id)
            
            pattern_result = None
            if should_update_patterns:
                # 4. 更新学习模式（可能耗时，但在同步模式下执行）
                pattern_result = PatternLearner.learn_user_patterns(user_id)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            logger.info("同步特征处理完成", 
                       user_id=user_id, feature_id=feature_id, 
                       processing_time=processing_time)
            
            return {
                'status': 'success',
                'feature_id': feature_id,
                'processing_time': processing_time,
                'pattern_updated': pattern_result is not None,
                'pattern_result': pattern_result
            }
            
        except Exception as e:
            logger.error("同步特征处理失败", user_id=user_id, error=str(e))
            raise
    
    @staticmethod
    def _process_features_async(user_id: int, word_id: int, record_id: int,
                               user_input: str, correct_answer: str,
                               duration_seconds: float, context: Dict[str, Any]):
        """异步处理特征"""
        try:
            result = FeatureLearningService._process_features_sync(
                user_id, word_id, record_id, user_input,
                correct_answer, duration_seconds, context
            )
            logger.info("异步特征处理完成", user_id=user_id, result=result)
        except Exception as e:
            logger.error("异步特征处理失败", user_id=user_id, error=str(e))
    
    @staticmethod
    def _prepare_context(user_id: int, word_id: int, correct_answer: str, 
                        context: Dict[str, Any] = None) -> Dict[str, Any]:
        """准备增强的上下文信息"""
        enhanced_context = context or {}
        
        # 添加单词基础信息
        enhanced_context.update({
            'word_length': len(correct_answer),
            'word_id': word_id,
            'user_id': user_id,
            'processing_timestamp': datetime.now().isoformat()
        })
        
        # 可以从数据库获取更多上下文信息
        # 比如用户历史表现、单词难度等
        
        return enhanced_context
    
    @staticmethod
    def _should_update_patterns(user_id: int) -> bool:
        """判断是否需要更新用户模式"""
        try:
            # 获取配置的更新频率
            config = FeatureLearningConfig.get_config('pattern_learning_thresholds')
            if not config:
                return False
            
            update_frequency = config['config_value'].get('update_frequency', 24)  # 小时
            
            # 获取用户最近的特征数据数量
            recent_features = UserInputFeatures.get_user_features(user_id, limit=100)
            
            # 简单策略：每20个新特征更新一次模式，或者距上次更新超过24小时
            if len(recent_features) % 20 == 0:
                return True
            
            # 可以添加更复杂的更新策略
            return False
            
        except Exception as e:
            logger.warning("判断模式更新失败", user_id=user_id, error=str(e))
            return False
    
    @staticmethod
    def get_user_learning_insights(user_id: int) -> Dict[str, Any]:
        """
        获取用户学习洞察
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 学习洞察报告
        """
        try:
            # 获取用户的学习模式
            patterns = PatternLearner.learn_user_patterns(user_id)
            
            # 获取复习建议
            recommendations = PatternLearner.recommend_review_strategy(user_id)
            
            # 获取最近的特征数据统计
            recent_features = UserInputFeatures.get_user_features(user_id, limit=50)
            
            # 计算基础统计
            basic_stats = FeatureLearningService._calculate_basic_stats(recent_features)
            
            return {
                'user_id': user_id,
                'generated_at': datetime.now().isoformat(),
                'patterns': patterns,
                'recommendations': recommendations,
                'basic_stats': basic_stats,
                'insights_available': len(recent_features) >= 5  # 降低要求从10条到5条
            }
            
        except Exception as e:
            logger.error("获取学习洞察失败", user_id=user_id, error=str(e))
            return {
                'user_id': user_id,
                'error': str(e),
                'insights_available': False
            }
    
    @staticmethod
    def predict_word_difficulty(user_id: int, word_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        预测用户对特定单词的难度
        
        Args:
            user_id: 用户ID
            word_info: 单词信息
            
        Returns:
            Dict[str, Any]: 难度预测结果
        """
        return PatternLearner.predict_difficulty(user_id, word_info)
    
    @staticmethod
    def _calculate_basic_stats(features: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算基础统计信息"""
        if not features:
            return {
                'total_attempts': 0,
                'accuracy_rate': 0.0,
                'average_duration': 0.0,
                'average_similarity': 0.0
            }
        
        total_attempts = len(features)
        correct_count = 0
        total_duration = 0
        total_similarity = 0
        
        for feature in features:
            text_features = feature['feature_data']['text_features']
            time_features = feature['feature_data']['time_features']
            
            if text_features.get('exact_match', False):
                correct_count += 1
            
            total_duration += time_features.get('duration_seconds', 0)
            total_similarity += text_features.get('similarity_score', 0)
        
        return {
            'total_attempts': total_attempts,
            'accuracy_rate': correct_count / total_attempts,
            'average_duration': total_duration / total_attempts,
            'average_similarity': total_similarity / total_attempts,
            'recent_trend': FeatureLearningService._calculate_recent_trend(features)
        }
    
    @staticmethod
    def _calculate_recent_trend(features: List[Dict[str, Any]]) -> str:
        """计算最近的趋势"""
        if len(features) < 6:
            return 'insufficient_data'
        
        # 比较前半部分和后半部分的表现
        mid_point = len(features) // 2
        early_features = features[mid_point:]  # 较早的特征（索引大的是较新的）
        recent_features = features[:mid_point]  # 最近的特征
        
        early_accuracy = sum(1 for f in early_features 
                           if f['feature_data']['text_features'].get('exact_match', False)) / len(early_features)
        recent_accuracy = sum(1 for f in recent_features 
                            if f['feature_data']['text_features'].get('exact_match', False)) / len(recent_features)
        
        if recent_accuracy > early_accuracy + 0.1:
            return 'improving'
        elif recent_accuracy < early_accuracy - 0.1:
            return 'declining'
        else:
            return 'stable'
    
    @staticmethod
    def enhanced_similarity_check(user_input: str, correct_answer: str) -> Dict[str, Any]:
        """
        增强的相似度检查，替换原有的简单算法
        
        Args:
            user_input: 用户输入
            correct_answer: 正确答案
            
        Returns:
            Dict[str, Any]: 详细的相似度分析结果
        """
        # 提取文本特征
        text_features = FeatureExtractor.extract_text_features(user_input, correct_answer)
        
        # 提供更详细的相似度分析
        return {
            'similarity_score': text_features['similarity_score'],
            'edit_distance': text_features['edit_distance'],
            'exact_match': text_features['exact_match'],
            'case_sensitive_match': text_features['case_sensitive_match'],
            'is_close_match': text_features['similarity_score'] > 0.8,
            'has_minor_errors': 0.7 < text_features['similarity_score'] <= 0.8,
            'character_analysis': text_features['character_analysis'],
            'suggested_feedback': FeatureLearningService._generate_feedback(text_features)
        }
    
    @staticmethod
    def _generate_feedback(text_features: Dict[str, Any]) -> str:
        """基于特征生成反馈"""
        similarity = text_features['similarity_score']
        
        if text_features['exact_match']:
            return "完全正确！"
        elif similarity > 0.9:
            return "非常接近，注意细节！"
        elif similarity > 0.7:
            char_analysis = text_features.get('character_analysis', {})
            missing_chars = char_analysis.get('missing_chars', [])
            extra_chars = char_analysis.get('extra_chars', [])
            
            if missing_chars:
                return f"拼写接近，缺少字母：{', '.join(missing_chars)}"
            elif extra_chars:
                return f"拼写接近，多了字母：{', '.join(extra_chars)}"
            else:
                return "拼写接近，再仔细检查！"
        else:
            return "需要更多练习，继续努力！"
    
    @staticmethod
    def cleanup_old_features(days_to_keep: int = 365):
        """清理过期的特征数据"""
        try:
            # 获取保留策略配置
            config = FeatureLearningConfig.get_config('feature_retention_policy')
            if config:
                days_to_keep = config['config_value'].get('days_to_keep', days_to_keep)
            
            # 删除过期数据的SQL逻辑
            # 实际实现会删除超过指定天数的特征记录
            logger.info("特征数据清理完成", days_kept=days_to_keep)
            
        except Exception as e:
            logger.error("特征数据清理失败", error=str(e))

    @staticmethod
    def get_detailed_learning_insights(user_id: int) -> Dict[str, Any]:
        """
        获取详细的学习洞察分析

        Args:
            user_id: 用户ID

        Returns:
            Dict[str, Any]: 详细洞察数据
        """
        try:
            # 获取基础洞察
            basic_insights = FeatureLearningService.get_user_learning_insights(user_id)

            if not basic_insights.get('insights_available', False):
                return basic_insights

            # 获取详细分析
            detailed_analysis = {
                'basic_insights': basic_insights,
                'word_analysis': WordLevelAnalyzer.get_difficult_words_analysis(user_id),
                'mastery_distribution': WordLevelAnalyzer.get_word_mastery_distribution(user_id),
                'length_analysis': WordLevelAnalyzer.get_word_length_analysis(user_id),
                'category_analysis': WordLevelAnalyzer.get_category_analysis(user_id),
                'time_trends': TimeSeriesAnalyzer.get_learning_trends(user_id),
                'time_patterns': TimeSeriesAnalyzer.get_learning_time_patterns(user_id),
                'streak_analysis': TimeSeriesAnalyzer.get_streak_analysis(user_id),
                'efficiency_analysis': TimeSeriesAnalyzer.get_efficiency_analysis(user_id),
                'error_analysis': ErrorPatternAnalyzer.get_comprehensive_error_analysis(user_id)
            }

            # 生成综合建议
            detailed_analysis['comprehensive_recommendations'] = FeatureLearningService._generate_comprehensive_recommendations(detailed_analysis)

            # 确保设置洞察可用标志
            detailed_analysis['insights_available'] = True

            return detailed_analysis

        except Exception as e:
            logger.error(f"获取详细学习洞察失败: {str(e)}")
            return {
                'error': str(e),
                'insights_available': False
            }

    @staticmethod
    def _generate_comprehensive_recommendations(analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        基于详细分析生成综合建议

        Args:
            analysis: 详细分析数据

        Returns:
            List[Dict[str, Any]]: 综合建议列表
        """
        recommendations = []

        try:
            # 基于困难单词的建议
            word_analysis = analysis.get('word_analysis', {})
            if word_analysis.get('difficult_words'):
                difficult_count = len(word_analysis['difficult_words'])
                if difficult_count > 10:
                    recommendations.append({
                        'type': 'focus_practice',
                        'priority': 'high',
                        'title': '重点练习困难单词',
                        'description': f'您有 {difficult_count} 个困难单词需要重点关注',
                        'action': '建议每天专门练习3-5个困难单词'
                    })

            # 基于掌握程度分布的建议
            mastery = analysis.get('mastery_distribution', {})
            distribution = mastery.get('distribution', {})
            if 'struggling' in distribution and distribution['struggling']['count'] > 5:
                recommendations.append({
                    'type': 'learning_strategy',
                    'priority': 'medium',
                    'title': '调整学习策略',
                    'description': f'有 {distribution["struggling"]["count"]} 个单词掌握困难',
                    'action': '建议降低学习难度，增加重复练习'
                })

            # 基于类别分析的建议
            category_analysis = analysis.get('category_analysis', {})
            categories = category_analysis.get('categories', [])
            if categories:
                worst_category = min(categories, key=lambda x: x['accuracy_rate'])
                if worst_category['accuracy_rate'] < 0.6:
                    recommendations.append({
                        'type': 'category_focus',
                        'priority': 'medium',
                        'title': f'加强 {worst_category["section"]} 类别',
                        'description': f'该类别准确率仅 {worst_category["accuracy_rate"]:.1%}',
                        'action': '建议集中练习该类别单词'
                    })

            # 基于学习连续性的建议
            streak_analysis = analysis.get('streak_analysis', {})
            current_streak = streak_analysis.get('current_streak', 0)
            if current_streak == 0:
                recommendations.append({
                    'type': 'consistency',
                    'priority': 'high',
                    'title': '保持学习连续性',
                    'description': '今天还没有学习',
                    'action': '建议每天至少学习10-15分钟'
                })
            elif current_streak >= 7:
                recommendations.append({
                    'type': 'encouragement',
                    'priority': 'low',
                    'title': '学习习惯很棒！',
                    'description': f'已连续学习 {current_streak} 天',
                    'action': '继续保持这个好习惯'
                })

        except Exception as e:
            logger.error(f"生成综合建议失败: {str(e)}")
            recommendations.append({
                'type': 'error',
                'priority': 'low',
                'title': '建议生成失败',
                'description': '无法生成个性化建议',
                'action': '请继续正常学习'
            })

        return recommendations