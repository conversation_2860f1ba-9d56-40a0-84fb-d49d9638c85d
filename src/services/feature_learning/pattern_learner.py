"""
模式学习器
分析用户学习模式，提供个性化推荐
"""
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import json

from ...models.feature_learning import UserInputFeatures, UserLearningPatterns, WordFeaturesStats
from ...core import get_logger

logger = get_logger(__name__)


class PatternLearner:
    """模式学习器类"""
    
    @staticmethod
    def learn_user_patterns(user_id: int, min_samples: int = 5) -> Dict[str, Any]:
        """
        学习用户的个人模式
        
        Args:
            user_id: 用户ID
            min_samples: 最小样本数量
            
        Returns:
            Dict[str, Any]: 学习结果统计
        """
        # 获取用户的特征数据
        features = UserInputFeatures.get_user_features(user_id, limit=1000)
        
        if len(features) < min_samples:
            logger.info("样本数量不足，跳过模式学习", 
                       user_id=user_id, sample_count=len(features), min_required=min_samples)
            return {'status': 'insufficient_data', 'sample_count': len(features)}
        
        results = {}
        
        # 学习错误模式
        error_patterns = PatternLearner._learn_error_patterns(features)
        if error_patterns:
            UserLearningPatterns.create_or_update(
                user_id, 'error_patterns', error_patterns,
                confidence_score=PatternLearner._calculate_confidence(error_patterns, len(features)),
                sample_count=len(features)
            )
            results['error_patterns'] = error_patterns
        
        # 学习时间模式
        time_patterns = PatternLearner._learn_time_patterns(features)
        if time_patterns:
            UserLearningPatterns.create_or_update(
                user_id, 'time_patterns', time_patterns,
                confidence_score=PatternLearner._calculate_confidence(time_patterns, len(features)),
                sample_count=len(features)
            )
            results['time_patterns'] = time_patterns
        
        # 学习难度偏好
        difficulty_patterns = PatternLearner._learn_difficulty_patterns(features)
        if difficulty_patterns:
            UserLearningPatterns.create_or_update(
                user_id, 'difficulty_preferences', difficulty_patterns,
                confidence_score=PatternLearner._calculate_confidence(difficulty_patterns, len(features)),
                sample_count=len(features)
            )
            results['difficulty_patterns'] = difficulty_patterns
        
        # 学习学习效率模式
        efficiency_patterns = PatternLearner._learn_efficiency_patterns(features)
        if efficiency_patterns:
            UserLearningPatterns.create_or_update(
                user_id, 'efficiency_patterns', efficiency_patterns,
                confidence_score=PatternLearner._calculate_confidence(efficiency_patterns, len(features)),
                sample_count=len(features)
            )
            results['efficiency_patterns'] = efficiency_patterns
        
        logger.info("用户模式学习完成", 
                   user_id=user_id, patterns_learned=len(results), sample_count=len(features))
        
        return {
            'status': 'success',
            'patterns_learned': len(results),
            'sample_count': len(features),
            'results': results
        }
    
    @staticmethod
    def _learn_error_patterns(features: List[Dict[str, Any]]) -> Dict[str, Any]:
        """学习错误模式"""
        error_types_counter = Counter()
        common_mistakes = defaultdict(int)
        similarity_distribution = []
        
        for feature in features:
            text_features = feature['feature_data']['text_features']
            behavioral_features = feature['feature_data']['behavioral_features']
            
            # 统计错误类型
            for error_type in behavioral_features.get('error_types', []):
                error_types_counter[error_type] += 1
            
            # 统计相似度分布
            similarity_distribution.append(text_features.get('similarity_score', 0))
            
            # 统计常见错误字符
            char_analysis = text_features.get('character_analysis', {})
            for char in char_analysis.get('missing_chars', []):
                common_mistakes[f'missing_{char}'] += 1
            for char in char_analysis.get('extra_chars', []):
                common_mistakes[f'extra_{char}'] += 1
        
        # 计算平均相似度
        avg_similarity = sum(similarity_distribution) / len(similarity_distribution) if similarity_distribution else 0
        
        return {
            'most_common_errors': dict(error_types_counter.most_common(5)),
            'common_character_mistakes': dict(list(common_mistakes.items())[:10]),
            'average_similarity_score': avg_similarity,
            'similarity_variance': PatternLearner._calculate_variance(similarity_distribution),
            'error_frequency': len([f for f in features if not f['feature_data']['text_features']['exact_match']]) / len(features)
        }
    
    @staticmethod
    def _learn_time_patterns(features: List[Dict[str, Any]]) -> Dict[str, Any]:
        """学习时间模式"""
        durations = []
        speeds = []
        time_categories = Counter()
        hour_performance = defaultdict(list)
        
        for feature in features:
            time_features = feature['feature_data']['time_features']
            context_features = feature['feature_data']['context_features']
            text_features = feature['feature_data']['text_features']
            
            duration = time_features.get('duration_seconds', 0)
            speed = time_features.get('input_speed_chars_per_sec', 0)
            time_category = time_features.get('time_category', 'unknown')
            hour = context_features.get('time_of_day', 0)
            
            durations.append(duration)
            speeds.append(speed)
            time_categories[time_category] += 1
            
            # 按小时统计表现
            hour_performance[hour].append({
                'correct': text_features.get('exact_match', False),
                'duration': duration,
                'speed': speed
            })
        
        # 计算最佳学习时间
        best_hours = PatternLearner._find_best_learning_hours(hour_performance)
        
        return {
            'average_duration': sum(durations) / len(durations),
            'average_speed': sum(speeds) / len(speeds),
            'duration_variance': PatternLearner._calculate_variance(durations),
            'time_category_distribution': dict(time_categories),
            'best_learning_hours': best_hours,
            'speed_consistency': 1.0 - (PatternLearner._calculate_variance(speeds) / (sum(speeds) / len(speeds)) if speeds else 0)
        }
    
    @staticmethod
    def _learn_difficulty_patterns(features: List[Dict[str, Any]]) -> Dict[str, Any]:
        """学习难度偏好模式"""
        word_length_performance = defaultdict(list)
        difficulty_performance = defaultdict(list)
        
        for feature in features:
            context_features = feature['feature_data']['context_features']
            text_features = feature['feature_data']['text_features']
            time_features = feature['feature_data']['time_features']
            
            word_length = context_features.get('word_length', 0)
            difficulty = context_features.get('word_difficulty', 0)
            is_correct = text_features.get('exact_match', False)
            duration = time_features.get('duration_seconds', 0)
            
            word_length_performance[word_length].append({
                'correct': is_correct,
                'duration': duration
            })
            
            if difficulty > 0:
                difficulty_performance[difficulty].append({
                    'correct': is_correct,
                    'duration': duration
                })
        
        # 分析各难度级别的表现
        length_analysis = PatternLearner._analyze_performance_by_category(word_length_performance)
        difficulty_analysis = PatternLearner._analyze_performance_by_category(difficulty_performance)
        
        return {
            'word_length_performance': length_analysis,
            'difficulty_performance': difficulty_analysis,
            'optimal_word_length': PatternLearner._find_optimal_category(length_analysis),
            'comfortable_difficulty': PatternLearner._find_optimal_category(difficulty_analysis)
        }
    
    @staticmethod
    def _learn_efficiency_patterns(features: List[Dict[str, Any]]) -> Dict[str, Any]:
        """学习效率模式"""
        session_positions = defaultdict(list)
        day_of_week_performance = defaultdict(list)
        learning_stages = defaultdict(list)
        
        for feature in features:
            context_features = feature['feature_data']['context_features']
            text_features = feature['feature_data']['text_features']
            time_features = feature['feature_data']['time_features']
            
            session_pos = context_features.get('session_position', 0)
            day_of_week = context_features.get('day_of_week', 0)
            learning_stage = context_features.get('learning_stage', 'unknown')
            
            is_correct = text_features.get('exact_match', False)
            duration = time_features.get('duration_seconds', 0)
            efficiency = time_features.get('time_efficiency_ratio', 0)
            
            performance_data = {
                'correct': is_correct,
                'duration': duration,
                'efficiency': efficiency
            }
            
            session_positions[session_pos].append(performance_data)
            day_of_week_performance[day_of_week].append(performance_data)
            learning_stages[learning_stage].append(performance_data)
        
        return {
            'session_position_analysis': PatternLearner._analyze_performance_by_category(session_positions),
            'day_of_week_analysis': PatternLearner._analyze_performance_by_category(day_of_week_performance),
            'learning_stage_analysis': PatternLearner._analyze_performance_by_category(learning_stages),
            'fatigue_pattern': PatternLearner._detect_fatigue_pattern(session_positions)
        }
    
    @staticmethod
    def predict_difficulty(user_id: int, word_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        预测用户对特定单词的难度
        
        Args:
            user_id: 用户ID
            word_info: 单词信息
            
        Returns:
            Dict[str, Any]: 难度预测结果
        """
        # 获取用户的学习模式
        patterns = UserLearningPatterns.get_user_patterns(user_id)
        
        if not patterns:
            return {
                'predicted_difficulty': 'medium',
                'confidence': 0.0,
                'reasoning': '缺乏用户历史数据，使用默认难度'
            }
        
        # 基于不同模式计算难度预测
        difficulty_scores = []
        reasoning_parts = []
        
        for pattern in patterns:
            pattern_type = pattern['pattern_type']
            pattern_data = pattern['pattern_data']
            confidence = pattern['confidence_score']
            
            if pattern_type == 'difficulty_preferences':
                score = PatternLearner._predict_from_difficulty_pattern(word_info, pattern_data)
                difficulty_scores.append(score * confidence)
                reasoning_parts.append(f"基于难度偏好模式: {score:.2f}")
            
            elif pattern_type == 'error_patterns':
                score = PatternLearner._predict_from_error_pattern(word_info, pattern_data)
                difficulty_scores.append(score * confidence)
                reasoning_parts.append(f"基于错误模式: {score:.2f}")
            
            elif pattern_type == 'time_patterns':
                score = PatternLearner._predict_from_time_pattern(word_info, pattern_data)
                difficulty_scores.append(score * confidence)
                reasoning_parts.append(f"基于时间模式: {score:.2f}")
        
        # 计算最终预测
        if difficulty_scores:
            avg_score = sum(difficulty_scores) / len(difficulty_scores)
            overall_confidence = min(1.0, sum(p['confidence_score'] for p in patterns) / len(patterns))
        else:
            avg_score = 0.5  # 默认中等难度
            overall_confidence = 0.0
        
        # 转换为难度级别
        if avg_score < 0.3:
            predicted_difficulty = 'easy'
        elif avg_score < 0.7:
            predicted_difficulty = 'medium'
        else:
            predicted_difficulty = 'hard'
        
        return {
            'predicted_difficulty': predicted_difficulty,
            'difficulty_score': avg_score,
            'confidence': overall_confidence,
            'reasoning': '; '.join(reasoning_parts)
        }
    
    @staticmethod
    def recommend_review_strategy(user_id: int) -> Dict[str, Any]:
        """
        推荐复习策略
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 复习策略推荐
        """
        patterns = UserLearningPatterns.get_user_patterns(user_id)
        
        recommendations = {
            'focus_areas': [],
            'optimal_session_length': 20,  # 默认20题
            'best_time_slots': [],
            'difficulty_progression': 'adaptive',
            'specific_suggestions': []
        }
        
        for pattern in patterns:
            pattern_type = pattern['pattern_type']
            pattern_data = pattern['pattern_data']
            
            if pattern_type == 'error_patterns':
                # 基于错误模式的建议
                common_errors = pattern_data.get('most_common_errors', {})
                if common_errors:
                    top_error = max(common_errors.items(), key=lambda x: x[1])
                    recommendations['focus_areas'].append(f"改善{top_error[0]}类型的错误")
                    recommendations['specific_suggestions'].append(
                        f"针对{top_error[0]}错误加强练习"
                    )
            
            elif pattern_type == 'time_patterns':
                # 基于时间模式的建议
                best_hours = pattern_data.get('best_learning_hours', [])
                if best_hours:
                    recommendations['best_time_slots'] = best_hours
                    recommendations['specific_suggestions'].append(
                        f"在{best_hours}点学习效果最佳"
                    )
            
            elif pattern_type == 'efficiency_patterns':
                # 基于效率模式的建议
                fatigue_pattern = pattern_data.get('fatigue_pattern', {})
                if fatigue_pattern.get('has_fatigue', False):
                    optimal_length = fatigue_pattern.get('optimal_session_length', 20)
                    recommendations['optimal_session_length'] = optimal_length
                    recommendations['specific_suggestions'].append(
                        f"建议每次学习{optimal_length}个单词以保持效率"
                    )
        
        return recommendations
    
    # 辅助方法
    @staticmethod
    def _calculate_confidence(pattern_data: Dict[str, Any], sample_count: int) -> float:
        """计算模式置信度"""
        # 基于样本数量和模式强度计算置信度
        base_confidence = min(1.0, sample_count / 50.0)  # 50个样本达到基础置信度
        
        # 可以根据模式的一致性调整置信度
        # 这里简化处理，实际可以更复杂
        return base_confidence
    
    @staticmethod
    def _calculate_variance(values: List[float]) -> float:
        """计算方差"""
        if len(values) < 2:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance
    
    @staticmethod
    def _find_best_learning_hours(hour_performance: Dict[int, List[Dict[str, Any]]]) -> List[int]:
        """找到最佳学习时间"""
        hour_scores = {}
        
        for hour, performances in hour_performance.items():
            if len(performances) < 5:  # 样本太少
                continue
            
            correct_rate = sum(1 for p in performances if p['correct']) / len(performances)
            avg_speed = sum(p['speed'] for p in performances) / len(performances)
            
            # 综合正确率和速度计算分数
            score = correct_rate * 0.7 + min(1.0, avg_speed / 2.0) * 0.3
            hour_scores[hour] = score
        
        # 返回前3个最佳时间
        best_hours = sorted(hour_scores.items(), key=lambda x: x[1], reverse=True)[:3]
        return [hour for hour, score in best_hours]
    
    @staticmethod
    def _analyze_performance_by_category(category_data: Dict[Any, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """按类别分析表现"""
        analysis = {}
        
        for category, performances in category_data.items():
            if len(performances) < 3:  # 样本太少
                continue
            
            correct_count = sum(1 for p in performances if p['correct'])
            correct_rate = correct_count / len(performances)
            avg_duration = sum(p['duration'] for p in performances) / len(performances)
            
            analysis[str(category)] = {
                'correct_rate': correct_rate,
                'average_duration': avg_duration,
                'sample_count': len(performances),
                'performance_score': correct_rate * 0.8 + (1.0 / (1.0 + avg_duration)) * 0.2
            }
        
        return analysis
    
    @staticmethod
    def _find_optimal_category(analysis: Dict[str, Any]) -> str:
        """找到最佳类别"""
        if not analysis:
            return 'unknown'
        
        best_category = max(analysis.items(), key=lambda x: x[1]['performance_score'])
        return best_category[0]
    
    @staticmethod
    def _detect_fatigue_pattern(session_positions: Dict[int, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """检测疲劳模式"""
        if len(session_positions) < 10:
            return {'has_fatigue': False}
        
        # 按位置排序，分析表现趋势
        sorted_positions = sorted(session_positions.items())
        
        early_performance = []
        late_performance = []
        
        for pos, performances in sorted_positions:
            avg_correct = sum(1 for p in performances if p['correct']) / len(performances) if performances else 0
            
            if pos <= 10:  # 前10题
                early_performance.append(avg_correct)
            elif pos >= 20:  # 后面的题
                late_performance.append(avg_correct)
        
        if early_performance and late_performance:
            early_avg = sum(early_performance) / len(early_performance)
            late_avg = sum(late_performance) / len(late_performance)
            
            # 如果后期表现明显下降，说明有疲劳
            fatigue_detected = early_avg - late_avg > 0.1
            
            return {
                'has_fatigue': fatigue_detected,
                'early_performance': early_avg,
                'late_performance': late_avg,
                'optimal_session_length': 15 if fatigue_detected else 25
            }
        
        return {'has_fatigue': False}
    
    @staticmethod
    def _predict_from_difficulty_pattern(word_info: Dict[str, Any], pattern_data: Dict[str, Any]) -> float:
        """基于难度模式预测"""
        word_length = word_info.get('word_length', 5)
        
        length_performance = pattern_data.get('word_length_performance', {})
        if str(word_length) in length_performance:
            perf_data = length_performance[str(word_length)]
            return 1.0 - perf_data.get('correct_rate', 0.5)  # 正确率越低，难度越高
        
        return 0.5  # 默认中等难度
    
    @staticmethod
    def _predict_from_error_pattern(word_info: Dict[str, Any], pattern_data: Dict[str, Any]) -> float:
        """基于错误模式预测"""
        error_frequency = pattern_data.get('error_frequency', 0.5)
        return error_frequency  # 错误频率作为难度指标
    
    @staticmethod
    def _predict_from_time_pattern(word_info: Dict[str, Any], pattern_data: Dict[str, Any]) -> float:
        """基于时间模式预测"""
        word_length = word_info.get('word_length', 5)
        avg_speed = pattern_data.get('average_speed', 1.0)
        
        # 预期时间基于单词长度和用户平均速度
        expected_time = word_length / avg_speed if avg_speed > 0 else word_length
        
        # 如果预期时间过长，认为难度较高
        if expected_time > 10:
            return 0.8
        elif expected_time > 5:
            return 0.5
        else:
            return 0.3