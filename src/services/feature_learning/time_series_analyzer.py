"""
时间序列分析器
提供学习时间趋势和模式分析
"""
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import statistics

from ...models.base import db
from ...core import get_logger

logger = get_logger(__name__)


class TimeSeriesAnalyzer:
    """时间序列分析器"""
    
    @staticmethod
    def get_learning_trends(user_id: int, days: int = 30) -> Dict[str, Any]:
        """
        获取学习趋势分析
        
        Args:
            user_id: 用户ID
            days: 分析天数
            
        Returns:
            Dict[str, Any]: 学习趋势数据
        """
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days-1)
        
        query = """
        SELECT 
            date,
            COUNT(*) as attempts,
            SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_count,
            ROUND(
                CAST(SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*), 
                3
            ) as daily_accuracy,
            AVG(duration_seconds) as avg_duration,
            COUNT(DISTINCT word_id) as unique_words
        FROM word_record 
        WHERE user_id = ? AND date >= ? AND date <= ?
        GROUP BY date
        ORDER BY date
        """
        
        results = db.execute_query(query, (user_id, start_date.isoformat(), end_date.isoformat()))
        
        # 创建完整的日期序列
        daily_data = {}
        current_date = start_date
        while current_date <= end_date:
            daily_data[current_date.isoformat()] = {
                'date': current_date.isoformat(),
                'attempts': 0,
                'correct_count': 0,
                'daily_accuracy': 0.0,
                'avg_duration': 0.0,
                'unique_words': 0
            }
            current_date += timedelta(days=1)
        
        # 填入实际数据
        for row in results:
            date_str = row['date']
            # 如果包含时间信息，只取日期部分
            if ' ' in date_str:
                date_str = date_str.split(' ')[0]
            daily_data[date_str] = {
                'date': date_str,
                'attempts': row['attempts'],
                'correct_count': row['correct_count'],
                'daily_accuracy': row['daily_accuracy'],
                'avg_duration': round(row['avg_duration'], 1),
                'unique_words': row['unique_words']
            }
        
        # 转换为列表并计算趋势
        timeline = list(daily_data.values())
        trends = TimeSeriesAnalyzer._calculate_trends(timeline)
        
        return {
            'timeline': timeline,
            'trends': trends,
            'summary': TimeSeriesAnalyzer._summarize_trends(timeline, trends)
        }
    
    @staticmethod
    def get_learning_time_patterns(user_id: int) -> Dict[str, Any]:
        """
        分析学习时间模式
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 时间模式分析
        """
        # 这里需要扩展数据库以包含学习时间信息
        # 目前基于现有数据进行分析
        query = """
        SELECT 
            date,
            COUNT(*) as session_length,
            AVG(duration_seconds) as avg_response_time,
            SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) as correct_count
        FROM word_record 
        WHERE user_id = ?
        GROUP BY date
        HAVING COUNT(*) >= 5  -- 至少5题算一个有效学习会话
        ORDER BY date DESC
        LIMIT 30
        """
        
        results = db.execute_query(query, (user_id,))
        
        sessions = []
        for row in results:
            session_data = dict(row)
            session_data['accuracy'] = round(
                session_data['correct_count'] / session_data['session_length'], 3
            )
            session_data['avg_response_time'] = round(session_data['avg_response_time'], 1)
            sessions.append(session_data)
        
        patterns = TimeSeriesAnalyzer._analyze_session_patterns(sessions)
        
        return {
            'recent_sessions': sessions,
            'patterns': patterns,
            'recommendations': TimeSeriesAnalyzer._generate_time_recommendations(patterns)
        }
    
    @staticmethod
    def get_streak_analysis(user_id: int) -> Dict[str, Any]:
        """
        分析学习连续性
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 连续学习分析
        """
        query = """
        SELECT DISTINCT date
        FROM word_record 
        WHERE user_id = ?
        ORDER BY date DESC
        """
        
        results = db.execute_query(query, (user_id,))
        
        if not results:
            return {
                'current_streak': 0,
                'longest_streak': 0,
                'total_active_days': 0,
                'streak_history': []
            }
        
        # 获取所有学习日期（处理可能包含时间的格式）
        learning_dates = []
        for row in results:
            date_str = row['date']
            # 如果包含时间信息，只取日期部分
            if ' ' in date_str:
                date_str = date_str.split(' ')[0]
            learning_dates.append(datetime.strptime(date_str, '%Y-%m-%d').date())
        learning_dates.sort(reverse=True)
        
        # 计算当前连续天数
        current_streak = TimeSeriesAnalyzer._calculate_current_streak(learning_dates)
        
        # 计算最长连续天数
        longest_streak = TimeSeriesAnalyzer._calculate_longest_streak(learning_dates)
        
        # 生成连续性历史
        streak_history = TimeSeriesAnalyzer._generate_streak_history(learning_dates)
        
        return {
            'current_streak': current_streak,
            'longest_streak': longest_streak,
            'total_active_days': len(learning_dates),
            'streak_history': streak_history,
            'insights': TimeSeriesAnalyzer._generate_streak_insights(current_streak, longest_streak, len(learning_dates))
        }
    
    @staticmethod
    def get_efficiency_analysis(user_id: int) -> Dict[str, Any]:
        """
        分析学习效率变化
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 效率分析结果
        """
        query = """
        SELECT 
            date,
            COUNT(*) as attempts,
            AVG(duration_seconds) as avg_duration,
            ROUND(
                CAST(SUM(CASE WHEN is_correct = 1 THEN 1 ELSE 0 END) AS FLOAT) / COUNT(*), 
                3
            ) as accuracy
        FROM word_record 
        WHERE user_id = ?
        GROUP BY date
        HAVING COUNT(*) >= 3
        ORDER BY date
        """
        
        results = db.execute_query(query, (user_id,))
        
        efficiency_data = []
        for row in results:
            # 计算效率分数 (准确率 / 平均用时)
            efficiency_score = row['accuracy'] / max(row['avg_duration'], 0.1)
            
            efficiency_data.append({
                'date': row['date'],
                'attempts': row['attempts'],
                'accuracy': row['accuracy'],
                'avg_duration': round(row['avg_duration'], 1),
                'efficiency_score': round(efficiency_score, 3)
            })
        
        # 分析效率趋势
        efficiency_trends = TimeSeriesAnalyzer._analyze_efficiency_trends(efficiency_data)
        
        return {
            'efficiency_data': efficiency_data,
            'trends': efficiency_trends,
            'insights': TimeSeriesAnalyzer._generate_efficiency_insights(efficiency_trends)
        }
    
    @staticmethod
    def _calculate_trends(timeline: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算各项指标的趋势"""
        # 过滤出有数据的天数
        active_days = [day for day in timeline if day['attempts'] > 0]
        
        if len(active_days) < 3:
            return {
                'attempts_trend': 'insufficient_data',
                'accuracy_trend': 'insufficient_data',
                'duration_trend': 'insufficient_data'
            }
        
        # 计算趋势（简单的前后对比）
        recent_days = active_days[-7:] if len(active_days) >= 7 else active_days[-3:]
        earlier_days = active_days[:-7] if len(active_days) >= 14 else active_days[:-3]
        
        if not earlier_days:
            earlier_days = active_days[:len(active_days)//2]
            recent_days = active_days[len(active_days)//2:]
        
        trends = {}
        
        # 练习量趋势
        recent_attempts = statistics.mean([d['attempts'] for d in recent_days])
        earlier_attempts = statistics.mean([d['attempts'] for d in earlier_days])
        trends['attempts_trend'] = TimeSeriesAnalyzer._classify_trend(recent_attempts, earlier_attempts)
        
        # 准确率趋势
        recent_accuracy = statistics.mean([d['daily_accuracy'] for d in recent_days if d['daily_accuracy'] > 0])
        earlier_accuracy = statistics.mean([d['daily_accuracy'] for d in earlier_days if d['daily_accuracy'] > 0])
        trends['accuracy_trend'] = TimeSeriesAnalyzer._classify_trend(recent_accuracy, earlier_accuracy)
        
        # 用时趋势（越短越好）
        recent_duration = statistics.mean([d['avg_duration'] for d in recent_days if d['avg_duration'] > 0])
        earlier_duration = statistics.mean([d['avg_duration'] for d in earlier_days if d['avg_duration'] > 0])
        trends['duration_trend'] = TimeSeriesAnalyzer._classify_trend(earlier_duration, recent_duration)  # 反向比较
        
        return trends
    
    @staticmethod
    def _classify_trend(recent_value: float, earlier_value: float, threshold: float = 0.05) -> str:
        """分类趋势方向"""
        if abs(recent_value - earlier_value) / max(earlier_value, 0.001) < threshold:
            return 'stable'
        elif recent_value > earlier_value:
            return 'improving'
        else:
            return 'declining'
    
    @staticmethod
    def _summarize_trends(timeline: List[Dict[str, Any]], trends: Dict[str, Any]) -> Dict[str, Any]:
        """总结趋势分析"""
        active_days = [day for day in timeline if day['attempts'] > 0]
        
        if not active_days:
            return {'status': 'no_data'}
        
        total_attempts = sum(day['attempts'] for day in active_days)
        total_correct = sum(day['correct_count'] for day in active_days)
        overall_accuracy = total_correct / total_attempts if total_attempts > 0 else 0
        
        return {
            'active_days_count': len(active_days),
            'total_attempts': total_attempts,
            'overall_accuracy': round(overall_accuracy, 3),
            'avg_daily_attempts': round(total_attempts / len(active_days), 1),
            'trends_summary': trends
        }
    
    @staticmethod
    def _analyze_session_patterns(sessions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析学习会话模式"""
        if not sessions:
            return {}
        
        session_lengths = [s['session_length'] for s in sessions]
        accuracies = [s['accuracy'] for s in sessions]
        response_times = [s['avg_response_time'] for s in sessions]
        
        return {
            'avg_session_length': round(statistics.mean(session_lengths), 1),
            'optimal_session_length': TimeSeriesAnalyzer._find_optimal_session_length(sessions),
            'avg_accuracy': round(statistics.mean(accuracies), 3),
            'avg_response_time': round(statistics.mean(response_times), 1),
            'consistency_score': TimeSeriesAnalyzer._calculate_consistency_score(sessions)
        }
    
    @staticmethod
    def _find_optimal_session_length(sessions: List[Dict[str, Any]]) -> int:
        """找出最佳学习会话长度"""
        # 按会话长度分组，找出准确率最高的长度范围
        length_groups = defaultdict(list)
        
        for session in sessions:
            length_range = (session['session_length'] // 5) * 5  # 按5题分组
            length_groups[length_range].append(session['accuracy'])
        
        best_length = 20  # 默认值
        best_accuracy = 0
        
        for length, accuracies in length_groups.items():
            if len(accuracies) >= 2:  # 至少2个样本
                avg_accuracy = statistics.mean(accuracies)
                if avg_accuracy > best_accuracy:
                    best_accuracy = avg_accuracy
                    best_length = length + 2  # 取范围中点
        
        return min(max(best_length, 10), 30)  # 限制在10-30题之间
    
    @staticmethod
    def _calculate_consistency_score(sessions: List[Dict[str, Any]]) -> float:
        """计算学习一致性分数"""
        if len(sessions) < 3:
            return 0.5
        
        accuracies = [s['accuracy'] for s in sessions]
        response_times = [s['avg_response_time'] for s in sessions]
        
        # 计算变异系数（越小越一致）
        accuracy_cv = statistics.stdev(accuracies) / statistics.mean(accuracies) if statistics.mean(accuracies) > 0 else 1
        time_cv = statistics.stdev(response_times) / statistics.mean(response_times) if statistics.mean(response_times) > 0 else 1
        
        # 转换为一致性分数（0-1，越高越一致）
        consistency = 1 / (1 + (accuracy_cv + time_cv) / 2)
        return round(consistency, 3)
    
    @staticmethod
    def _calculate_current_streak(learning_dates: List[datetime]) -> int:
        """计算当前连续学习天数"""
        if not learning_dates:
            return 0
        
        today = datetime.now().date()
        current_streak = 0
        
        # 从今天开始往前检查
        check_date = today
        for i, learning_date in enumerate(learning_dates):
            if learning_date == check_date:
                current_streak += 1
                check_date -= timedelta(days=1)
            elif learning_date < check_date:
                # 如果是昨天的日期，检查是否连续
                if (check_date - learning_date).days == 1:
                    current_streak += 1
                    check_date = learning_date - timedelta(days=1)
                else:
                    break
            # 如果learning_date > check_date，继续下一个
        
        return current_streak
    
    @staticmethod
    def _calculate_longest_streak(learning_dates: List[datetime]) -> int:
        """计算最长连续学习天数"""
        if not learning_dates:
            return 0
        
        learning_dates.sort()
        longest_streak = 1
        current_streak = 1
        
        for i in range(1, len(learning_dates)):
            if (learning_dates[i] - learning_dates[i-1]).days == 1:
                current_streak += 1
                longest_streak = max(longest_streak, current_streak)
            else:
                current_streak = 1
        
        return longest_streak
    
    @staticmethod
    def _generate_streak_history(learning_dates: List[datetime]) -> List[Dict[str, Any]]:
        """生成连续学习历史"""
        if not learning_dates:
            return []
        
        learning_dates.sort(reverse=True)
        history = []
        
        # 最近30天的学习情况
        today = datetime.now().date()
        for i in range(30):
            check_date = today - timedelta(days=i)
            is_active = check_date in learning_dates
            history.append({
                'date': check_date.isoformat(),
                'is_active': is_active
            })
        
        return history
    
    @staticmethod
    def _generate_time_recommendations(patterns: Dict[str, Any]) -> List[str]:
        """生成时间管理建议"""
        recommendations = []
        
        if 'optimal_session_length' in patterns:
            optimal_length = patterns['optimal_session_length']
            recommendations.append(f"建议每次学习 {optimal_length} 题，这是您的最佳学习长度")
        
        if 'consistency_score' in patterns:
            consistency = patterns['consistency_score']
            if consistency < 0.6:
                recommendations.append("尝试保持更规律的学习节奏，有助于提高学习效果")
            elif consistency > 0.8:
                recommendations.append("您的学习节奏很稳定，继续保持！")
        
        return recommendations
    
    @staticmethod
    def _analyze_efficiency_trends(efficiency_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析效率趋势"""
        if len(efficiency_data) < 3:
            return {'trend': 'insufficient_data'}
        
        recent_efficiency = statistics.mean([d['efficiency_score'] for d in efficiency_data[-5:]])
        earlier_efficiency = statistics.mean([d['efficiency_score'] for d in efficiency_data[:-5]]) if len(efficiency_data) > 5 else statistics.mean([d['efficiency_score'] for d in efficiency_data[:len(efficiency_data)//2]])
        
        trend = TimeSeriesAnalyzer._classify_trend(recent_efficiency, earlier_efficiency)
        
        return {
            'trend': trend,
            'recent_efficiency': round(recent_efficiency, 3),
            'earlier_efficiency': round(earlier_efficiency, 3),
            'improvement_rate': round((recent_efficiency - earlier_efficiency) / earlier_efficiency * 100, 1) if earlier_efficiency > 0 else 0
        }
    
    @staticmethod
    def _generate_efficiency_insights(efficiency_trends: Dict[str, Any]) -> List[str]:
        """生成效率洞察"""
        insights = []
        
        if efficiency_trends.get('trend') == 'improving':
            insights.append(f"学习效率正在提升，提高了 {efficiency_trends.get('improvement_rate', 0):.1f}%")
        elif efficiency_trends.get('trend') == 'declining':
            insights.append("学习效率有所下降，建议调整学习策略")
        elif efficiency_trends.get('trend') == 'stable':
            insights.append("学习效率保持稳定")
        
        return insights
    
    @staticmethod
    def _generate_streak_insights(current_streak: int, longest_streak: int, total_days: int) -> List[str]:
        """生成连续学习洞察"""
        insights = []
        
        if current_streak == 0:
            insights.append("今天还没有学习，快来开始吧！")
        elif current_streak == 1:
            insights.append("很好！继续保持学习势头")
        elif current_streak >= 7:
            insights.append(f"太棒了！您已经连续学习 {current_streak} 天")
        else:
            insights.append(f"已连续学习 {current_streak} 天，继续加油！")
        
        if longest_streak > current_streak:
            insights.append(f"您的最长连续记录是 {longest_streak} 天，可以挑战一下")
        
        if total_days >= 30:
            insights.append(f"总共学习了 {total_days} 天，学习习惯很好！")
        
        return insights
