"""
Services package - 业务逻辑服务
提供模块化的业务服务，支持向后兼容和新模块化导入
"""

# 新模块化导入 - 推荐使用
from .auth import AuthService
from .learning import LearningService, LearningPlanService
from .statistics import StatisticsService
from .proficiency import ProficiencyCalculator

# 向后兼容性导入 - 保持原有导入方式正常工作
# 这样现有代码 from src.services import AuthService 仍然可以正常工作
try:
    # 如果原始services.py文件存在，也从那里导入（过渡期）
    from .services import AuthService as _LegacyAuthService
except ImportError:
    # 如果原始文件不存在，使用新模块
    pass

__all__ = [
    # 认证服务
    'AuthService',

    # 学习服务
    'LearningService',
    'LearningPlanService',

    # 统计服务
    'StatisticsService',

    # 熟练度计算
    'ProficiencyCalculator'
]

# 版本信息
__version__ = '2.0.0'  # 服务层重构版本

# 模块信息
__modules__ = {
    'auth': 'src.services.auth',
    'learning': 'src.services.learning',
    'statistics': 'src.services.statistics',
    'proficiency': 'src.services.proficiency'
}
