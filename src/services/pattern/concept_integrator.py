"""
Pattern概念整合器
解决语义冗余问题，实现智能概念去重和整合
"""

from typing import List, Dict, Set, Optional, Tuple
from dataclasses import dataclass
from src.core import get_logger

logger = get_logger(__name__)


@dataclass
class ConceptGroup:
    """概念组定义"""
    group_id: str
    group_name: str
    primary_pattern: Dict  # 主要代表pattern
    integrated_patterns: List[Dict]  # 整合的patterns
    educational_value: float
    cognitive_level: str
    dimension_category: str


class ConceptIntegrator:
    """Pattern概念整合器"""
    
    def __init__(self):
        self._concept_mappings = self._build_concept_mappings()
        self._integration_rules = self._build_integration_rules()
    
    def integrate_patterns(self, static_patterns: List[Dict], dynamic_patterns: List[Dict], 
                          user_proficiency: float = 50.0) -> List[Dict]:
        """
        智能整合patterns，解决语义冗余问题
        
        Args:
            static_patterns: 静态数据库patterns
            dynamic_patterns: 动态检测patterns
            user_proficiency: 用户平均熟练度
            
        Returns:
            整合后的去重patterns列表
        """
        try:
            # 第一步：按概念组分类所有patterns
            concept_groups = self._group_patterns_by_concept(static_patterns, dynamic_patterns)
            
            # 第二步：每个概念组内选择最佳代表pattern
            integrated_patterns = []
            for group in concept_groups.values():
                best_pattern = self._select_best_pattern_from_group(group, user_proficiency)
                if best_pattern:
                    integrated_patterns.append(best_pattern)
            
            # 第三步：添加未归类的独立patterns
            ungrouped_patterns = self._get_ungrouped_patterns(static_patterns, dynamic_patterns)
            integrated_patterns.extend(ungrouped_patterns)
            
            # 第四步：按认知层次和教育价值排序
            sorted_patterns = self._sort_by_cognitive_hierarchy(integrated_patterns, user_proficiency)
            
            logger.info(f"概念整合完成: {len(static_patterns + dynamic_patterns)} -> {len(sorted_patterns)}")
            return sorted_patterns
            
        except Exception as e:
            logger.error(f"Pattern概念整合失败: {e}")
            # 失败时返回简单去重结果
            return self._simple_deduplication(static_patterns, dynamic_patterns)
    
    def _build_concept_mappings(self) -> Dict[str, Dict]:
        """构建概念映射规则"""
        return {
            # ER概念组：后缀、双元音、字母组合都视为同一概念
            'er_concept': {
                'patterns': [
                    ('suffix', 'er'),
                    ('letter_combo', 'er'), 
                    ('phonetic', 'er'),
                    ('rhyme', 'er')
                ],
                'primary_type': 'letter_combo',  # 优先选择类型
                'name': 'ER模式整合',
                'dimension': 'orthography',
                'cognitive_level': 'basic'
            },
            
            # ING概念组：后缀、字母组合、发音、押韵都视为同一概念
            'ing_concept': {
                'patterns': [
                    ('suffix', 'ing'),
                    ('letter_combo', 'ing'),
                    ('phonetic', 'ing'), 
                    ('rhyme', 'ng')  # NG尾音通常是ING后缀的一部分
                ],
                'primary_type': 'suffix',
                'name': 'ING模式整合',
                'dimension': 'morphology',
                'cognitive_level': 'intermediate'
            },
            
            # 家庭概念组
            'family_concept': {
                'patterns': [
                    ('theme', 'family'),
                    ('semantic', '家庭关系'),
                    ('collocation', 'family_words')
                ],
                'primary_type': 'theme',
                'name': '家庭关系整合',
                'dimension': 'semantic',
                'cognitive_level': 'intermediate'
            },
            
            # 时间概念组
            'time_concept': {
                'patterns': [
                    ('theme', 'time'),
                    ('semantic', '时间'),
                    ('collocation', 'time_words')
                ],
                'primary_type': 'theme', 
                'name': '时间表达整合',
                'dimension': 'semantic',
                'cognitive_level': 'intermediate'
            },
            
            # 颜色概念组
            'color_concept': {
                'patterns': [
                    ('theme', 'colors'),
                    ('semantic', '颜色'),
                    ('synonym', 'color_words')
                ],
                'primary_type': 'theme',
                'name': '颜色词汇整合', 
                'dimension': 'semantic',
                'cognitive_level': 'basic'
            },
            
            # 学校概念组
            'school_concept': {
                'patterns': [
                    ('theme', 'school'),
                    ('semantic', '学习'),
                    ('collocation', 'school_words')
                ],
                'primary_type': 'theme',
                'name': '学校场景整合',
                'dimension': 'collocation',
                'cognitive_level': 'intermediate'
            },
            
            # 动作概念组  
            'action_concept': {
                'patterns': [
                    ('semantic', '动作'),
                    ('verb_forms', 'action_verbs'),
                    ('collocation', 'action_words')
                ],
                'primary_type': 'semantic',
                'name': '动作词汇整合',
                'dimension': 'semantic', 
                'cognitive_level': 'intermediate'
            },
            
            # 同义词组整合（按基础词分组）
            'good_meaning_group': {
                'patterns': [
                    ('synonym', 'good'),
                    ('antonym', 'good')  # 反义词也归到同一概念组
                ],
                'primary_type': 'synonym',
                'name': 'good词义关系',
                'dimension': 'semantic',
                'cognitive_level': 'intermediate'
            },
            
            'big_meaning_group': {
                'patterns': [
                    ('synonym', 'big'),
                    ('antonym', 'big'),
                    ('adjective_forms', 'big')
                ],
                'primary_type': 'synonym', 
                'name': 'big词义关系',
                'dimension': 'semantic',
                'cognitive_level': 'intermediate'
            },
            
            # 形容词变形组（按基础形式分组）
            'adjective_forms_group': {
                'patterns': [
                    ('adjective_forms', 'good'),
                    ('adjective_forms', 'big'),
                    ('adjective_forms', 'small'),
                    ('adjective_forms', 'fast')
                ],
                'primary_type': 'adjective_forms',
                'name': '形容词变形',
                'dimension': 'morphology',
                'cognitive_level': 'intermediate'
            },
            
            # 复数形式组
            'plural_forms_group': {
                'patterns': [
                    ('plural_forms', 'book'),
                    ('plural_forms', 'child'),
                    ('plural_forms', 'man')
                ],
                'primary_type': 'plural_forms',
                'name': '名词复数变化',
                'dimension': 'morphology', 
                'cognitive_level': 'basic'
            }
        }
    
    def _build_integration_rules(self) -> Dict[str, float]:
        """构建整合规则的权重配置"""
        return {
            'static_priority_boost': 0.2,  # 静态pattern优先级提升
            'primary_type_boost': 0.3,     # 主要类型优先级提升
            'educational_value_weight': 0.4,  # 教育价值权重
            'word_count_weight': 0.3,      # 单词数量权重
            'match_strength_weight': 0.3,  # 匹配强度权重
        }
    
    def _group_patterns_by_concept(self, static_patterns: List[Dict], 
                                 dynamic_patterns: List[Dict]) -> Dict[str, List[Dict]]:
        """按概念组对patterns进行分类"""
        concept_groups = {}
        
        # 处理所有patterns
        all_patterns = []
        for pattern in static_patterns:
            pattern['source'] = 'static'
            all_patterns.append(pattern)
        for pattern in dynamic_patterns:
            pattern['source'] = 'dynamic'
            all_patterns.append(pattern)
        
        # 按概念映射规则分组
        for pattern in all_patterns:
            pattern_key = (pattern.get('pattern_type'), pattern.get('pattern_value'))
            concept_group_id = None
            
            # 查找匹配的概念组
            for group_id, config in self._concept_mappings.items():
                if pattern_key in config['patterns']:
                    concept_group_id = group_id
                    break
            
            # 如果找到概念组，添加到对应组中
            if concept_group_id:
                if concept_group_id not in concept_groups:
                    concept_groups[concept_group_id] = []
                concept_groups[concept_group_id].append(pattern)
        
        return concept_groups
    
    def _select_best_pattern_from_group(self, group_patterns: List[Dict], 
                                      user_proficiency: float) -> Optional[Dict]:
        """从概念组中选择最佳代表pattern"""
        if not group_patterns:
            return None
        
        # 如果只有一个pattern，直接返回
        if len(group_patterns) == 1:
            return group_patterns[0]
        
        # 获取概念组配置
        concept_config = None
        for pattern in group_patterns:
            pattern_key = (pattern.get('pattern_type'), pattern.get('pattern_value'))
            for group_id, config in self._concept_mappings.items():
                if pattern_key in config['patterns']:
                    concept_config = config
                    break
            if concept_config:
                break
        
        if not concept_config:
            # 没有配置的情况下，选择第一个静态pattern或教育价值最高的
            static_patterns = [p for p in group_patterns if p.get('source') == 'static']
            if static_patterns:
                return static_patterns[0]
            return max(group_patterns, key=lambda p: p.get('match_strength', 0))
        
        # 根据规则选择最佳pattern
        best_pattern = None
        best_score = -1
        
        for pattern in group_patterns:
            score = self._calculate_pattern_score(pattern, concept_config, user_proficiency)
            if score > best_score:
                best_score = score
                best_pattern = pattern
        
        # 增强最佳pattern的信息（整合概念组信息）
        if best_pattern:
            best_pattern = self._enhance_pattern_with_concept_info(
                best_pattern, group_patterns, concept_config
            )
        
        return best_pattern
    
    def _calculate_pattern_score(self, pattern: Dict, concept_config: Dict, 
                               user_proficiency: float) -> float:
        """计算pattern在概念组中的评分"""
        score = 0.0
        rules = self._integration_rules
        
        # 基础分数：教育价值或匹配强度
        base_score = pattern.get('educational_value', pattern.get('match_strength', 0.5))
        score += base_score * 0.4
        
        # 静态pattern加分
        if pattern.get('source') == 'static':
            score += rules['static_priority_boost']
        
        # 主要类型加分
        if pattern.get('pattern_type') == concept_config['primary_type']:
            score += rules['primary_type_boost']
        
        # 单词数量加分（归一化）
        word_count = pattern.get('word_count', len(pattern.get('similar_words', [])))
        if word_count > 0:
            score += min(word_count / 100.0, 0.2)  # 最多0.2分
        
        # 认知层次适配加分
        pattern_cognitive = pattern.get('cognitive_level', concept_config['cognitive_level'])
        if user_proficiency < 50 and pattern_cognitive == 'basic':
            score += 0.1
        elif 50 <= user_proficiency < 80 and pattern_cognitive == 'intermediate':
            score += 0.1
        elif user_proficiency >= 80 and pattern_cognitive == 'advanced':
            score += 0.1
        
        return score
    
    def _enhance_pattern_with_concept_info(self, best_pattern: Dict, all_patterns: List[Dict],
                                         concept_config: Dict) -> Dict:
        """用概念组信息增强最佳pattern"""
        enhanced = best_pattern.copy()
        
        # 设置概念组标识
        enhanced['concept_group'] = concept_config.get('name', '概念整合')
        enhanced['is_concept_integrated'] = True
        enhanced['dimension_category'] = concept_config['dimension']
        enhanced['cognitive_level'] = concept_config['cognitive_level']
        
        # 合并相似单词（去重）
        all_similar_words = []
        seen_words = set()
        
        for pattern in all_patterns:
            similar_words = pattern.get('similar_words', [])
            for word in similar_words:
                word_id = word.get('word_id')
                if word_id and word_id not in seen_words:
                    all_similar_words.append(word)
                    seen_words.add(word_id)
        
        enhanced['similar_words'] = all_similar_words[:5]  # 最多5个
        
        # 增强推荐理由
        original_reason = enhanced.get('match_reason', '')
        concept_reason = f"【{concept_config['name']}】通过概念整合发现的关联模式"
        enhanced['match_reason'] = f"{concept_reason}。{original_reason}"
        
        # 提升匹配强度（概念整合的附加价值）
        original_strength = enhanced.get('match_strength', 0.5)
        enhanced['match_strength'] = min(original_strength + 0.1, 1.0)
        
        return enhanced
    
    def _get_ungrouped_patterns(self, static_patterns: List[Dict], 
                              dynamic_patterns: List[Dict]) -> List[Dict]:
        """获取未归类的独立patterns"""
        ungrouped = []
        
        for pattern in static_patterns + dynamic_patterns:
            pattern_key = (pattern.get('pattern_type'), pattern.get('pattern_value'))
            is_grouped = False
            
            for config in self._concept_mappings.values():
                if pattern_key in config['patterns']:
                    is_grouped = True
                    break
            
            if not is_grouped:
                ungrouped.append(pattern)
        
        return ungrouped
    
    def _sort_by_cognitive_hierarchy(self, patterns: List[Dict], 
                                   user_proficiency: float) -> List[Dict]:
        """按认知层次和用户水平排序patterns"""
        
        # 确定用户的认知层次偏好
        if user_proficiency < 50:
            level_priority = {'basic': 1, 'intermediate': 2, 'advanced': 3}
        elif user_proficiency < 80:
            level_priority = {'intermediate': 1, 'basic': 2, 'advanced': 3}
        else:
            level_priority = {'advanced': 1, 'intermediate': 2, 'basic': 3}
        
        def sort_key(pattern):
            cognitive_level = pattern.get('cognitive_level', 'basic')
            educational_value = pattern.get('educational_value', 
                                          pattern.get('match_strength', 0.5))
            is_concept = pattern.get('is_concept_integrated', False)
            
            return (
                0 if is_concept else 1,  # 概念整合的优先
                level_priority.get(cognitive_level, 9),  # 认知层次适配
                -educational_value,  # 教育价值降序
                -pattern.get('priority_level', 1),  # 优先级降序
                -len(pattern.get('similar_words', []))  # 相似单词数量降序
            )
        
        return sorted(patterns, key=sort_key)
    
    def _simple_deduplication(self, static_patterns: List[Dict], 
                            dynamic_patterns: List[Dict]) -> List[Dict]:
        """简单去重机制（备用方案）"""
        seen = set()
        result = []
        
        # 静态patterns优先
        for pattern in static_patterns:
            key = (pattern.get('pattern_type'), pattern.get('pattern_value'))
            if key not in seen:
                result.append(pattern)
                seen.add(key)
        
        # 添加不重复的动态patterns
        for pattern in dynamic_patterns:
            key = (pattern.get('pattern_type'), pattern.get('pattern_value'))
            if key not in seen:
                result.append(pattern)
                seen.add(key)
        
        return result
    
    def get_concept_statistics(self) -> Dict:
        """获取概念整合统计信息"""
        stats = {
            'total_concept_groups': len(self._concept_mappings),
            'concept_groups': {},
            'integration_rules': self._integration_rules
        }
        
        for group_id, config in self._concept_mappings.items():
            stats['concept_groups'][group_id] = {
                'name': config['name'],
                'pattern_count': len(config['patterns']),
                'primary_type': config['primary_type'],
                'dimension': config['dimension'],
                'cognitive_level': config['cognitive_level']
            }
        
        return stats