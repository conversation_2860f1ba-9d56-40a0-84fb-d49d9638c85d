"""
Pattern检测服务
负责检测单词的各种pattern特征并建立关联关系
"""

import re
import sqlite3
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from src.core import get_logger

logger = get_logger(__name__)


@dataclass
class PatternMatch:
    """Pattern匹配结果"""
    pattern_id: int
    pattern_type: str
    pattern_value: str
    pattern_name: str
    match_strength: float
    match_position: str
    match_reason: str
    is_primary: bool = False


class PatternDetectorService:
    """Pattern检测服务"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self._letter_combo_patterns = {}
        self._theme_patterns = {}
        self._prefix_patterns = {}
        self._suffix_patterns = {}
        self._load_patterns()
    
    def _load_patterns(self):
        """从数据库加载所有活跃的pattern"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, pattern_type, pattern_value, pattern_name, priority_level
                FROM word_patterns 
                WHERE is_active = 1
                ORDER BY priority_level DESC, word_count DESC
            """)
            
            for row in cursor.fetchall():
                pattern_id, pattern_type, pattern_value, pattern_name, priority = row
                pattern_info = {
                    'id': pattern_id,
                    'name': pattern_name,
                    'priority': priority
                }
                
                if pattern_type == 'letter_combo':
                    self._letter_combo_patterns[pattern_value] = pattern_info
                elif pattern_type == 'theme':
                    self._theme_patterns[pattern_value] = pattern_info
                elif pattern_type == 'prefix':
                    self._prefix_patterns[pattern_value] = pattern_info
                elif pattern_type == 'suffix':
                    self._suffix_patterns[pattern_value] = pattern_info
            
            conn.close()
            logger.info(f"已加载patterns: 字母组合{len(self._letter_combo_patterns)}, "
                       f"主题{len(self._theme_patterns)}, "
                       f"前缀{len(self._prefix_patterns)}, "
                       f"后缀{len(self._suffix_patterns)}")
                       
        except Exception as e:
            logger.error(f"加载patterns失败: {e}")
            raise
    
    def detect_word_patterns(self, word_id: int, word: str) -> List[PatternMatch]:
        """检测单词的所有pattern匹配"""
        word_lower = word.lower().strip()
        matches = []
        
        # 检测字母组合patterns
        matches.extend(self._detect_letter_combos(word_lower))
        
        # 检测前缀patterns
        matches.extend(self._detect_prefixes(word_lower))
        
        # 检测后缀patterns
        matches.extend(self._detect_suffixes(word_lower))
        
        # 检测主题patterns
        matches.extend(self._detect_themes(word_lower))
        
        # 标记主要patterns（优先级最高的1-2个）
        self._mark_primary_patterns(matches)
        
        return matches
    
    def _detect_letter_combos(self, word: str) -> List[PatternMatch]:
        """检测字母组合patterns"""
        matches = []
        
        for combo, pattern_info in self._letter_combo_patterns.items():
            if combo in word:
                # 计算匹配强度
                match_strength = self._calculate_combo_strength(word, combo)
                
                # 确定匹配位置
                match_position = self._get_match_position(word, combo)
                
                match = PatternMatch(
                    pattern_id=pattern_info['id'],
                    pattern_type='letter_combo',
                    pattern_value=combo,
                    pattern_name=pattern_info['name'],
                    match_strength=match_strength,
                    match_position=match_position,
                    match_reason=f"包含'{combo}'字母组合"
                )
                matches.append(match)
        
        return matches
    
    def _detect_prefixes(self, word: str) -> List[PatternMatch]:
        """检测前缀patterns"""
        matches = []
        
        for prefix, pattern_info in self._prefix_patterns.items():
            if word.startswith(prefix) and len(word) > len(prefix) + 2:
                match_strength = 0.8  # 前缀匹配强度较高
                
                match = PatternMatch(
                    pattern_id=pattern_info['id'],
                    pattern_type='prefix',
                    pattern_value=prefix,
                    pattern_name=pattern_info['name'],
                    match_strength=match_strength,
                    match_position='start',
                    match_reason=f"以'{prefix}'前缀开头"
                )
                matches.append(match)
        
        return matches
    
    def _detect_suffixes(self, word: str) -> List[PatternMatch]:
        """检测后缀patterns"""
        matches = []
        
        for suffix, pattern_info in self._suffix_patterns.items():
            if word.endswith(suffix) and len(word) > len(suffix) + 2:
                match_strength = 0.8  # 后缀匹配强度较高
                
                match = PatternMatch(
                    pattern_id=pattern_info['id'],
                    pattern_type='suffix',
                    pattern_value=suffix,
                    pattern_name=pattern_info['name'],
                    match_strength=match_strength,
                    match_position='end',
                    match_reason=f"以'{suffix}'后缀结尾"
                )
                matches.append(match)
        
        return matches
    
    def _detect_themes(self, word: str) -> List[PatternMatch]:
        """检测主题patterns"""
        matches = []
        
        # 主题词汇映射
        theme_word_mapping = self._get_theme_word_mapping()
        
        for theme, pattern_info in self._theme_patterns.items():
            if theme in theme_word_mapping and word in theme_word_mapping[theme]:
                match_strength = 1.0  # 主题匹配强度最高
                
                match = PatternMatch(
                    pattern_id=pattern_info['id'],
                    pattern_type='theme',
                    pattern_value=theme,
                    pattern_name=pattern_info['name'],
                    match_strength=match_strength,
                    match_position='whole',
                    match_reason=f"属于{pattern_info['name']}词汇"
                )
                matches.append(match)
        
        return matches
    
    def _get_theme_word_mapping(self) -> Dict[str, List[str]]:
        """获取主题词汇映射关系"""
        return {
            'time': [
                'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday',
                'january', 'february', 'march', 'april', 'may', 'june', 
                'july', 'august', 'september', 'october', 'november', 'december',
                'morning', 'afternoon', 'evening', 'night', 'today', 'tomorrow', 'yesterday'
            ],
            'colors': [
                'red', 'blue', 'green', 'yellow', 'black', 'white', 'brown', 'pink', 
                'purple', 'orange', 'grey', 'gray'
            ],
            'body_parts': [
                'head', 'hair', 'eye', 'nose', 'mouth', 'ear', 'hand', 'arm', 
                'leg', 'foot', 'back', 'finger', 'toe'
            ],
            'clothes': [
                'shirt', 't-shirt', 'skirt', 'dress', 'jacket', 'coat', 'hat', 
                'shoes', 'socks', 'pants', 'jeans', 'blouse'
            ],
            'family': [
                'father', 'mother', 'brother', 'sister', 'family', 'parent', 
                'child', 'baby', 'grandfather', 'grandmother'
            ],
            'jobs': [
                'teacher', 'doctor', 'driver', 'worker', 'player', 'singer', 
                'writer', 'hairdresser', 'actor'
            ],
            'food': [
                'apple', 'banana', 'bread', 'milk', 'water', 'rice', 'meat', 
                'fish', 'cake', 'tea', 'coffee'
            ],
            'animals': [
                'cat', 'dog', 'bird', 'fish', 'horse', 'cow', 'pig', 'sheep', 
                'lion', 'tiger', 'elephant'
            ]
        }
    
    def _calculate_combo_strength(self, word: str, combo: str) -> float:
        """计算字母组合的匹配强度"""
        # 基础强度
        base_strength = 0.6
        
        # 如果组合在单词中重复出现，增加强度
        count = word.count(combo)
        if count > 1:
            base_strength += 0.1 * (count - 1)
        
        # 如果组合长度占单词长度比例较高，增加强度
        ratio = len(combo) / len(word)
        if ratio > 0.3:
            base_strength += 0.2
        
        # 如果是在词首或词尾，稍微增加强度
        if word.startswith(combo) or word.endswith(combo):
            base_strength += 0.1
        
        return min(base_strength, 1.0)
    
    def _get_match_position(self, word: str, combo: str) -> str:
        """获取匹配位置"""
        start_pos = word.find(combo)
        word_len = len(word)
        combo_len = len(combo)
        
        if start_pos == 0:
            return 'start'
        elif start_pos + combo_len == word_len:
            return 'end'
        elif word.count(combo) > 1:
            return 'multiple'
        else:
            return 'middle'
    
    def _mark_primary_patterns(self, matches: List[PatternMatch]):
        """标记主要patterns"""
        if not matches:
            return
        
        # 按优先级和匹配强度排序
        matches.sort(key=lambda m: (
            self._get_pattern_priority(m.pattern_type),
            m.match_strength
        ), reverse=True)
        
        # 标记前1-2个为主要pattern
        primary_count = min(2, len(matches))
        for i in range(primary_count):
            matches[i].is_primary = True
    
    def _get_pattern_priority(self, pattern_type: str) -> int:
        """获取pattern类型的优先级"""
        priority_map = {
            'theme': 5,      # 主题分组优先级最高
            'suffix': 4,     # 后缀优先级较高
            'prefix': 4,     # 前缀优先级较高
            'letter_combo': 3 # 字母组合优先级中等
        }
        return priority_map.get(pattern_type, 1)
    
    def save_word_patterns(self, word_id: int, matches: List[PatternMatch]) -> bool:
        """保存单词的pattern关联关系到数据库"""
        if not matches:
            return True
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 先删除现有关联
            cursor.execute("DELETE FROM word_pattern_relations WHERE word_id = ?", (word_id,))
            
            # 插入新的关联关系
            for match in matches:
                cursor.execute("""
                    INSERT INTO word_pattern_relations 
                    (word_id, pattern_id, match_strength, match_position, match_reason, is_primary)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    word_id, match.pattern_id, match.match_strength,
                    match.match_position, match.match_reason, match.is_primary
                ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"已保存单词{word_id}的{len(matches)}个pattern关联")
            return True
            
        except Exception as e:
            logger.error(f"保存单词pattern关联失败: {e}")
            return False
    
    def process_all_words(self) -> Dict[str, int]:
        """处理所有单词，检测并保存pattern关联"""
        stats = {
            'total_words': 0,
            'processed_words': 0,
            'total_matches': 0,
            'failed_words': 0
        }
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有单词
            cursor.execute("SELECT id, english_word FROM word ORDER BY id")
            words = cursor.fetchall()
            conn.close()
            
            stats['total_words'] = len(words)
            
            for word_id, word in words:
                try:
                    # 检测patterns
                    matches = self.detect_word_patterns(word_id, word)
                    
                    # 保存到数据库
                    if self.save_word_patterns(word_id, matches):
                        stats['processed_words'] += 1
                        stats['total_matches'] += len(matches)
                    else:
                        stats['failed_words'] += 1
                        
                except Exception as e:
                    logger.error(f"处理单词 {word}(id:{word_id}) 失败: {e}")
                    stats['failed_words'] += 1
            
            logger.info(f"Pattern检测完成: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"批量处理单词失败: {e}")
            raise
    
    def get_word_patterns(self, word_id: int) -> List[Dict]:
        """获取单词的所有pattern关联"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT wp.pattern_type, wp.pattern_value, wp.pattern_name,
                       wpr.match_strength, wpr.match_position, wpr.match_reason, wpr.is_primary
                FROM word_pattern_relations wpr
                JOIN word_patterns wp ON wpr.pattern_id = wp.id
                WHERE wpr.word_id = ? AND wp.is_active = 1
                ORDER BY wpr.is_primary DESC, wpr.match_strength DESC
            """, (word_id,))
            
            results = []
            for row in cursor.fetchall():
                results.append({
                    'pattern_type': row[0],
                    'pattern_value': row[1], 
                    'pattern_name': row[2],
                    'match_strength': row[3],
                    'match_position': row[4],
                    'match_reason': row[5],
                    'is_primary': bool(row[6])
                })
            
            conn.close()
            return results
            
        except Exception as e:
            logger.error(f"获取单词patterns失败: {e}")
            return []