"""
智能资源查找工具
提供统一的音频和图片资源查找接口，支持多种命名格式的容错处理
"""

import os
import re
from pathlib import Path
from typing import Optional, List
from flask import current_app

class ResourceFinder:
    """智能资源查找器"""
    
    @staticmethod
    def normalize_word_for_filename(word: str) -> str:
        """标准化单词为文件名格式"""
        if not word:
            return ""
        
        normalized = word.lower()
        
        # 处理特殊的时间表达
        if 'a.m.' in normalized or 'p.m.' in normalized:
            normalized = normalized.replace('a.m./p.m.', 'a_m_p_m')
            normalized = normalized.replace('a.m.', 'a_m')
            normalized = normalized.replace('p.m.', 'p_m')
        
        # 标准替换
        normalized = normalized.replace(' ', '_')
        normalized = normalized.replace("'", "")
        normalized = normalized.replace('"', '')
        normalized = normalized.replace('.', '')
        normalized = normalized.replace('/', '_')
        normalized = normalized.replace('-', '_')
        normalized = normalized.replace('(', '')
        normalized = normalized.replace(')', '')
        
        # 清理多个连续下划线
        normalized = re.sub(r'_+', '_', normalized)
        normalized = normalized.strip('_')
        
        return normalized
    
    @staticmethod
    def generate_filename_candidates(word: str, extension: str) -> List[str]:
        """生成可能的文件名候选列表，按优先级排序"""
        if not word:
            return []
        
        candidates = []
        
        # 1. 标准化格式（最高优先级）
        normalized = ResourceFinder.normalize_word_for_filename(word)
        if normalized:
            candidates.append(f"{normalized}{extension}")
        
        # 2. 简单下划线替换
        underscore_version = word.lower().replace(' ', '_')
        if underscore_version != normalized and underscore_version:
            candidates.append(f"{underscore_version}{extension}")
        
        # 3. 原始格式（保留空格）
        original_lower = word.lower()
        if original_lower not in [normalized, underscore_version]:
            candidates.append(f"{original_lower}{extension}")
        
        # 4. 原始大小写
        if word != original_lower:
            candidates.append(f"{word}{extension}")
        
        # 5. 特殊处理：移除所有特殊字符但保留字母数字和下划线
        clean_version = re.sub(r'[^a-zA-Z0-9_]', '_', word.lower())
        clean_version = re.sub(r'_+', '_', clean_version).strip('_')
        if clean_version and clean_version not in [c.replace(extension, '') for c in candidates]:
            candidates.append(f"{clean_version}{extension}")
        
        # 去重但保持顺序
        seen = set()
        unique_candidates = []
        for candidate in candidates:
            if candidate not in seen:
                seen.add(candidate)
                unique_candidates.append(candidate)
        
        return unique_candidates
    
    @staticmethod
    def find_audio_file(word: str) -> Optional[str]:
        """
        智能查找音频文件
        
        Args:
            word: 单词文本
            
        Returns:
            音频文件的相对URL路径，如果找不到则返回None
        """
        if not word:
            return None
        
        try:
            audio_dir = Path(current_app.static_folder) / 'audio' / 'words'
            if not audio_dir.exists():
                return None
            
            # 生成候选文件名
            candidates = ResourceFinder.generate_filename_candidates(word, '.mp3')
            
            # 按优先级查找
            for candidate in candidates:
                file_path = audio_dir / candidate
                if file_path.exists() and file_path.is_file():
                    return f"/static/audio/words/{candidate}"
            
            # 如果都找不到，尝试其他音频格式
            for ext in ['.wav', '.ogg', '.m4a']:
                candidates = ResourceFinder.generate_filename_candidates(word, ext)
                for candidate in candidates:
                    file_path = audio_dir / candidate
                    if file_path.exists() and file_path.is_file():
                        return f"/static/audio/words/{candidate}"
            
            return None
            
        except Exception as e:
            current_app.logger.error(f"查找音频文件失败 - word: {word}, error: {e}")
            return None
    
    @staticmethod
    def find_image_file(word: str) -> Optional[str]:
        """
        智能查找图片文件
        
        Args:
            word: 单词文本
            
        Returns:
            图片文件的相对URL路径，如果找不到则返回None
        """
        if not word:
            return None
        
        try:
            image_dir = Path(current_app.static_folder) / 'images' / 'words'
            if not image_dir.exists():
                return None
            
            # 生成候选文件名
            candidates = ResourceFinder.generate_filename_candidates(word, '.jpg')
            
            # 按优先级查找
            for candidate in candidates:
                file_path = image_dir / candidate
                if file_path.exists() and file_path.is_file():
                    return f"/static/images/words/{candidate}"
            
            # 如果都找不到，尝试其他图片格式
            for ext in ['.png', '.jpeg', '.webp', '.gif']:
                candidates = ResourceFinder.generate_filename_candidates(word, ext)
                for candidate in candidates:
                    file_path = image_dir / candidate
                    if file_path.exists() and file_path.is_file():
                        return f"/static/images/words/{candidate}"
            
            # 最后尝试默认图片
            default_image = Path(current_app.static_folder) / 'images' / 'default.jpg'
            if default_image.exists():
                return "/static/images/default.jpg"
            
            return None
            
        except Exception as e:
            current_app.logger.error(f"查找图片文件失败 - word: {word}, error: {e}")
            return None
    
    @staticmethod
    def check_resource_exists(word: str) -> dict:
        """
        检查单词的所有资源是否存在
        
        Args:
            word: 单词文本
            
        Returns:
            dict: {'audio': bool, 'image': bool, 'audio_path': str|None, 'image_path': str|None}
        """
        audio_path = ResourceFinder.find_audio_file(word)
        image_path = ResourceFinder.find_image_file(word)
        
        return {
            'audio': audio_path is not None,
            'image': image_path is not None,
            'audio_path': audio_path,
            'image_path': image_path
        }
    
    @staticmethod
    def get_resource_info(word: str) -> dict:
        """
        获取单词资源的详细信息
        
        Args:
            word: 单词文本
            
        Returns:
            dict: 包含资源路径、文件大小等详细信息
        """
        result = ResourceFinder.check_resource_exists(word)
        
        # 添加文件大小信息
        if result['audio_path']:
            try:
                audio_file = Path(current_app.static_folder) / result['audio_path'].lstrip('/')
                if audio_file.exists():
                    result['audio_size'] = audio_file.stat().st_size
            except:
                result['audio_size'] = 0
        
        if result['image_path']:
            try:
                image_file = Path(current_app.static_folder) / result['image_path'].lstrip('/')
                if image_file.exists():
                    result['image_size'] = image_file.stat().st_size
            except:
                result['image_size'] = 0
        
        return result


# 兼容性函数，便于在现有代码中使用
def find_audio_file(word: str) -> Optional[str]:
    """兼容性函数：查找音频文件"""
    return ResourceFinder.find_audio_file(word)

def find_image_file(word: str) -> Optional[str]:
    """兼容性函数：查找图片文件"""
    return ResourceFinder.find_image_file(word)

def normalize_word_for_filename(word: str) -> str:
    """兼容性函数：标准化文件名"""
    return ResourceFinder.normalize_word_for_filename(word)