<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试免复习功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 免复习功能测试</h1>
        <p>点击下面的按钮测试免复习功能的用户反馈改进：</p>
        
        <button class="test-button" onclick="testSkipReviewModal()">
            测试免复习选择模态框
        </button>
        
        <div id="test-results" style="margin-top: 20px; padding: 15px; background: #f9f9f9; border-radius: 6px;">
            <h3>测试说明：</h3>
            <ul>
                <li>✅ 点击按钮后应该看到加载状态（⏳ 设置中...）</li>
                <li>✅ 设置成功后应该显示详细的单词列表和免复习截止日期</li>
                <li>✅ 按钮应该变为"✅ 确认完成"</li>
                <li>✅ 应该有顶部通知消息</li>
                <li>✅ 点击确认完成后才关闭模态框</li>
            </ul>
        </div>
    </div>

    <!-- 包含免复习选择模态框 -->
    <div id="skipReviewModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🎯 选择免复习词汇</h2>
                <span class="close" onclick="closeSkipReviewModal()">&times;</span>
            </div>
            
            <div class="modal-body">
                <!-- 消息显示区域 -->
                <div id="skipReviewMessage" class="skip-review-message" style="display: none;"></div>
                
                <!-- 学习日期显示 -->
                <div class="date-info">
                    <p>📅 学习日期：<span id="learningDate">2025-08-02</span></p>
                    <p>📊 今日学习：<span id="totalWordsCount">5</span>个单词，推荐免复习：<span id="recommendCount">3</span>个</p>
                </div>
                
                <!-- 免复习天数选择 -->
                <div class="skip-duration">
                    <label for="skipDays">免复习天数：</label>
                    <select id="skipDays">
                        <option value="3">3天</option>
                        <option value="7" selected>7天（推荐）</option>
                        <option value="14">14天</option>
                        <option value="30">30天</option>
                    </select>
                </div>
                
                <!-- 模拟单词列表 -->
                <div class="words-container">
                    <div id="wordsList" class="words-list">
                        <div class="word-item selected">
                            <input type="checkbox" id="word_1" checked onclick="toggleWordSelection(1)">
                            <div class="word-info">
                                <div class="word-main">
                                    <div class="word-english">apple</div>
                                    <div class="word-chinese">苹果</div>
                                </div>
                                <div class="word-stats">
                                    <div class="proficiency high">95.0%</div>
                                    <div class="today-performance">今日: 3/3 (100%)</div>
                                </div>
                            </div>
                        </div>
                        <div class="word-item selected">
                            <input type="checkbox" id="word_2" checked onclick="toggleWordSelection(2)">
                            <div class="word-info">
                                <div class="word-main">
                                    <div class="word-english">book</div>
                                    <div class="word-chinese">书</div>
                                </div>
                                <div class="word-stats">
                                    <div class="proficiency high">88.5%</div>
                                    <div class="today-performance">今日: 2/2 (100%)</div>
                                </div>
                            </div>
                        </div>
                        <div class="word-item selected">
                            <input type="checkbox" id="word_3" checked onclick="toggleWordSelection(3)">
                            <div class="word-info">
                                <div class="word-main">
                                    <div class="word-english">computer</div>
                                    <div class="word-chinese">电脑</div>
                                </div>
                                <div class="word-stats">
                                    <div class="proficiency high">92.3%</div>
                                    <div class="today-performance">今日: 4/4 (100%)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="modal-footer">
                <div class="selection-info">
                    已选择：<span id="selectedCount">3</span>个单词
                </div>
                
                <div class="action-buttons">
                    <button type="button" class="btn btn-secondary" onclick="closeSkipReviewModal()">
                        取消
                    </button>
                    <button type="button" class="btn btn-primary" onclick="testConfirmSkipReview()" id="confirmBtn">
                        确认设置免复习
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* 模态框样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px 12px 0 0;
        }

        .modal-body {
            padding: 20px 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
            border-radius: 0 0 12px 12px;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            opacity: 0.8;
        }

        .close:hover {
            opacity: 1;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        /* 消息显示样式 */
        .skip-review-message {
            margin-bottom: 15px;
            padding: 16px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            text-align: left;
            animation: messageSlideIn 0.3s ease-out;
            white-space: pre-line;
            line-height: 1.5;
            max-height: 300px;
            overflow-y: auto;
        }

        .skip-review-message.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #c8e6c8;
            box-shadow: 0 2px 8px rgba(46, 125, 50, 0.1);
        }

        .word-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.2s;
        }

        .word-item.selected {
            background: #f0f8ff;
            border-color: #007bff;
        }

        .word-info {
            margin-left: 12px;
            flex: 1;
        }

        .word-main {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .word-english {
            font-weight: bold;
            font-size: 16px;
        }

        .word-chinese {
            color: #666;
        }

        .word-stats {
            display: flex;
            gap: 12px;
            margin-top: 4px;
            font-size: 12px;
        }

        .proficiency.high {
            background: #4CAF50;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translate(-50%, -100%) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>

    <script>
        // 模拟数据
        let todayWords = [
            {word_id: 1, english_word: 'apple', chinese_meaning: '苹果', proficiency: 95.0},
            {word_id: 2, english_word: 'book', chinese_meaning: '书', proficiency: 88.5},
            {word_id: 3, english_word: 'computer', chinese_meaning: '电脑', proficiency: 92.3}
        ];
        let selectedWordIds = new Set([1, 2, 3]);

        function testSkipReviewModal() {
            document.getElementById('skipReviewModal').style.display = 'block';
        }

        function closeSkipReviewModal() {
            document.getElementById('skipReviewModal').style.display = 'none';
            
            // 重置消息显示
            const messageElement = document.getElementById('skipReviewMessage');
            if (messageElement) {
                messageElement.style.display = 'none';
                messageElement.className = 'skip-review-message';
                messageElement.textContent = '';
            }
            
            // 重置确认按钮
            const confirmBtn = document.getElementById('confirmBtn');
            if (confirmBtn) {
                confirmBtn.textContent = '确认设置免复习';
                confirmBtn.style.background = '';
                confirmBtn.style.color = '';
                confirmBtn.style.opacity = '1';
                confirmBtn.disabled = false;
                confirmBtn.onclick = function() { testConfirmSkipReview(); };
            }
        }

        function toggleWordSelection(wordId) {
            // 模拟切换选择
        }

        // 显示消息函数
        function showMessage(message, type = 'info') {
            const messageElement = document.getElementById('skipReviewMessage');
            if (!messageElement) return;
            
            messageElement.className = 'skip-review-message';
            if (type === 'success' || type === 'error' || type === 'warning') {
                messageElement.classList.add(type);
            }
            
            messageElement.textContent = message;
            messageElement.style.display = 'block';
        }

        // 创建临时全局消息提示
        function createTemporaryMessage(message, type) {
            const tempMessage = document.createElement('div');
            tempMessage.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                background: #e8f5e8;
                color: #2e7d32;
                border: 1px solid #c8e6c8;
            `;
            
            tempMessage.textContent = message;
            document.body.appendChild(tempMessage);
            
            setTimeout(() => {
                if (tempMessage.parentNode) {
                    tempMessage.remove();
                }
            }, 3000);
        }

        // 显示详细的成功信息
        function showDetailedSuccessMessage(result, skipDays) {
            const selectedWords = todayWords.filter(word => selectedWordIds.has(word.word_id));
            
            const skipUntilDate = new Date();
            skipUntilDate.setDate(skipUntilDate.getDate() + skipDays);
            const skipUntilStr = skipUntilDate.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
            let detailMessage = `🎉 免复习设置成功！\n\n`;
            detailMessage += `📊 设置详情：\n`;
            detailMessage += `   • 单词数量：${result.affected_count} 个\n`;
            detailMessage += `   • 免复习天数：${skipDays} 天\n`;
            detailMessage += `   • 截止日期：${skipUntilStr}\n\n`;
            detailMessage += `📝 免复习单词列表：\n`;
            
            selectedWords.forEach((word, index) => {
                const proficiency = word.proficiency ? word.proficiency.toFixed(1) : '0.0';
                detailMessage += `   ${index + 1}. ${word.english_word} - ${word.chinese_meaning} (熟练度: ${proficiency}%)\n`;
            });
            
            detailMessage += `\n💡 这些单词在 ${skipUntilStr} 之前不会出现在复习计划中。`;
            
            showMessage(detailMessage, 'success');
            
            const confirmBtn = document.getElementById('confirmBtn');
            confirmBtn.textContent = '✅ 确认完成';
            confirmBtn.style.background = '#4CAF50';
            confirmBtn.style.color = 'white';
            confirmBtn.style.fontWeight = 'bold';
            
            confirmBtn.onclick = function() {
                closeSkipReviewModal();
            };
            
            createTemporaryMessage(`🎯 成功设置 ${result.affected_count} 个单词免复习至 ${skipUntilStr}`, 'success');
        }

        // 测试确认设置免复习
        async function testConfirmSkipReview() {
            if (selectedWordIds.size === 0) {
                showMessage('请至少选择一个单词', 'warning');
                return;
            }
            
            const skipDays = parseInt(document.getElementById('skipDays').value);
            const confirmBtn = document.getElementById('confirmBtn');
            
            // 禁用按钮并显示加载状态
            confirmBtn.disabled = true;
            confirmBtn.textContent = '⏳ 设置中...';
            confirmBtn.style.opacity = '0.7';
            
            // 隐藏之前的消息
            const messageElement = document.getElementById('skipReviewMessage');
            if (messageElement) {
                messageElement.style.display = 'none';
            }
            
            // 模拟API调用延迟
            setTimeout(() => {
                // 模拟成功响应
                const result = {
                    success: true,
                    affected_count: selectedWordIds.size,
                    skip_until: '2025-08-09',
                    skip_days: skipDays
                };
                
                showDetailedSuccessMessage(result, skipDays);
            }, 1000);
        }
    </script>
</body>
</html>
