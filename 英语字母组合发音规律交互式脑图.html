<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语字母组合发音规律交互式脑图</title>
    <script src="https://unpkg.com/d3@7"></script>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        
        .controls {
            padding: 15px;
            background: #f5f5f5;
            border-bottom: 1px solid #ddd;
            text-align: center;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn.secondary {
            background: #2196F3;
        }
        
        .btn.secondary:hover {
            background: #1976D2;
        }
        
        #mindmap {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
        }
        
        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .node circle {
            fill: #fff;
            stroke: steelblue;
            stroke-width: 3px;
        }
        
        .node.level-0 circle {
            fill: #4CAF50;
            stroke: #2E7D32;
            r: 25;
        }
        
        .node.level-1 circle {
            fill: #2196F3;
            stroke: #1565C0;
            r: 20;
        }
        
        .node.level-2 circle {
            fill: #FF9800;
            stroke: #E65100;
            r: 15;
        }
        
        .node.level-3 circle {
            fill: #9C27B0;
            stroke: #6A1B9A;
            r: 12;
        }
        
        .node.level-4 circle {
            fill: #F44336;
            stroke: #C62828;
            r: 10;
        }
        
        .node text {
            font: 12px sans-serif;
            text-anchor: middle;
            fill: #333;
            pointer-events: none;
        }
        
        .node.level-0 text {
            font-size: 14px;
            font-weight: bold;
            fill: white;
        }
        
        .node.level-1 text {
            font-size: 13px;
            font-weight: bold;
            fill: white;
        }
        
        .link {
            fill: none;
            stroke: #999;
            stroke-opacity: 0.6;
            stroke-width: 2px;
        }
        
        .node:hover circle {
            stroke-width: 5px;
            filter: brightness(1.1);
        }
        
        .collapsed circle {
            fill: #ccc !important;
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            max-width: 200px;
        }
        
        .legend {
            padding: 15px;
            background: #f9f9f9;
            border-top: 1px solid #ddd;
        }
        
        .legend-item {
            display: inline-block;
            margin: 5px 15px;
            font-size: 12px;
        }
        
        .legend-color {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 5px;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 英语字母组合发音规律交互式脑图</h1>
            <p>点击节点展开/收缩 | 拖拽移动 | 滚轮缩放</p>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="expandAll()">🔍 全部展开</button>
            <button class="btn" onclick="collapseAll()">📁 全部收缩</button>
            <button class="btn secondary" onclick="resetView()">🔄 重置视图</button>
            <button class="btn secondary" onclick="exportSVG()">💾 导出图片</button>
        </div>
        
        <svg id="mindmap"></svg>
        
        <div class="legend">
            <div class="legend-item">
                <span class="legend-color" style="background: #4CAF50;"></span>
                中心节点
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background: #2196F3;"></span>
                🟢 超简单级/简单级
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background: #FF9800;"></span>
                🟡 中级
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background: #9C27B0;"></span>
                🟠 中高级
            </div>
            <div class="legend-item">
                <span class="legend-color" style="background: #F44336;"></span>
                🔴 高级
            </div>
        </div>
    </div>

    <script>
        // 数据结构
        const data = {
            name: "英语字母组合\n发音规律体系",
            level: 0,
            children: [
                {
                    name: "🟢 超简单级\n(信心建立)",
                    level: 1,
                    children: [
                        {
                            name: "AW组合 /ɔː/ 95%",
                            level: 2,
                            tooltip: "规律性最强，几乎无例外",
                            children: [
                                {
                                    name: "基础词",
                                    level: 3,
                                    children: [
                                        {name: "saw 看见", level: 4},
                                        {name: "law 法律", level: 4},
                                        {name: "raw 生的", level: 4},
                                        {name: "paw 爪子", level: 4},
                                        {name: "jaw 下巴", level: 4},
                                        {name: "claw 爪子", level: 4}
                                    ]
                                },
                                {
                                    name: "日常词",
                                    level: 3,
                                    children: [
                                        {name: "draw 画画", level: 4},
                                        {name: "awful 糟糕的", level: 4},
                                        {name: "crawl 爬行", level: 4},
                                        {name: "dawn 黎明", level: 4},
                                        {name: "lawn 草坪", level: 4},
                                        {name: "yawn 打哈欠", level: 4},
                                        {name: "hawk 鹰", level: 4}
                                    ]
                                },
                                {
                                    name: "进阶词",
                                    level: 3,
                                    children: [
                                        {name: "strawberry 草莓", level: 4},
                                        {name: "withdraw 撤回", level: 4},
                                        {name: "awkward 尴尬的", level: 4},
                                        {name: "awesome 棒极了", level: 4}
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    name: "🟢 简单级\n(基础掌握)",
                    level: 1,
                    children: [
                        {
                            name: "AI组合 /eɪ/ 75%",
                            level: 2,
                            tooltip: "主要双元音，少量例外",
                            children: [
                                {
                                    name: "主规律 /eɪ/",
                                    level: 3,
                                    children: [
                                        {name: "rain 雨", level: 4},
                                        {name: "pain 疼痛", level: 4},
                                        {name: "train 火车", level: 4},
                                        {name: "brain 大脑", level: 4},
                                        {name: "maintain 维持", level: 4},
                                        {name: "explain 解释", level: 4}
                                    ]
                                },
                                {
                                    name: "例外词",
                                    level: 3,
                                    children: [
                                        {name: "said /sed/ 说", level: 4},
                                        {name: "again /əˈɡen/ 再次", level: 4},
                                        {name: "captain /ˈkæptən/ 船长", level: 4}
                                    ]
                                }
                            ]
                        },
                        {
                            name: "EW组合 /uː/ 80%",
                            level: 2,
                            tooltip: "主要长元音，规律性强",
                            children: [
                                {
                                    name: "主规律 /uː/",
                                    level: 3,
                                    children: [
                                        {name: "new 新的", level: 4},
                                        {name: "few 少数", level: 4},
                                        {name: "grew 成长", level: 4},
                                        {name: "news 新闻", level: 4},
                                        {name: "interview 面试", level: 4},
                                        {name: "review 复习", level: 4}
                                    ]
                                },
                                {
                                    name: "例外词",
                                    level: 3,
                                    children: [
                                        {name: "sew /soʊ/ 缝纫", level: 4}
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    name: "🟡 中级\n(双模式规律)",
                    level: 1,
                    children: [
                        {
                            name: "OO组合 双主模式",
                            level: 2,
                            tooltip: "两个主要发音模式",
                            children: [
                                {
                                    name: "长音 /uː/ 60%",
                                    level: 3,
                                    children: [
                                        {name: "food 食物", level: 4},
                                        {name: "moon 月亮", level: 4},
                                        {name: "school 学校", level: 4},
                                        {name: "cool 凉爽", level: 4},
                                        {name: "smooth 光滑", level: 4},
                                        {name: "choose 选择", level: 4}
                                    ]
                                },
                                {
                                    name: "短音 /ʊ/ 35%",
                                    level: 3,
                                    children: [
                                        {name: "book 书", level: 4},
                                        {name: "look 看", level: 4},
                                        {name: "good 好的", level: 4},
                                        {name: "took 拿", level: 4},
                                        {name: "childhood 童年", level: 4},
                                        {name: "understood 理解", level: 4}
                                    ]
                                },
                                {
                                    name: "例外 /ʌ/ 5%",
                                    level: 3,
                                    children: [
                                        {name: "blood /blʌd/ 血", level: 4},
                                        {name: "flood /flʌd/ 洪水", level: 4}
                                    ]
                                }
                            ]
                        },
                        {
                            name: "OW组合 双元音对立",
                            level: 2,
                            tooltip: "两种截然不同的双元音",
                            children: [
                                {
                                    name: "开口音 /aʊ/ 70%",
                                    level: 3,
                                    children: [
                                        {name: "cow 奶牛", level: 4},
                                        {name: "how 如何", level: 4},
                                        {name: "now 现在", level: 4},
                                        {name: "down 向下", level: 4},
                                        {name: "flower 花", level: 4},
                                        {name: "power 力量", level: 4}
                                    ]
                                },
                                {
                                    name: "圆唇音 /oʊ/ 30%",
                                    level: 3,
                                    children: [
                                        {name: "show 展示", level: 4},
                                        {name: "know 知道", level: 4},
                                        {name: "grow 成长", level: 4},
                                        {name: "snow 雪", level: 4},
                                        {name: "window 窗户", level: 4},
                                        {name: "follow 跟随", level: 4}
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    name: "🟠 中高级\n(多模式环境依赖)",
                    level: 1,
                    children: [
                        {
                            name: "EA组合 4模式环境依赖",
                            level: 2,
                            tooltip: "4个发音模式，环境决定发音",
                            children: [
                                {
                                    name: "长音 /iː/ 40%",
                                    level: 3,
                                    children: [
                                        {name: "eat 吃", level: 4},
                                        {name: "tea 茶", level: 4},
                                        {name: "sea 海", level: 4},
                                        {name: "read 读", level: 4},
                                        {name: "meat 肉", level: 4},
                                        {name: "team 团队", level: 4},
                                        {name: "dream 梦想", level: 4},
                                        {name: "steam 蒸汽", level: 4}
                                    ]
                                },
                                {
                                    name: "短音 /e/ 35%",
                                    level: 3,
                                    children: [
                                        {name: "bread 面包", level: 4},
                                        {name: "head 头", level: 4},
                                        {name: "dead 死的", level: 4},
                                        {name: "heavy 重的", level: 4},
                                        {name: "ready 准备好", level: 4},
                                        {name: "weather 天气", level: 4},
                                        {name: "leather 皮革", level: 4},
                                        {name: "treasure 宝藏", level: 4}
                                    ]
                                },
                                {
                                    name: "双元音 /eɪ/ 15%",
                                    level: 3,
                                    children: [
                                        {name: "break 打破", level: 4},
                                        {name: "great 伟大的", level: 4},
                                        {name: "steak 牛排", level: 4}
                                    ]
                                },
                                {
                                    name: "卷舌音 /ɜː/ 10%",
                                    level: 3,
                                    children: [
                                        {name: "learn 学习", level: 4},
                                        {name: "earth 地球", level: 4},
                                        {name: "search 搜索", level: 4},
                                        {name: "heard 听见", level: 4}
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    name: "🔴 高级\n(复杂多样规律)",
                    level: 1,
                    children: [
                        {
                            name: "OU组合 4模式复杂",
                            level: 2,
                            tooltip: "4个发音模式，无明显环境规律",
                            children: [
                                {
                                    name: "双元音 /aʊ/ 50%",
                                    level: 3,
                                    children: [
                                        {name: "house 房子", level: 4},
                                        {name: "mouse 老鼠", level: 4},
                                        {name: "about 关于", level: 4},
                                        {name: "sound 声音", level: 4},
                                        {name: "ground 地面", level: 4},
                                        {name: "mountain 山", level: 4},
                                        {name: "thousand 千", level: 4},
                                        {name: "pronounce 发音", level: 4}
                                    ]
                                },
                                {
                                    name: "长音 /uː/ 25%",
                                    level: 3,
                                    children: [
                                        {name: "soup 汤", level: 4},
                                        {name: "group 组", level: 4},
                                        {name: "through 通过", level: 4},
                                        {name: "youth 青年", level: 4},
                                        {name: "route 路线", level: 4},
                                        {name: "wound 伤口", level: 4},
                                        {name: "troupe 剧团", level: 4},
                                        {name: "coupon 优惠券", level: 4}
                                    ]
                                },
                                {
                                    name: "短音 /ʌ/ 20%",
                                    level: 3,
                                    children: [
                                        {name: "country 国家", level: 4},
                                        {name: "young 年轻", level: 4},
                                        {name: "trouble 麻烦", level: 4},
                                        {name: "double 双倍", level: 4},
                                        {name: "touch 触摸", level: 4},
                                        {name: "southern 南方的", level: 4},
                                        {name: "cousin 表兄弟", level: 4},
                                        {name: "couple 夫妇", level: 4}
                                    ]
                                },
                                {
                                    name: "长音 /ɔː/ 5%",
                                    level: 3,
                                    children: [
                                        {name: "four 四", level: 4},
                                        {name: "pour 倒", level: 4},
                                        {name: "court 法庭", level: 4},
                                        {name: "source 来源", level: 4},
                                        {name: "course 课程", level: 4}
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        // SVG设置
        const width = 1160;
        const height = 600;
        const svg = d3.select("#mindmap")
            .attr("width", width)
            .attr("height", height);

        const g = svg.append("g");

        // 缩放功能
        const zoom = d3.zoom()
            .scaleExtent([0.1, 3])
            .on("zoom", (event) => {
                g.attr("transform", event.transform);
            });

        svg.call(zoom);

        // 树布局
        const tree = d3.tree().size([height - 100, width - 200]);

        // 根节点
        let root = d3.hierarchy(data);
        root.x0 = height / 2;
        root.y0 = 0;

        let i = 0;
        const duration = 750;

        // 初始化：收缩除了第一层的所有节点
        if (root.children) {
            root.children.forEach(collapse);
        }

        // 初始视图设置
        svg.call(zoom.transform, d3.zoomIdentity.translate(100, height/2).scale(0.8));

        update(root);

        function collapse(d) {
            if (d.children) {
                d._children = d.children;
                d._children.forEach(collapse);
                d.children = null;
            }
        }

        function update(source) {
            const treeData = tree(root);
            const nodes = treeData.descendants();
            const links = treeData.descendants().slice(1);

            // 标准化固定深度
            nodes.forEach(d => {
                d.y = d.depth * 180;
            });

            // 节点
            const node = g.selectAll('g.node')
                .data(nodes, d => d.id || (d.id = ++i));

            const nodeEnter = node.enter().append('g')
                .attr('class', d => `node level-${d.data.level}`)
                .attr("transform", d => `translate(${source.y0},${source.x0})`)
                .on('click', click);

            nodeEnter.append('circle')
                .attr('r', 1e-6);

            nodeEnter.append('text')
                .attr("dy", ".35em")
                .attr("x", d => d.children || d._children ? -13 : 13)
                .attr("text-anchor", d => d.children || d._children ? "end" : "start")
                .text(d => d.data.name);

            // 工具提示
            nodeEnter.on("mouseover", function(event, d) {
                if (d.data.tooltip) {
                    const tooltip = d3.select("body").append("div")
                        .attr("class", "tooltip")
                        .style("opacity", 0);

                    tooltip.transition()
                        .duration(200)
                        .style("opacity", .9);

                    tooltip.html(d.data.tooltip)
                        .style("left", (event.pageX + 10) + "px")
                        .style("top", (event.pageY - 28) + "px");
                }
            })
            .on("mouseout", function() {
                d3.selectAll(".tooltip").remove();
            });

            const nodeUpdate = nodeEnter.merge(node);

            nodeUpdate.transition()
                .duration(duration)
                .attr("transform", d => `translate(${d.y},${d.x})`);

            nodeUpdate.select('circle')
                .attr('r', d => {
                    const sizes = [25, 20, 15, 12, 10];
                    return sizes[d.data.level] || 8;
                })
                .attr('class', d => d._children ? 'collapsed' : '');

            const nodeExit = node.exit().transition()
                .duration(duration)
                .attr("transform", d => `translate(${source.y},${source.x})`)
                .remove();

            nodeExit.select('circle')
                .attr('r', 1e-6);

            nodeExit.select('text')
                .style('fill-opacity', 1e-6);

            // 链接
            const link = g.selectAll('path.link')
                .data(links, d => d.id);

            const linkEnter = link.enter().insert('path', "g")
                .attr("class", "link")
                .attr('d', d => {
                    const o = {x: source.x0, y: source.y0};
                    return diagonal(o, o);
                });

            const linkUpdate = linkEnter.merge(link);

            linkUpdate.transition()
                .duration(duration)
                .attr('d', d => diagonal(d, d.parent));

            const linkExit = link.exit().transition()
                .duration(duration)
                .attr('d', d => {
                    const o = {x: source.x, y: source.y};
                    return diagonal(o, o);
                })
                .remove();

            nodes.forEach(d => {
                d.x0 = d.x;
                d.y0 = d.y;
            });
        }

        function click(event, d) {
            if (d.children) {
                d._children = d.children;
                d.children = null;
            } else {
                d.children = d._children;
                d._children = null;
            }
            update(d);
        }

        function diagonal(s, d) {
            return `M ${s.y} ${s.x}
                    C ${(s.y + d.y) / 2} ${s.x},
                      ${(s.y + d.y) / 2} ${d.x},
                      ${d.y} ${d.x}`;
        }

        // 控制函数
        function expandAll() {
            function expand(d) {
                if (d._children) {
                    d.children = d._children;
                    d._children = null;
                }
                if (d.children) {
                    d.children.forEach(expand);
                }
            }
            expand(root);
            update(root);
        }

        function collapseAll() {
            function collapse(d) {
                if (d.children) {
                    d._children = d.children;
                    d._children.forEach(collapse);
                    d.children = null;
                }
            }
            root.children.forEach(collapse);
            update(root);
        }

        function resetView() {
            svg.transition().duration(750).call(
                zoom.transform,
                d3.zoomIdentity.translate(100, height/2).scale(1)
            );
        }

        function exportSVG() {
            const svgData = new XMLSerializer().serializeToString(document.getElementById("mindmap"));
            const canvas = document.createElement("canvas");
            const ctx = canvas.getContext("2d");
            const img = new Image();
            
            canvas.width = width;
            canvas.height = height;
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0);
                const link = document.createElement("a");
                link.download = "英语发音规律脑图.png";
                link.href = canvas.toDataURL();
                link.click();
            };
            
            img.src = "data:image/svg+xml;base64," + btoa(unescape(encodeURIComponent(svgData)));
        }
    </script>
</body>
</html>
