# Pattern推荐系统完整列表

## 概述

Word Learning App v5的Pattern推荐系统采用多层次认知架构，结合静态数据库存储和动态算法检测，为用户提供智能化的单词学习关联推荐。

## 静态Pattern (数据库存储)

### 字母组合类 (LETTER_COMBO)

- **含ai字母组合** - 包含ai字母组合的单词，如air, train, captain等 (35个单词)
- **含ar字母组合** - 包含ar字母组合的单词，如march, area, art等，常见于月份和名词 (70个单词)
- **含ch字母组合** - 包含ch字母组合的单词，如chinese, lunch, much等 (38个单词)
- **含ea字母组合** - 包含ea字母组合的单词，如beach, bread, breakfast等 (51个单词)
- **含er字母组合** - 包含er字母组合的单词，如teacher, player, sister等，多为职业或比较级 (103个单词)
- **含ir字母组合** - 包含ir字母组合的单词，如girl, hair, shirt, chair等 (27个单词)
- **含ly字母组合** - 包含ly字母组合的单词，多为副词如usually, really等 (8个单词)
- **含oo字母组合** - 包含oo字母组合的单词，如book, afternoon, bedroom等 (36个单词)
- **含or字母组合** - 包含or字母组合的单词，如history, actor, doctor等 (53个单词)
- **含ou字母组合** - 包含ou字母组合的单词，如cloud, country, cousin等 (41个单词)
- **含th字母组合** - 包含th字母组合的单词，如thursday, bath, birthday等 (61个单词)
- **含ur字母组合** - 包含ur字母组合的单词，如saturday, adventure, burger等 (38个单词)

### 前缀类 (PREFIX)

- **re前缀** - 以re开头的单词，表示重复，如repair (7个单词)
- **un前缀** - 以un开头的单词，表示否定，如unhappy (5个单词)

### 后缀类 (SUFFIX)

- **ed后缀** - 以ed结尾的单词，如surprised, tired, excited等 (9个单词)
- **ing后缀** - 以ing结尾的单词，如something, interesting, swimming等 (20个单词)

### 主题类 (THEME)

- **动物类** - 动物相关词汇：fish, lion, pig等 (3个单词)
- **家庭关系** - 家庭关系词汇：family, brother, sister等 (5个单词)
- **时间相关** - 时间相关词汇：星期、月份、时间概念等 (26个单词)
- **职业类** - 职业相关词汇：teacher, doctor, driver等 (9个单词)
- **衣物类** - 衣物相关词汇：shirt, dress, shoes等 (12个单词)
- **身体部位** - 身体部位词汇：head, hand, hair等 (9个单词)
- **颜色类** - 颜色相关词汇：red, blue, green等 (10个单词)
- **食物类** - 食物相关词汇：apple, bread, coffee等 (10个单词)

## 动态Pattern (算法检测)

### 发音规律类 (PHONETIC)

#### 双元音检测
- **ou双元音** - 发音/aʊ/，如house, about, cloud
- **ai双元音** - 发音/eɪ/，如rain, pain, train  
- **ee双元音** - 发音/iː/，如see, tree, free
- **oo双元音** - 发音/uː/或/ʊ/，如moon, book, good
- **ea双元音** - 发音/iː/或/e/，如read, bread, beach

#### 不发音字母检测 (SILENT_LETTER)
- **gh不发音组合** - 如daughter, night, eight中的gh
- **augh不发音组合** - 如naughty, daughter中的augh
- **mb不发音** - 如lamb, comb中的b
- **kn不发音** - 如knee, knife中的k

#### 开音节结构 (SYLLABLE)
- **开音节检测** - CV(辅音+元音)结构，如be, go, no
- **闭音节检测** - CVC结构，如cat, dog, big
- **音节分割** - 帮助理解单词发音规律

### 押韵音韵类 (RHYME)

- **ing尾音** - 如sing, ring, thing, swimming
- **er尾音** - 如teacher, water, father
- **ly尾音** - 如quickly, slowly, really
- **ed尾音** - 如played, worked, studied
- **ay尾音** - 如day, play, say
- **ight尾音** - 如night, light, right
- **动态押韵检测** - 基于音节末尾自动识别押韵模式

### 语义关联类 (SEMANTIC)

#### 主题语义检测
- **颜色语义** - 基于中文含义检测颜色相关词汇
- **动物语义** - 检测动物、宠物相关词汇  
- **食物语义** - 检测食物、饮料、餐具相关词汇
- **时间语义** - 检测时间、日期、季节相关词汇
- **情感语义** - 检测情感、感受相关词汇
- **学习语义** - 检测学习、教育相关词汇
- **家庭语义** - 检测家庭、亲属关系词汇

### 词汇变形类

#### 同义词检测 (SYNONYM)
- **good同义词组** - great, excellent, wonderful, nice, fine
- **big同义词组** - large, huge, giant, enormous  
- **fast同义词组** - quick, rapid, speedy, swift
- **happy同义词组** - glad, joyful, pleased, cheerful
- **beautiful同义词组** - pretty, lovely, attractive, gorgeous
- **smart同义词组** - clever, intelligent, bright, wise

#### 反义词检测 (ANTONYM)  
- **good反义词组** - bad, terrible, awful, horrible
- **big反义词组** - small, little, tiny, mini
- **hot反义词组** - cold, cool, freezing, chilly
- **new反义词组** - old, ancient, worn, aged
- **fast反义词组** - slow, sluggish, gradual
- **happy反义词组** - sad, unhappy, upset, blue

#### 形容词变形 (ADJECTIVE_FORMS)
- **规则比较级** - big→bigger→biggest, small→smaller→smallest
- **不规则比较级** - good→better→best, bad→worse→worst  
- **多音节形容词** - beautiful→more beautiful→most beautiful
- **特殊变化** - many→more→most, little→less→least

#### 名词复数 (PLURAL_FORMS)
- **规则复数** - book→books, cat→cats, dog→dogs
- **es复数** - box→boxes, class→classes, watch→watches
- **y变ies** - baby→babies, city→cities, family→families
- **f变ves** - knife→knives, leaf→leaves, shelf→shelves
- **不规则复数** - man→men, child→children, foot→feet, mouse→mice

### 词根词缀类 (MORPHOLOGY)

#### 前缀检测
- **un-前缀** - unhappy, uncomfortable, unusual (否定)
- **re-前缀** - replay, return, repeat (重新)
- **pre-前缀** - preview, prepare, predict (预先)  
- **dis-前缀** - disagree, disappear, dislike (否定/分离)
- **mis-前缀** - mistake, misunderstand (错误)
- **over-前缀** - overcome, overhead (超过)

#### 后缀检测
- **-ing后缀** - running, swimming, reading (进行/动名词)
- **-ed后缀** - played, worked, studied (过去式/过去分词)
- **-er后缀** - teacher, player, worker (人/比较级)
- **-ly后缀** - quickly, slowly, really (副词)
- **-tion后缀** - education, information, station (名词)
- **-ness后缀** - happiness, kindness, darkness (名词)

#### 词根检测
- **常见拉丁词根** - spect(看), dict(说), port(运)
- **希腊词根** - photo(光), bio(生命), geo(地)
- **词根变形** - 识别词根在不同单词中的变化

### 搭配模式类 (COLLOCATION)

#### 时间表达搭配
- **星期搭配** - Monday, Tuesday, Wednesday...的使用场景
- **月份搭配** - January, February, March...的常用表达
- **时间词搭配** - morning, afternoon, evening, night的搭配

#### 学校场景搭配  
- **学习用品** - book, pen, pencil, desk, chair的搭配
- **学科搭配** - math, English, science, history的使用
- **校园活动** - class, lesson, homework, test的搭配

#### 家庭成员搭配
- **核心家庭** - father, mother, brother, sister的关系
- **扩展家庭** - grandfather, grandmother, uncle, aunt的使用
- **家庭活动** - 家庭成员间的常见活动搭配

## Pattern认知层次架构

### 基础感知层 (Basic)
**目标**: 直接的视觉和听觉感知  
**包含**: 字母组合、发音规律、押韵模式  
**特点**: 感官直接识别，学习难度低  

- 字母组合识别 (Letter Combination)
- 基础发音规律 (Basic Phonetics)  
- 简单押韵模式 (Simple Rhymes)

### 语义关联层 (Intermediate)  
**目标**: 意义理解和关联建立  
**包含**: 语义分类、主题关联、情景搭配  
**特点**: 需要理解力，建立概念连接  

- 主题语义分类 (Semantic Categories)
- 情景关联 (Contextual Associations)
- 基础搭配模式 (Basic Collocations)

### 高级应用层 (Advanced)
**目标**: 深层语法规律和应用能力  
**包含**: 词根词缀、语法变形、复杂搭配  
**特点**: 需要语法知识，应用能力强  

- 词汇变形规律 (Word Formation)
- 词根词缀分析 (Morphological Analysis)  
- 高级搭配模式 (Advanced Collocations)
- 同义反义关系 (Synonym/Antonym Relations)

## 智能特性

### 去重机制
- **语义等价检测** - 识别不同类型但语义相同的patterns
- **ER概念统一** - ER后缀、ER双元音、ER字母组合视为同一概念
- **ING概念合并** - ING后缀、NG尾音统一为ING概念  
- **家庭概念整合** - 家庭主题、家庭语义、家庭搭配合并

### 个性化推荐
- **学习进度适配** - 基于用户已学词汇进行pattern匹配
- **熟练度权重** - 结合单词熟练度调整推荐优先级  
- **新词包含策略** - 默认包含"new"状态单词扩大推荐范围
- **相似度阈值** - 只推荐相似度>0.3的高质量匹配

### 动态算法
- **实时检测** - 动态分析单词特征生成patterns
- **多层次匹配** - 从字面到语义的多层次关联分析
- **上下文感知** - 基于用户学习上下文调整推荐策略
- **质量评估** - 自动评估pattern质量和学习价值

## 推荐优先级

1. **静态Pattern** (优先级: 高)  
   - 数据库预定义，质量可控
   - 经过人工筛选和验证
   - 包含准确的描述和分类

2. **动态Pattern** (优先级: 中)
   - 算法实时检测，覆盖面广
   - 能发现静态数据未涵盖的关联
   - 个性化程度高

3. **去重合并** (优先级: 最高)
   - 避免冗余信息干扰学习
   - 提供最精炼的pattern建议
   - 优先保留高价值patterns

## 使用指导

### 教师使用建议
- 根据学生水平选择合适认知层次的patterns  
- 利用主题分类进行词汇教学设计
- 结合发音规律进行语音教学

### 学生学习建议  
- 从基础感知层开始，逐步提升到高级应用层
- 重点关注高频字母组合和发音规律
- 利用同义反义词扩展词汇量
- 通过搭配模式提升语言运用能力

### 自适应学习
- 系统会根据学习进度自动调整推荐内容
- 高熟练度单词将推荐更高级的patterns  
- 新词汇重点推荐基础感知层patterns
- 个性化推荐基于个人学习特点

---

*最后更新: 2025-01-26*  
*系统版本: Word Learning App v5*