# 单词学习优先级系统文档

## 系统概述

本文档记录了Word Learning App v5中实现的优先级系统，确保新增单词模块（如W24-W40）能够优先被安排到学习计划中。

## 优先级设计

### 优先级分级
- **Priority 1 (最高)**: W1-W22 (已完成模块，优先复习)
- **Priority 2 (中高)**: W24-W40 (新增模块，优先学习)
- **Priority 3 (最低)**: 主题单词 (辅助学习，recognition模式)

### 学习要求分级
- **spelling**: 需要拼写练习的单词
- **recognition**: 只需要识别的单词

## 技术实现

### 数据库结构

#### word表新增字段
```sql
ALTER TABLE word ADD COLUMN priority INTEGER DEFAULT 1;
CREATE INDEX idx_word_priority ON word(priority, section);
```

#### 优先级分配
```sql
-- W1-W22: Priority 1, spelling
UPDATE word SET priority = 1 WHERE section LIKE 'w%' AND section GLOB 'w[0-9]*' AND CAST(SUBSTR(section, 2) AS INTEGER) <= 22;

-- W24-W40: Priority 2, spelling/recognition混合
UPDATE word SET priority = 2 WHERE section LIKE 'w%' AND section GLOB 'w[0-9]*' AND CAST(SUBSTR(section, 2) AS INTEGER) >= 24;

-- 主题单词: Priority 3, recognition
UPDATE word SET priority = 3 WHERE NOT (section LIKE 'w%' AND section GLOB 'w[0-9]*');
```

### 学习计划生成逻辑修改

#### 关键修改的方法
1. `_select_new_words()` - 新词选择
2. `_select_review_words()` - 复习词选择  
3. `_select_review_words_for_new_positions()` - 复习词补充
4. `_select_attention_or_new_words()` - 注意力词和新词补充

#### 排序逻辑
所有学习计划生成查询都采用统一的排序规则：
```sql
ORDER BY w.priority ASC, [其他条件], w.id ASC
```

## 新单词导入标准流程

### 1. 数据准备
```sql
-- 导入新单词到word表，设置正确的priority和learning_requirement
INSERT INTO word (english_word, chinese_meaning, section, learning_requirement, priority) VALUES
('word', 'meaning', 'w41', 'spelling', 2);
```

### 2. 用户记录初始化
```sql
-- 为所有用户创建user_word记录
INSERT INTO user_word (user_id, word_id, learning_count, correct_count, status, proficiency)
SELECT u.id, w.id, 0, 0, 'new', 0.0
FROM user u, word w 
WHERE w.section = 'w41' 
AND NOT EXISTS (SELECT 1 FROM user_word uw WHERE uw.user_id = u.id AND uw.word_id = w.id);
```

### 3. 验证检查
```sql
-- 验证优先级设置
SELECT priority, COUNT(*) FROM word WHERE section = 'w41' GROUP BY priority;

-- 验证用户记录
SELECT COUNT(*) FROM user_word uw JOIN word w ON uw.word_id = w.id WHERE w.section = 'w41';
```

## 监控和维护

### 日常监控查询

#### 1. 优先级分布检查
```sql
SELECT 
    w.priority,
    CASE 
        WHEN w.section LIKE 'w%' AND w.section GLOB 'w[0-9]*' THEN 'W系列'
        ELSE '主题单词'
    END as type,
    COUNT(*) as count
FROM word w
GROUP BY w.priority, type
ORDER BY w.priority;
```

#### 2. 学习计划优先级验证
```sql
SELECT 
    DATE(lp.planned_date) as date,
    w.priority,
    lp.item_type,
    COUNT(*) as count
FROM learning_plan lp
JOIN word w ON lp.word_id = w.id
WHERE lp.planned_date >= DATE('now', '-7 days')
GROUP BY DATE(lp.planned_date), w.priority, lp.item_type
ORDER BY date DESC, w.priority;
```

#### 3. 新词优先级检查
```sql
-- 检查新词是否按优先级顺序选择
SELECT w.section, w.priority, COUNT(*) as selected_count
FROM learning_plan lp
JOIN word w ON lp.word_id = w.id
WHERE lp.planned_date = DATE('now') 
  AND lp.item_type = 'new'
  AND lp.user_id = ?
GROUP BY w.section, w.priority
ORDER BY w.priority, w.section;
```

## 故障排除

### 常见问题及解决方案

#### 1. 新单词没有被优先选择
**原因**: user_word记录缺失或priority设置错误
**解决**: 
```sql
-- 检查缺失的user_word记录
SELECT w.id, w.english_word, w.section 
FROM word w 
LEFT JOIN user_word uw ON w.id = uw.word_id AND uw.user_id = ?
WHERE uw.word_id IS NULL AND w.priority = 2;

-- 补充缺失记录
INSERT INTO user_word (user_id, word_id, learning_count, correct_count, status, proficiency)
SELECT ?, w.id, 0, 0, 'new', 0.0
FROM word w 
LEFT JOIN user_word uw ON w.id = uw.word_id AND uw.user_id = ?
WHERE uw.word_id IS NULL AND w.priority = 2;
```

#### 2. 优先级排序不生效
**原因**: SQL查询中缺少priority排序
**解决**: 检查plan_service.py中所有查询是否包含 `ORDER BY w.priority ASC`

#### 3. 数据不一致
**原因**: 部分单词priority字段为NULL或错误值
**解决**:
```sql
-- 检查异常priority值
SELECT priority, COUNT(*) FROM word GROUP BY priority;

-- 修复NULL值
UPDATE word SET priority = 3 WHERE priority IS NULL;
```

## 未来扩展指南

### 添加新的单词模块（如W41-W60）
1. 设置priority = 2（与W24-W40同级）或priority = 1.5（更高优先级）
2. 遵循标准导入流程
3. 验证学习计划生成逻辑

### 优先级系统升级
如需更复杂的优先级（如小数优先级），可考虑：
- 将priority字段改为DECIMAL类型
- 添加sub_priority字段进行细分
- 实现动态优先级调整机制

## 版本记录

- **v1.0** (2025-07-27): 初始优先级系统实现
  - 添加priority字段
  - 修改5个查询方法的排序逻辑
  - 完成W24-W40单词导入和优先级设置

---

**重要提醒**: 任何涉及学习计划生成的代码修改都应考虑priority字段的影响，确保新单词能够按预期优先级被选择。