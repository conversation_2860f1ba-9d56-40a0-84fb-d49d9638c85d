# =============================================================================
# Docker 构建忽略文件
# =============================================================================

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.mypy_cache/

# 开发环境
.vscode/
.idea/
*.swp
*.swo
*~

# 测试和覆盖率
.coverage
.pytest_cache/
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 文档
*.md
docs/
*.sh

# Git
.git/
.gitignore

# Docker
Dockerfile*
.dockerignore
docker-compose*.yml
*.tar
word_learning_app_v5*.tar

# 日志
*.log
logs/

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 依赖管理
requirements-dev.txt
DEPENDENCIES.md

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 备份文件
*.bak
*.backup

# 开发数据库（保留实例数据库）
# instance/
*.db-journal

# 静态文件缓存（可选）
# static/cache/

# 导出文件
static/exports/*
!static/exports/.gitkeep 