<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词导入管理</title>
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        .import-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .tab-container {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background: #f5f5f5;
            border: 1px solid #e0e0e0;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        
        .tab.active {
            background: white;
            border-bottom: 2px solid white;
            margin-bottom: -2px;
        }
        
        .tab-content {
            display: none;
            background: white;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 0 5px 5px 5px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .upload-area {
            border: 2px dashed #4CAF50;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f9f9f9;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .upload-area:hover {
            background: #e8f5e9;
        }
        
        .upload-area.dragging {
            background: #c8e6c9;
            border-color: #2e7d32;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .btn {
            padding: 10px 20px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn-secondary {
            background: #757575;
        }
        
        .btn-secondary:hover {
            background: #616161;
        }
        
        .result-message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            display: none;
        }
        
        .result-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .result-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .history-table th, .history-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .history-table th {
            background: #f5f5f5;
            font-weight: bold;
        }
        
        .json-editor {
            font-family: 'Courier New', monospace;
            min-height: 200px;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        
        .progress-fill {
            height: 100%;
            background: #4CAF50;
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="import-container">
        <h1>📚 单词导入管理</h1>
        
        <div class="tab-container">
            <div class="tab active" onclick="showTab('csv')">CSV文件导入</div>
            <div class="tab" onclick="showTab('single')">单个添加</div>
            <div class="tab" onclick="showTab('json')">JSON批量导入</div>
            <div class="tab" onclick="showTab('history')">导入历史</div>
        </div>
        
        <!-- CSV导入 -->
        <div id="csv-tab" class="tab-content active">
            <h2>CSV文件导入</h2>
            <p>上传CSV文件批量导入单词。CSV文件必须包含 word（单词）和 answer（答案）列，可选 section（分类）列。</p>
            
            <div class="upload-area" id="upload-area">
                <p>📁 拖拽CSV文件到这里，或点击选择文件</p>
                <input type="file" id="csv-file" accept=".csv" style="display: none;">
            </div>
            
            <div class="progress-bar" id="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            
            <div class="result-message" id="csv-result"></div>
            
            <div style="margin-top: 20px;">
                <button class="btn btn-secondary" onclick="downloadTemplate()">📥 下载CSV模板</button>
            </div>
        </div>
        
        <!-- 单个添加 -->
        <div id="single-tab" class="tab-content">
            <h2>添加单个单词</h2>
            <form id="single-form">
                <div class="form-group">
                    <label for="single-word">单词（中文）*</label>
                    <input type="text" id="single-word" required placeholder="例如：苹果">
                </div>
                
                <div class="form-group">
                    <label for="single-answer">答案（英文）*</label>
                    <input type="text" id="single-answer" required placeholder="例如：apple">
                </div>
                
                <div class="form-group">
                    <label for="single-section">分类</label>
                    <input type="text" id="single-section" list="section-options" placeholder="输入或选择分类" value="新增">
                    <datalist id="section-options">
                        <option value="基础">
                        <option value="进阶">
                        <option value="高级">
                        <option value="3年级">
                        <option value="4年级">
                        <option value="5年级">
                        <option value="6年级">
                        <option value="新增">
                    </datalist>
                </div>
                
                <button type="submit" class="btn">➕ 添加单词</button>
            </form>
            
            <div class="result-message" id="single-result"></div>
        </div>
        
        <!-- JSON导入 -->
        <div id="json-tab" class="tab-content">
            <h2>JSON批量导入</h2>
            <p>使用JSON格式批量导入单词。格式示例：</p>
            <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px;">
{
  "words": [
    {"word": "苹果", "answer": "apple", "section": "基础"},
    {"word": "香蕉", "answer": "banana", "section": "基础"},
    {"word": "橙子", "answer": "orange", "section": "基础"}
  ]
}
            </pre>
            
            <div class="form-group">
                <label for="json-input">JSON数据</label>
                <textarea id="json-input" class="json-editor" rows="10" placeholder='{"words": [...]}'></textarea>
            </div>
            
            <button class="btn" onclick="importJSON()">📤 导入JSON</button>
            
            <div class="result-message" id="json-result"></div>
        </div>
        
        <!-- 导入历史 -->
        <div id="history-tab" class="tab-content">
            <h2>导入历史</h2>
            <button class="btn" onclick="loadHistory()">🔄 刷新历史</button>
            
            <table class="history-table" id="history-table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>类型</th>
                        <th>新增单词</th>
                        <th>更新单词</th>
                        <th>错误数</th>
                        <th>来源</th>
                    </tr>
                </thead>
                <tbody id="history-body">
                    <tr>
                        <td colspan="6" style="text-align: center;">点击"刷新历史"加载数据</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        // 显示标签页
        function showTab(tabName) {
            // 隐藏所有标签页
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }
        
        // CSV上传区域
        const uploadArea = document.getElementById('upload-area');
        const csvFile = document.getElementById('csv-file');
        
        uploadArea.addEventListener('click', () => {
            csvFile.click();
        });
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragging');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragging');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragging');
            
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].name.endsWith('.csv')) {
                handleCSVUpload(files[0]);
            } else {
                showResult('csv-result', 'error', '请上传CSV文件');
            }
        });
        
        csvFile.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleCSVUpload(e.target.files[0]);
            }
        });
        
        // 处理CSV上传
        function handleCSVUpload(file) {
            const formData = new FormData();
            formData.append('file', file);
            
            // 显示进度条
            const progressBar = document.getElementById('progress-bar');
            const progressFill = document.getElementById('progress-fill');
            progressBar.style.display = 'block';
            progressFill.style.width = '50%';
            
            fetch('/api/words/import/csv', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                progressFill.style.width = '100%';
                setTimeout(() => {
                    progressBar.style.display = 'none';
                    progressFill.style.width = '0%';
                }, 1000);
                
                if (data.success) {
                    let message = data.message;
                    if (data.errors && data.errors.length > 0) {
                        message += '<br><br>错误详情:<br>' + data.errors.join('<br>');
                    }
                    showResult('csv-result', 'success', message);
                } else {
                    showResult('csv-result', 'error', data.error);
                }
            })
            .catch(error => {
                progressBar.style.display = 'none';
                showResult('csv-result', 'error', '上传失败: ' + error);
            });
        }
        
        // 单个添加表单
        document.getElementById('single-form').addEventListener('submit', (e) => {
            e.preventDefault();
            
            const data = {
                word: document.getElementById('single-word').value,
                answer: document.getElementById('single-answer').value,
                section: document.getElementById('single-section').value
            };
            
            fetch('/api/words/add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult('single-result', 'success', data.message);
                    document.getElementById('single-form').reset();
                } else {
                    showResult('single-result', 'error', data.error);
                }
            })
            .catch(error => {
                showResult('single-result', 'error', '添加失败: ' + error);
            });
        });
        
        // JSON导入
        function importJSON() {
            const jsonInput = document.getElementById('json-input').value;
            
            try {
                const data = JSON.parse(jsonInput);
                
                fetch('/api/words/import/json', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let message = data.message;
                        if (data.errors && data.errors.length > 0) {
                            message += '<br><br>错误详情:<br>' + data.errors.join('<br>');
                        }
                        showResult('json-result', 'success', message);
                    } else {
                        showResult('json-result', 'error', data.error);
                    }
                })
                .catch(error => {
                    showResult('json-result', 'error', '导入失败: ' + error);
                });
            } catch (e) {
                showResult('json-result', 'error', 'JSON格式错误: ' + e.message);
            }
        }
        
        // 加载历史
        function loadHistory() {
            fetch('/api/words/history')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const tbody = document.getElementById('history-body');
                        tbody.innerHTML = '';
                        
                        if (data.history.length === 0) {
                            tbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">暂无导入历史</td></tr>';
                        } else {
                            data.history.forEach(record => {
                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${new Date(record.timestamp).toLocaleString('zh-CN')}</td>
                                    <td>${record.type.toUpperCase()}</td>
                                    <td>${record.new_words}</td>
                                    <td>${record.updated_words}</td>
                                    <td>${record.errors}</td>
                                    <td>${record.filename || record.user || '-'}</td>
                                `;
                                tbody.appendChild(row);
                            });
                        }
                    }
                })
                .catch(error => {
                    console.error('加载历史失败:', error);
                });
        }
        
        // 下载模板
        function downloadTemplate() {
            window.location.href = '/api/words/template';
        }
        
        // 显示结果消息
        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = 'result-message ' + type;
            element.innerHTML = message;
            element.style.display = 'block';
            
            // 5秒后自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    element.style.display = 'none';
                }, 5000);
            }
        }
    </script>
</body>
</html> 