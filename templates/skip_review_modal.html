<!-- 免复习选择模态框 -->
<div id="skipReviewModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2>🎯 选择免复习词汇</h2>
            <span class="close" onclick="closeSkipReviewModal()">&times;</span>
        </div>
        
        <div class="modal-body">
            <!-- 学习日期显示 -->
            <div class="date-info">
                <p>📅 学习日期：<span id="learningDate"></span></p>
                <p>📊 今日学习：<span id="totalWordsCount">0</span>个单词，推荐免复习：<span id="recommendCount">0</span>个</p>
            </div>
            
            <!-- 快速操作按钮 -->
            <div class="quick-actions">
                <button type="button" class="btn btn-outline" onclick="selectRecommended()">
                    ⭐ 选择推荐词汇
                </button>
                <button type="button" class="btn btn-outline" onclick="selectByProficiency(90)">
                    🏆 选择熟练度≥90%
                </button>
                <button type="button" class="btn btn-outline" onclick="selectByProficiency(80)">
                    ✨ 选择熟练度≥80%
                </button>
                <button type="button" class="btn btn-outline" onclick="selectAll()">
                    📝 全选
                </button>
                <button type="button" class="btn btn-outline" onclick="clearSelection()">
                    🔄 清空选择
                </button>
            </div>
            
            <!-- 免复习天数选择 -->
            <div class="skip-duration">
                <label for="skipDays">免复习天数：</label>
                <select id="skipDays">
                    <option value="3">3天</option>
                    <option value="7" selected>7天（推荐）</option>
                    <option value="14">14天</option>
                    <option value="30">30天</option>
                </select>
            </div>
            
            <!-- 单词列表 -->
            <div class="words-container">
                <div id="wordsLoading" class="loading" style="display: none;">
                    加载中...
                </div>
                
                <div id="wordsList" class="words-list">
                    <!-- 单词列表将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
        
        <div class="modal-footer">
            <div class="selection-info">
                已选择：<span id="selectedCount">0</span>个单词
            </div>
            
            <div class="action-buttons">
                <button type="button" class="btn btn-secondary" onclick="closeSkipReviewModal()">
                    取消
                </button>
                <button type="button" class="btn btn-primary" onclick="confirmSkipReview()" id="confirmBtn">
                    确认设置免复习
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 免复习选择的CSS样式 -->
<style>
/* 模态框基础样式 */
#skipReviewModal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

#skipReviewModal .modal-content {
    background-color: #fefefe;
    margin: 2% auto;
    padding: 0;
    border: none;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -100%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, 0) scale(1);
    }
}

/* 模态框头部 */
.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.close:hover {
    opacity: 1;
}

/* 模态框主体 */
.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* 日期信息 */
.date-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.date-info p {
    margin: 5px 0;
    font-size: 14px;
}

/* 快速操作按钮 */
.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.quick-actions .btn {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 20px;
    transition: all 0.2s;
}

.btn-outline {
    background: white;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
    transform: translateY(-1px);
}

/* 免复习天数选择 */
.skip-duration {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.skip-duration label {
    font-weight: 500;
    color: #495057;
}

.skip-duration select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

/* 单词列表容器 */
.words-container {
    min-height: 200px;
    max-height: 350px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
}

.words-list {
    display: grid;
    gap: 8px;
}

/* 单词卡片 */
.word-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: white;
    transition: all 0.2s;
    cursor: pointer;
}

.word-item:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

.word-item.selected {
    background: #e7f3ff;
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
}

.word-item.recommended {
    background: #fff3cd;
    border-color: #ffc107;
}

.word-item.recommended::before {
    content: "⭐";
    margin-right: 8px;
}

/* 复选框 */
.word-checkbox {
    margin-right: 12px;
    transform: scale(1.2);
}

/* 单词信息 */
.word-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.word-main {
    display: flex;
    flex-direction: column;
}

.word-english {
    font-weight: 600;
    color: #212529;
    font-size: 16px;
}

.word-chinese {
    color: #6c757d;
    font-size: 14px;
}

.word-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 12px;
    color: #6c757d;
}

.proficiency {
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 12px;
    color: white;
    font-size: 11px;
}

.proficiency.high {
    background: #28a745;
}

.proficiency.medium {
    background: #ffc107;
    color: #212529;
}

.proficiency.low {
    background: #dc3545;
}

.today-performance {
    margin-top: 4px;
}

/* 模态框底部 */
.modal-footer {
    background: #f8f9fa;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e9ecef;
}

.selection-info {
    font-weight: 500;
    color: #495057;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 50px;
    color: #6c757d;
}

/* 移动端适配 */
@media (max-width: 768px) {
    #skipReviewModal .modal-content {
        width: 95%;
        margin: 5% auto;
    }
    
    .quick-actions {
        flex-direction: column;
    }
    
    .quick-actions .btn {
        width: 100%;
    }
    
    .word-info {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .word-stats {
        align-items: flex-start;
        margin-top: 8px;
    }
}
</style>

<script>
// 免复习选择相关的JavaScript函数
let todayWords = [];
let selectedWordIds = new Set();

// 显示免复习选择模态框
async function showSkipReviewModal(learningDate = null) {
    const modal = document.getElementById('skipReviewModal');
    const wordsLoading = document.getElementById('wordsLoading');
    const wordsList = document.getElementById('wordsList');
    
    // 显示模态框和加载状态
    modal.style.display = 'block';
    wordsLoading.style.display = 'block';
    wordsList.innerHTML = '';
    
    try {
        // 获取今日学习的单词
        const url = learningDate ? 
            `/api/skip-review/today-words?date=${learningDate}` : 
            '/api/skip-review/today-words';
            
        const response = await fetch(url);
        const result = await response.json();
        
        if (result.success) {
            todayWords = result.data.words;
            updateModalInfo(result.data);
            renderWordsList();
        } else {
            showMessage('获取学习单词失败：' + result.message, 'error');
            closeSkipReviewModal();
        }
    } catch (error) {
        console.error('获取今日学习单词失败:', error);
        showMessage('获取学习单词失败，请稍后重试', 'error');
        closeSkipReviewModal();
    } finally {
        wordsLoading.style.display = 'none';
    }
}

// 更新模态框信息
function updateModalInfo(data) {
    document.getElementById('learningDate').textContent = data.learning_date;
    document.getElementById('totalWordsCount').textContent = data.total_count;
    document.getElementById('recommendCount').textContent = data.recommend_count;
}

// 渲染单词列表
function renderWordsList() {
    const wordsList = document.getElementById('wordsList');
    
    if (todayWords.length === 0) {
        wordsList.innerHTML = '<div class="no-words">今日暂无学习记录</div>';
        return;
    }
    
    wordsList.innerHTML = todayWords.map(word => {
        const proficiencyClass = word.proficiency >= 80 ? 'high' : 
                               word.proficiency >= 50 ? 'medium' : 'low';
        
        const recommendedClass = word.recommend_skip ? 'recommended' : '';
        
        return `
            <div class="word-item ${recommendedClass}" onclick="toggleWordSelection(${word.word_id})">
                <input type="checkbox" class="word-checkbox" 
                       id="word_${word.word_id}" 
                       ${selectedWordIds.has(word.word_id) ? 'checked' : ''}>
                
                <div class="word-info">
                    <div class="word-main">
                        <div class="word-english">${word.english_word}</div>
                        <div class="word-chinese">${word.chinese_meaning}</div>
                    </div>
                    
                    <div class="word-stats">
                        <div class="proficiency ${proficiencyClass}">
                            ${word.proficiency.toFixed(1)}%
                        </div>
                        <div class="today-performance">
                            今日: ${word.today_correct_count}/${word.today_learning_count} 
                            (${word.today_accuracy}%)
                        </div>
                        ${word.is_currently_skipped ? 
                            `<div class="skip-status">免复习至: ${word.skip_review_until}</div>` : 
                            ''
                        }
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    updateSelectionCount();
}

// 切换单词选择状态
function toggleWordSelection(wordId) {
    const checkbox = document.getElementById(`word_${wordId}`);
    const wordItem = checkbox.closest('.word-item');
    
    if (selectedWordIds.has(wordId)) {
        selectedWordIds.delete(wordId);
        checkbox.checked = false;
        wordItem.classList.remove('selected');
    } else {
        selectedWordIds.add(wordId);
        checkbox.checked = true;
        wordItem.classList.add('selected');
    }
    
    updateSelectionCount();
}

// 选择推荐的单词
function selectRecommended() {
    clearSelection();
    todayWords.forEach(word => {
        if (word.recommend_skip) {
            selectedWordIds.add(word.word_id);
        }
    });
    updateWordSelectionUI();
}

// 根据熟练度选择单词
function selectByProficiency(minProficiency) {
    clearSelection();
    todayWords.forEach(word => {
        if (word.proficiency >= minProficiency && !word.is_currently_skipped) {
            selectedWordIds.add(word.word_id);
        }
    });
    updateWordSelectionUI();
}

// 全选
function selectAll() {
    clearSelection();
    todayWords.forEach(word => {
        if (!word.is_currently_skipped) {
            selectedWordIds.add(word.word_id);
        }
    });
    updateWordSelectionUI();
}

// 清空选择
function clearSelection() {
    selectedWordIds.clear();
    updateWordSelectionUI();
}

// 更新单词选择UI
function updateWordSelectionUI() {
    todayWords.forEach(word => {
        const checkbox = document.getElementById(`word_${word.word_id}`);
        const wordItem = checkbox.closest('.word-item');
        
        if (selectedWordIds.has(word.word_id)) {
            checkbox.checked = true;
            wordItem.classList.add('selected');
        } else {
            checkbox.checked = false;
            wordItem.classList.remove('selected');
        }
    });
    updateSelectionCount();
}

// 更新选择计数
function updateSelectionCount() {
    document.getElementById('selectedCount').textContent = selectedWordIds.size;
    
    const confirmBtn = document.getElementById('confirmBtn');
    confirmBtn.disabled = selectedWordIds.size === 0;
}

// 确认设置免复习
async function confirmSkipReview() {
    if (selectedWordIds.size === 0) {
        showMessage('请至少选择一个单词', 'warning');
        return;
    }
    
    const skipDays = parseInt(document.getElementById('skipDays').value);
    const confirmBtn = document.getElementById('confirmBtn');
    
    confirmBtn.disabled = true;
    confirmBtn.textContent = '设置中...';
    
    try {
        const response = await fetch('/api/skip-review/set', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                word_ids: Array.from(selectedWordIds),
                skip_days: skipDays
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage(`成功设置${result.affected_count}个单词免复习${skipDays}天！`, 'success');
            closeSkipReviewModal();
            
            // 如果在学习页面，可以刷新页面或更新相关数据
            if (typeof refreshLearningPlan === 'function') {
                refreshLearningPlan();
            }
        } else {
            showMessage('设置失败：' + result.message, 'error');
        }
    } catch (error) {
        console.error('设置免复习失败:', error);
        showMessage('设置失败，请稍后重试', 'error');
    } finally {
        confirmBtn.disabled = false;
        confirmBtn.textContent = '确认设置免复习';
    }
}

// 关闭模态框
function closeSkipReviewModal() {
    const modal = document.getElementById('skipReviewModal');
    modal.style.display = 'none';
    
    // 重置状态
    todayWords = [];
    selectedWordIds.clear();
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('skipReviewModal');
    if (event.target === modal) {
        closeSkipReviewModal();
    }
}

// 键盘事件处理
document.addEventListener('keydown', function(event) {
    const modal = document.getElementById('skipReviewModal');
    if (modal.style.display === 'block' && event.key === 'Escape') {
        closeSkipReviewModal();
    }
});
</script>