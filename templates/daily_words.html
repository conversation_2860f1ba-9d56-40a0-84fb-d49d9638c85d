<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>今日单词计划</title>
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        body {
            background-color: #f0f0f0;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            max-width: 900px;
            width: 100%;
        }
        h1 {
            margin: 0;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f8f8f8;
        }
        .overdue {
            background-color: #fff0f0; /* 顺延单词背景色 */
        }
        .summary-box {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .tab-container {
            margin-bottom: 20px;
        }
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
            border-radius: 5px 5px 0 0;
        }
        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 10px 16px;
            transition: 0.3s;
            font-size: 16px;
        }
        .tab button:hover {
            background-color: #ddd;
        }
        .tab button.active {
            background-color: #4CAF50;
            color: white;
        }
        .tabcontent {
            display: none;
            padding: 6px 12px;
            border: 1px solid #ccc;
            border-top: none;
            border-radius: 0 0 5px 5px;
            animation: fadeEffect 1s;
        }
        @keyframes fadeEffect {
            from {opacity: 0;}
            to {opacity: 1;}
        }
        
        /* 打印样式 */
        @media print {
            body {
                background-color: white;
                padding: 0;
                margin: 0;
            }
            
            .container {
                max-width: 100%;
                padding: 0;
                box-shadow: none;
                border-radius: 0;
            }
            
            h1 {
                font-size: 18px;
                margin: 5px 0 10px 0;
                text-align: center;
                border-bottom: 1px solid #333;
                padding-bottom: 5px;
            }
            
            /* 隐藏选项卡，显示所有内容 */
            .tab {
                display: none !important;
            }
            
            .tabcontent {
                display: block !important;
                border: none;
                padding: 0;
                page-break-inside: avoid;
            }
            
            /* 为每个表格添加标题 */
            #todayWords:before {
                content: "今日计划单词";
                display: block;
                font-size: 14px;
                font-weight: bold;
                margin: 10px 0 5px 0;
                padding-bottom: 3px;
            }
            
            #newWords:before {
                content: "新单词列表";
                display: block;
                font-size: 14px;
                font-weight: bold;
                margin: 15px 0 5px 0;
                padding-bottom: 3px;
            }
            
            #difficultWords:before {
                content: "不熟悉的词（正确率<70%）";
                display: block;
                font-size: 14px;
                font-weight: bold;
                margin: 15px 0 5px 0;
                padding-bottom: 3px;
            }
            
            table {
                font-size: 12px;
                page-break-inside: avoid;
            }
            
            th, td {
                padding: 3px 5px;
                border: 1px solid #333;
                font-size: 11px;
            }
            
            th {
                background-color: #f0f0f0 !important;
                font-weight: bold;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            /* 优化统计信息框 */
            .summary-box {
                background-color: transparent !important;
                border: none !important;
                margin-bottom: 10px;
                padding: 0;
            }
            
            .summary-box table {
                margin-bottom: 10px !important;
            }
            
            .summary-box td {
                font-size: 12px !important;
                border: 1px solid #999 !important;
                background-color: white !important;
            }
            
            /* 确保颜色在打印时显示 */
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            /* 页面设置 */
            @page {
                margin: 0.5cm;
                size: A4;
            }
            
            /* 隐藏空表格 */
            .tabcontent:has(tbody:empty) {
                display: none !important;
            }
            
            /* 确保内容紧凑 */
            .tabcontent + .tabcontent {
                margin-top: 20px;
            }
        }
        
        /* 打印按钮样式 */
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        
        .print-button:hover {
            background-color: #45a049;
        }
        
        @media print {
            .print-button {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <button class="print-button" onclick="optimizePrint()">🖨️ 打印计划</button>
    <div class="container">
        <h1>今日单词计划</h1>

        <div class="summary-box" id="summary-box">
            <!-- 统计信息将在这里显示 -->
        </div>

        <div class="tab-container">
            <div class="tab">
                <button class="tablinks active" onclick="openTab(event, 'todayWords')">今日计划</button>
                <button class="tablinks" onclick="openTab(event, 'newWords')">新单词</button>
                <button class="tablinks" onclick="openTab(event, 'difficultWords')">不熟悉的词 (正确率<70%)</button>
            </div>

            <div id="todayWords" class="tabcontent" style="display: block;">
                <table>
                    <thead>
                        <tr>
                            <th>单词</th>
                            <th>答案</th>
                            <th>类型</th>
                            <th>熟练度</th>
                            <th>最近学习日期</th>
                        </tr>
                    </thead>
                    <tbody id="today-words-table">
                        <!-- 今日计划单词将填充在这里 -->
                    </tbody>
                </table>
            </div>

            <div id="newWords" class="tabcontent">
                <table>
                    <thead>
                        <tr>
                            <th>单词</th>
                            <th>答案</th>
                            <th>熟练度</th>
                            <th>最近学习日期</th>
                        </tr>
                    </thead>
                    <tbody id="new-words-table">
                        <!-- 新单词将填充在这里 -->
                    </tbody>
                </table>
            </div>

            <div id="difficultWords" class="tabcontent">
                <table>
                    <thead>
                        <tr>
                            <th>单词</th>
                            <th>答案</th>
                            <th>熟练度</th>
                            <th>最近学习日期</th>
                            <th>学习次数</th>
                        </tr>
                    </thead>
                    <tbody id="difficult-words-table">
                        <!-- 不熟悉的词（正确率<70%且学习≥3次）将填充在这里 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <script>
        // 优化打印功能
        function optimizePrint() {
            // 临时移除空的表格容器
            const tabContents = document.querySelectorAll('.tabcontent');
            const hiddenElements = [];
            
            tabContents.forEach(tab => {
                const tbody = tab.querySelector('tbody');
                if (!tbody || tbody.children.length === 0) {
                    tab.style.display = 'none';
                    hiddenElements.push(tab);
                }
            });
            
            // 执行打印
            window.print();
            
            // 恢复隐藏的元素
            setTimeout(() => {
                hiddenElements.forEach(elem => {
                    elem.style.display = '';
                });
            }, 100);
        }
        
        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;

            // 隐藏所有选项卡内容
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }

            // 取消所有标签的活动状态
            tablinks = document.getElementsByClassName("tablinks");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }

            // 显示当前选项卡并添加活动类
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 获取URL中的日期参数
            const urlParams = new URLSearchParams(window.location.search);
            const dateParam = urlParams.get('date');

            // 构建API URL，如果有日期参数则添加
            let apiUrl = '/get_daily_words';
            if (dateParam) {
                apiUrl += `?date=${dateParam}`;
            }

            fetch(apiUrl)
                .then(response => response.json())
                .then(data => {
                    const todayTableBody = document.getElementById('today-words-table');
                    const newWordsTableBody = document.getElementById('new-words-table');
                    const difficultWordsTableBody = document.getElementById('difficult-words-table');

                    // 统计数据
                    let todayCount = 0;
                    let newWordsCount = 0;
                    let difficultWordsCount = 0;
                    let reviewWordsCount = 0;

                    data.daily_words.forEach(word => {
                        todayCount++;

                        if (word.is_new) {
                            newWordsCount++;
                        } else {
                            reviewWordsCount++;
                        }

                        // 获取熟练度数据 - 使用数据库中的proficiency字段
                        const proficiencyScore = word.long_term_proficiency || 0;  // 熟练度分数（0-100）

                        // 根据熟练度分数计算等级和颜色
                        let proficiencyLevel, proficiencyColor;
                        if (proficiencyScore >= 80) {
                            proficiencyLevel = '精通'; proficiencyColor = '#28a745';
                        } else if (proficiencyScore >= 60) {
                            proficiencyLevel = '熟练'; proficiencyColor = '#17a2b8';
                        } else if (proficiencyScore >= 40) {
                            proficiencyLevel = '一般'; proficiencyColor = '#ffc107';
                        } else if (proficiencyScore >= 20) {
                            proficiencyLevel = '初学'; proficiencyColor = '#fd7e14';
                        } else if (proficiencyScore > 0) {
                            proficiencyLevel = '生疏'; proficiencyColor = '#dc3545';
                        } else {
                            proficiencyLevel = '陌生'; proficiencyColor = '#6c757d';
                        }
                        
                        const accuracyRate = word.accuracy_rate || 0;
                        const lastReviewed = word.last_learned || '未学习';

                        // 今日计划表格
                        const todayRow = document.createElement('tr');
                        todayRow.innerHTML = `
                            <td>${word.word}</td>
                            <td>${word.answer}</td>
                            <td>${word.is_new ? '新' : '复习'}</td>
                            <td>
                                <div style="font-size: 12px; color: ${proficiencyColor}; font-weight: bold;">${proficiencyScore.toFixed(1)}%</div>
                            </td>
                            <td>${lastReviewed}</td>
                        `;
                        todayTableBody.appendChild(todayRow);

                        // 新单词表格
                        if (word.is_new) {
                            const newWordRow = document.createElement('tr');
                            newWordRow.innerHTML = `
                                <td>${word.word}</td>
                                <td>${word.answer}</td>
                                <td>
                                    <div style="font-size: 12px; color: ${proficiencyColor}; font-weight: bold;">${proficiencyScore.toFixed(1)}%</div>
                                </td>
                                <td>${lastReviewed}</td>
                            `;
                            newWordsTableBody.appendChild(newWordRow);
                        }

                        // 不熟悉的词表格（正确率<70%且学习次数≥3）
                        const recitationCount = word.learn_count || 0;
                        if (accuracyRate < 70 && recitationCount >= 3) {
                            difficultWordsCount++;
                            const difficultRow = document.createElement('tr');
                            difficultRow.innerHTML = `
                                <td>${word.word}</td>
                                <td>${word.answer}</td>
                                <td>
                                    <div style="font-size: 12px; color: ${proficiencyColor}; font-weight: bold;">${proficiencyScore.toFixed(1)}%</div>
                                </td>
                                <td>${lastReviewed}</td>
                                <td>${recitationCount}</td>
                            `;
                            difficultWordsTableBody.appendChild(difficultRow);
                        }
                    });

                    // 更新统计信息
                    const todayDate = new Date().toLocaleDateString('zh-CN');
                    document.getElementById('summary-box').innerHTML = `
                        <table style="width: 100%; border-collapse: collapse; margin: 0;">
                            <tr>
                                <td style="padding: 5px; border: none;"><strong>日期</strong></td>
                                <td style="padding: 5px; border: none;">${dateParam || todayDate}</td>
                                <td style="padding: 5px; border: none;"><strong>总计</strong></td>
                                <td style="padding: 5px; border: none;">${todayCount}个</td>
                            </tr>
                            <tr>
                                <td style="padding: 5px; border: none;"><strong>新词</strong></td>
                                <td style="padding: 5px; border: none;">${newWordsCount}个</td>
                                <td style="padding: 5px; border: none;"><strong>复习</strong></td>
                                <td style="padding: 5px; border: none;">${reviewWordsCount}个</td>
                            </tr>
                            <tr>
                                <td style="padding: 5px; border: none;"><strong>难词</strong></td>
                                <td style="padding: 5px; border: none;" colspan="3">${difficultWordsCount}个 <span style="font-size: 10px;">(正确率<70%,学习≥3次)</span></td>
                            </tr>
                        </table>
                    `;
                })
                .catch(error => console.error('Error fetching daily words:', error));
        });
    </script>
</body>
</html>
