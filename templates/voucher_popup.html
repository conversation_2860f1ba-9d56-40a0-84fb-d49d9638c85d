<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购物券详情</title>
    <style>
        .voucher-popup {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .voucher-header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .voucher-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        .voucher-title {
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        
        .voucher-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .stat-number {
            font-size: 28px;
            font-weight: bold;
            display: block;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
            margin-top: 5px;
        }
        
        .progress-section {
            margin-bottom: 20px;
        }
        
        .progress-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .progress-bar {
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin-bottom: 5px;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .incentive-list {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .incentive-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .incentive-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .incentive-item:last-child {
            border-bottom: none;
        }
        
        .incentive-desc {
            font-size: 14px;
        }
        
        .incentive-date {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .milestone-section {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .milestone-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .milestone-info {
            text-align: center;
        }
        
        .milestone-days {
            font-size: 36px;
            font-weight: bold;
            color: #FFD700;
        }
        
        .milestone-text {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .close-btn {
            width: 100%;
            padding: 12px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .close-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .error {
            background: #f44336;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="voucher-popup">
        <div id="loading" class="loading">
            <div>📊 加载中...</div>
        </div>
        
        <div id="error" class="error" style="display: none;">
            <div>❌ 加载失败，请重试</div>
        </div>
        
        <div id="content" style="display: none;">
            <div class="voucher-header">
                <div class="voucher-icon">🎫</div>
                <h1 class="voucher-title">我的购物券</h1>
            </div>
            
            <div class="voucher-stats">
                <div class="stat-card">
                    <span class="stat-number" id="current-vouchers">0</span>
                    <div class="stat-label">当前拥有</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="today-earned">0</span>
                    <div class="stat-label">今日获得</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="week-earned">0</span>
                    <div class="stat-label">本周获得</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number" id="consecutive-days">0</span>
                    <div class="stat-label">连续天数</div>
                </div>
            </div>
            
            <div class="progress-section">
                <div class="progress-title">今日进度</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="daily-progress"></div>
                </div>
                <div class="progress-text" id="daily-progress-text">0/2</div>
            </div>
            
            <div class="progress-section">
                <div class="progress-title">本周进度</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="weekly-progress"></div>
                </div>
                <div class="progress-text" id="weekly-progress-text">0/15</div>
            </div>
            
            <div class="incentive-list" id="incentive-list">
                <div class="incentive-title">🎉 今日激励</div>
                <div id="incentive-items"></div>
            </div>
            
            <div class="milestone-section" id="milestone-section">
                <div class="milestone-title">🏆 下一个里程碑</div>
                <div class="milestone-info" id="milestone-info">
                    <div class="milestone-days" id="milestone-days">-</div>
                    <div class="milestone-text" id="milestone-text">继续努力！</div>
                </div>
            </div>
            
            <button class="close-btn" onclick="closePopup()">关闭</button>
        </div>
    </div>

    <script>
        let voucherData = null;
        
        // 加载购物券信息
        async function loadVoucherInfo() {
            try {
                const response = await fetch('/api/voucher/popup-info');
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                
                voucherData = await response.json();
                
                if (voucherData.error) {
                    throw new Error(voucherData.error);
                }
                
                updateUI();
                
            } catch (error) {
                console.error('加载购物券信息失败:', error);
                showError();
            }
        }
        
        // 更新UI
        function updateUI() {
            // 隐藏加载提示，显示内容
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'block';
            
            // 更新统计数据
            document.getElementById('current-vouchers').textContent = voucherData.current_vouchers || 0;
            document.getElementById('today-earned').textContent = voucherData.today_earned || 0;
            document.getElementById('week-earned').textContent = voucherData.week_earned || 0;
            document.getElementById('consecutive-days').textContent = voucherData.consecutive_days || 0;
            
            // 更新进度条
            updateProgressBars();
            
            // 更新激励列表
            updateIncentiveList();
            
            // 更新里程碑信息
            updateMilestone();
        }
        
        // 更新进度条
        function updateProgressBars() {
            const limits = voucherData.limits || {};
            
            // 今日进度
            const dailyProgress = (voucherData.today_earned || 0) / (limits.daily_limit || 2) * 100;
            document.getElementById('daily-progress').style.width = Math.min(dailyProgress, 100) + '%';
            document.getElementById('daily-progress-text').textContent = 
                `${voucherData.today_earned || 0}/${limits.daily_limit || 2}`;
            
            // 本周进度
            const weeklyProgress = (voucherData.week_earned || 0) / (limits.weekly_limit || 15) * 100;
            document.getElementById('weekly-progress').style.width = Math.min(weeklyProgress, 100) + '%';
            document.getElementById('weekly-progress-text').textContent = 
                `${voucherData.week_earned || 0}/${limits.weekly_limit || 15}`;
        }
        
        // 更新激励列表
        function updateIncentiveList() {
            const incentiveItems = document.getElementById('incentive-items');
            const incentives = voucherData.recent_incentives || [];
            
            if (incentives.length === 0) {
                incentiveItems.innerHTML = '<div class="incentive-item"><span>暂无激励记录</span></div>';
                return;
            }
            
            incentiveItems.innerHTML = incentives.map(incentive => `
                <div class="incentive-item">
                    <span class="incentive-desc">${incentive.description}</span>
                    <span class="incentive-date">${incentive.date}</span>
                </div>
            `).join('');
        }
        
        // 更新里程碑信息
        function updateMilestone() {
            const milestone = voucherData.next_milestone;
            
            if (!milestone) {
                document.getElementById('milestone-section').style.display = 'none';
                return;
            }
            
            document.getElementById('milestone-days').textContent = milestone.remaining;
            document.getElementById('milestone-text').textContent = 
                `还需${milestone.remaining}天达成${milestone.reward.description}`;
        }
        
        // 显示错误
        function showError() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('error').style.display = 'block';
        }
        
        // 关闭弹窗
        function closePopup() {
            window.close();
        }
        
        // 页面加载时执行
        window.onload = function() {
            loadVoucherInfo();
        };
    </script>
</body>
</html>