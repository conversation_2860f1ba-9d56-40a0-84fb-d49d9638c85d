<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生词本 - 闹闹疯狂学单词</title>
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .stat-card .number {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-card .sub-text {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }
        .vocabulary-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        .vocabulary-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: grid;
            grid-template-columns: 1fr auto auto;
            gap: 15px;
            align-items: center;
        }
        .word-info {
            display: flex;
            flex-direction: column;
        }
        .word-text {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .word-answer {
            font-size: 16px;
            color: #666;
            margin-bottom: 5px;
        }
        .word-stats {
            font-size: 12px;
            color: #888;
            display: flex;
            gap: 15px;
        }
        .word-type {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }
        .type-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .type-manual {
            background: #e3f2fd;
            color: #1976d2;
        }
        .type-low-accuracy {
            background: #ffebee;
            color: #d32f2f;
        }
        .type-both {
            background: #fff3e0;
            color: #f57c00;
        }
        .accuracy-bar {
            width: 60px;
            height: 8px;
            background: #eee;
            border-radius: 4px;
            overflow: hidden;
        }
        .accuracy-fill {
            height: 100%;
            transition: width 0.3s;
        }
        .accuracy-high { background: #4caf50; }
        .accuracy-medium { background: #ff9800; }
        .accuracy-low { background: #f44336; }
        .word-actions {
            display: flex;
            gap: 10px;
        }
        .btn-small {
            padding: 8px 12px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-remove {
            background: #ff6b6b;
            color: white;
        }
        .btn-remove:hover {
            background: #ff5252;
        }
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .empty-state h3 {
            margin-bottom: 10px;
        }
        .filter-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
        }
        .filter-btn {
            padding: 8px 16px;
            border: 2px solid #ddd;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .filter-btn.active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }
        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: #6c757d;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
        }

        /* 打印样式 */
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            
            body {
                margin: 0;
                padding: 15mm;
                background: white !important;
                font-family: "SimSun", "宋体", serif;
                font-size: 12pt;
                line-height: 1.2;
            }
            
            /* 隐藏所有非打印内容 */
            .vocabulary-container > *:not(#print-area) {
                display: none !important;
            }
            
            /* 显示打印内容 */
            #print-area {
                display: block !important;
            }
            
            .vocabulary-list h2 {
                text-align: center;
                font-size: 18pt;
                font-weight: bold;
                margin: 0 0 20pt 0;
                color: black !important;
                border-bottom: 2pt solid black;
                padding-bottom: 10pt;
            }
            
            .print-matrix {
                display: grid !important;
                grid-template-columns: repeat(4, 1fr);
                grid-template-rows: repeat(10, 1fr);
                gap: 6pt;
                page-break-inside: avoid;
                margin-bottom: 10pt;
                width: 100%;
                height: auto;
            }
            
            .print-word-item {
                border: 1pt solid #333 !important;
                padding: 8pt;
                text-align: center;
                background: white !important;
                height: 60pt;
                display: flex !important;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                box-sizing: border-box;
            }
            
            .print-word {
                font-size: 14pt;
                font-weight: bold;
                color: black !important;
                margin-bottom: 4pt;
                word-wrap: break-word;
                max-width: 100%;
            }
            
            .print-answer {
                font-size: 10pt;
                color: #333 !important;
                text-align: center;
                word-wrap: break-word;
                max-width: 100%;
            }
            
            .print-page {
                page-break-after: always;
            }
            
            .print-page:last-child {
                page-break-after: auto;
            }
        }

        .print-btn {
            background: linear-gradient(135deg, #9c88ff 0%, #8c7ae6 100%);
            color: white;
        }
    </style>
</head>
<body>
    <a href="/dashboard" class="back-btn">← 返回仪表板</a>

    <div class="vocabulary-container">
        <div class="header">
            <h1>📚 生词本</h1>
            <p>管理您的生词和需要加强练习的单词</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <h3>📝 手动添加</h3>
                <div class="number">{{ manual_count }}</div>
                <div class="sub-text">用户主动添加的生词</div>
            </div>
            <div class="stat-card">
                <h3>📉 低正确率</h3>
                <div class="number">{{ low_accuracy_count }}</div>
                <div class="sub-text">正确率低于{{ low_accuracy_threshold }}%的单词</div>
            </div>
            <div class="stat-card">
                <h3>📊 总计</h3>
                <div class="number">{{ total_count }}</div>
                <div class="sub-text">生词本中的所有单词</div>
            </div>
        </div>

        <div class="action-buttons">
            <a href="/practice_vocabulary" class="btn btn-primary">
                🎯 开始生词练习
            </a>
            <button onclick="printVocabulary()" class="btn print-btn">
                🖨️ 打印生词本
            </button>
            <a href="/dashboard" class="btn btn-secondary">
                📊 返回仪表板
            </a>
        </div>

        {% if vocabulary_list %}
        <!-- 打印专用区域，屏幕时隐藏 -->
        <div id="print-area" style="display: none;">
            <div class="vocabulary-list">
                <h2>生词本</h2>
                <div id="print-content"></div>
            </div>
        </div>
        
        <div class="vocabulary-list">
            <h2>📋 生词列表</h2>
            
            <div class="filter-buttons">
                <button class="filter-btn active" onclick="filterWords('all')">全部</button>
                <button class="filter-btn" onclick="filterWords('manual')">手动添加</button>
                <button class="filter-btn" onclick="filterWords('low_accuracy')">低正确率</button>
                <button class="filter-btn" onclick="filterWords('both')">两种类型</button>
            </div>

            {% for word in vocabulary_list %}
            <div class="vocabulary-item" data-type="{{ word.type }}">
                <div class="word-info">
                    <div class="word-text">{{ word.word }}</div>
                    <div class="word-answer">{{ word.answer }}</div>
                    <div class="word-stats">
                        <span>⭐ {{ word.proficiency }}星</span>
                        <span>📝 练习{{ word.recitation_count }}次</span>
                        <span>✅ 正确{{ word.correct_count }}次</span>
                        {% if word.last_reviewed %}
                        <span>🕒 {{ word.last_reviewed.strftime('%m-%d') }}</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="word-type">
                    {% if word.type == 'manual' %}
                    <span class="type-badge type-manual">手动添加</span>
                    {% elif word.type == 'low_accuracy' %}
                    <span class="type-badge type-low-accuracy">低正确率</span>
                    {% elif word.type == 'both' %}
                    <span class="type-badge type-both">两种类型</span>
                    {% endif %}
                    
                    <div class="accuracy-bar">
                        <div class="accuracy-fill 
                            {% if word.accuracy >= 80 %}accuracy-high
                            {% elif word.accuracy >= 60 %}accuracy-medium
                            {% else %}accuracy-low{% endif %}"
                            style="width: {{ word.accuracy }}%">
                        </div>
                    </div>
                    <span style="font-size: 10px;">{{ "%.1f"|format(word.accuracy) }}%</span>
                </div>
                
                <div class="word-actions">
                    <button class="btn-small btn-remove" onclick="removeFromVocabulary('{{ word.word }}')">
                        移除
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <h3>📖 生词本为空</h3>
            <p>您还没有添加任何生词</p>
            <p>在学习过程中点击"加入生词库"按钮来添加生词</p>
        </div>
        {% endif %}
    </div>

    <script>
        function filterWords(type) {
            const items = document.querySelectorAll('.vocabulary-item');
            const buttons = document.querySelectorAll('.filter-btn');
            
            // 更新按钮状态
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 过滤单词
            items.forEach(item => {
                const itemType = item.dataset.type;
                if (type === 'all' || itemType === type || (type === 'both' && itemType === 'both')) {
                    item.style.display = 'grid';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function removeFromVocabulary(word) {
            if (!confirm(`确定要从生词本移除"${word}"吗？`)) {
                return;
            }

            fetch('/api/remove_from_vocabulary', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ word: word })
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert(data.message);
                    location.reload(); // 刷新页面
                } else if (data.error) {
                    alert('移除失败: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('移除失败');
            });
        }

        function printVocabulary() {
            // 获取当前显示的单词（考虑过滤状态）
            let visibleItems = document.querySelectorAll('.vocabulary-item');
            
            // 过滤掉隐藏的项目
            visibleItems = Array.from(visibleItems).filter(item => {
                const style = window.getComputedStyle(item);
                return style.display !== 'none';
            });
            
            console.log('找到', visibleItems.length, '个可见单词');
            
            if (visibleItems.length === 0) {
                alert('没有可打印的单词');
                return;
            }

            // 准备打印数据
            const words = [];
            visibleItems.forEach(item => {
                const wordText = item.querySelector('.word-text').textContent.trim();
                const wordAnswer = item.querySelector('.word-answer').textContent.trim();
                words.push({ word: wordText, answer: wordAnswer });
            });

            console.log('准备打印', words.length, '个单词');

            // 生成打印内容
            generatePrintContent(words);

            // 显示打印预览并触发打印
            showPrintPreview();
        }

        function generatePrintContent(words) {
            const printContent = document.getElementById('print-content');
            printContent.innerHTML = '';

            const wordsPerPage = 40; // 4列 x 10行
            const totalPages = Math.ceil(words.length / wordsPerPage);

            for (let page = 0; page < totalPages; page++) {
                const pageDiv = document.createElement('div');
                pageDiv.className = 'print-page';

                const matrixDiv = document.createElement('div');
                matrixDiv.className = 'print-matrix';

                // 获取当前页的单词（最多40个）
                const pageWords = words.slice(page * wordsPerPage, (page + 1) * wordsPerPage);

                // 确保每页都有40个格子（4列x10行），不足的用空格子填充
                const matrixWords = [...pageWords];
                while (matrixWords.length < 40) {
                    matrixWords.push({ word: '', answer: '' });
                }

                // 只取前40个，确保严格按照4x10矩阵
                matrixWords.slice(0, 40).forEach(wordData => {
                    const wordDiv = document.createElement('div');
                    wordDiv.className = 'print-word-item';

                    if (wordData.word) {
                        const wordSpan = document.createElement('div');
                        wordSpan.className = 'print-word';
                        wordSpan.textContent = wordData.word;

                        const answerSpan = document.createElement('div');
                        answerSpan.className = 'print-answer';
                        answerSpan.textContent = wordData.answer;

                        wordDiv.appendChild(wordSpan);
                        wordDiv.appendChild(answerSpan);
                    }

                    matrixDiv.appendChild(wordDiv);
                });

                pageDiv.appendChild(matrixDiv);
                printContent.appendChild(pageDiv);
            }
        }

        function showPrintPreview() {
            // 显示打印区域
            const printArea = document.getElementById('print-area');
            printArea.style.display = 'block';
            
            // 延迟一下确保DOM更新完成，然后打印
            setTimeout(() => {
                window.print();
                
                // 打印完成后隐藏打印区域
                setTimeout(() => {
                    printArea.style.display = 'none';
                }, 500);
            }, 100);
        }
    </script>
</body>
</html>
