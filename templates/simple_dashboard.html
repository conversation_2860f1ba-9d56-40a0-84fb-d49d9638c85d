<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闹闹疯狂学单词 - 仪表板</title>
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0;
        }
        .current-date {
            color: #666;
            font-size: 18px;
            margin-top: 10px;
        }
        .top-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .combined-stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }
        .combined-stats-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            pointer-events: none;
        }
        .unified-stats {
            text-align: center;
            position: relative;
            z-index: 1;
        }
        .unified-stats h3 {
            margin: 0 0 20px 0;
            font-size: 16px;
            opacity: 0.9;
            font-weight: 500;
        }
        .stats-formula {
            font-size: 42px;
            font-weight: 700;
            margin: 20px 0;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 12px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .learned-count, .total-count {
            color: #fff;
        }
        .divider {
            color: #fff;
            opacity: 0.8;
            font-size: 36px;
        }
        .progress-text {
            font-size: 16px;
            opacity: 0.95;
            margin-top: 15px;
            font-weight: 500;
        }
        .second-row {
            display: grid;
            grid-template-columns: 3fr 2fr; /* 60% + 40% 比例 */
            gap: 20px;
            margin-bottom: 30px;
        }
        .proficiency-distribution-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }
        .proficiency-table {
            width: 100%;
        }
        .proficiency-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .proficiency-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: 15px;
            padding: 18px;
            margin-bottom: 12px;
            background: white;
            border-radius: 12px;
            border: 1px solid rgba(0,0,0,0.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            align-items: center;
        }
        .proficiency-row:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .level-info {
            font-weight: 600;
            font-size: 16px;
            color: #333;
        }
        .score-range {
            color: #666;
            font-size: 14px;
            font-weight: 500;
        }
        .word-count {
            font-weight: 700;
            color: #2196F3;
            font-size: 16px;
        }
        .percentage {
            font-weight: 700;
            color: #4CAF50;
            font-size: 16px;
        }
        .proficient-border {
            border-left: 4px solid #28a745 !important;
        }
        .intermediate-border {
            border-left: 4px solid #ffc107 !important;
        }
        .beginner-border {
            border-left: 4px solid #dc3545 !important;
        }

        .today-btn {
            background: #43e97b !important;
        }
        .proficiency-summary {
            margin-top: 15px;
            padding: 15px;
            background-color: #e7f3ff;
            border-radius: 5px;
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .stat-card .number {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-card .sub-text {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }
        .today-plan {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            padding: 25px;
            border-radius: 15px;
            color: white;
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
            height: fit-content;
        }
        .proficiency-distribution-card h3,
        .today-plan h2 {
            margin: 0 0 15px 0;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
        }
        .plan-details {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;
            text-align: center;
        }
        .plan-item {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 8px;
        }
        .plan-item .label {
            font-size: 14px;
            opacity: 0.9;
        }
        .plan-item .value {
            font-size: 24px;
            font-weight: bold;
            margin-top: 5px;
        }
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }
        .btn-info {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }
        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #ff6b6b;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .logout-btn:hover {
            background: #ff5252;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            text-decoration: none;
            color: white;
        }

        /* 用户信息卡片 */
        .user-info-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 25px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            border: 1px solid rgba(0,0,0,0.05);
            color: white;
            position: relative;
        }

        .user-main-info {
            flex: 1;
        }

        .user-stats {
            margin-top: 12px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .user-level {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .level-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .points {
            font-size: 14px;
            font-weight: 500;
        }

        .user-currency {
            font-size: 14px;
            font-weight: 500;
        }

        .current-mission {
            margin-top: 8px;
        }

        .mission-text {
            font-size: 13px;
            opacity: 0.9;
            display: block;
            margin-bottom: 6px;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50 0%, #45a049 100%);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .progress-75 {
            width: 75%;
        }

        .user-info-card .user-welcome {
            color: white;
            font-weight: 600;
            font-size: 18px;
            white-space: nowrap;
        }

        .logout-dropdown {
            position: relative;
            display: inline-block;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s ease;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .user-avatar:hover {
            transform: scale(1.05);
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            margin-top: 5px;
            background-color: white;
            min-width: 160px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1000;
            border: 1px solid #e0e0e0;
        }

        .dropdown-content a {
            color: #333;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: background-color 0.2s ease;
            border-radius: 8px;
            margin: 4px;
        }

        .dropdown-content a:hover {
            background-color: #f5f5f5;
        }

        .dropdown-content a.logout-item {
            color: #ff6b6b;
            border-top: 1px solid #e0e0e0;
            margin-top: 8px;
            padding-top: 12px;
        }

        .dropdown-content a.logout-item:hover {
            background-color: #fff5f5;
        }

        .logout-dropdown:hover .dropdown-content {
            display: block;
        }

        /* 自定义确认对话框 */
        .custom-modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 400px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }

        .modal-message {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.5;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .modal-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 80px;
        }

        .modal-btn-cancel {
            background: #f5f5f5;
            color: #333;
        }

        .modal-btn-cancel:hover {
            background: #e0e0e0;
        }

        .modal-btn-confirm {
            background: #ff6b6b;
            color: white;
        }

        .modal-btn-confirm:hover {
            background: #ff5252;
            transform: translateY(-1px);
        }


        /* 响应式设计 */
        @media (max-width: 768px) {
            .top-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            .second-row {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            .stats-formula {
                font-size: 28px;
                flex-direction: column;
                gap: 5px;
            }
            .proficiency-distribution-card {
                padding: 15px;
            }
            .proficiency-header, .proficiency-row {
                grid-template-columns: 2fr 1fr 1fr 1fr;
                font-size: 12px;
            }
            .today-plan {
                padding: 15px;
            }
            .plan-details {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 10px;
            }

            .combined-stats-card {
                padding: 15px;
            }

            /* 移动端用户信息卡片优化 */
            .user-info-card {
                padding: 15px;
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .user-main-info {
                width: 100%;
            }

            .user-stats {
                gap: 6px;
            }

            .user-level {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .mission-text {
                font-size: 12px;
            }

            .logout-dropdown {
                align-self: flex-end;
            }

            .user-avatar {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }

            .dropdown-content {
                right: 0;
                min-width: 140px;
                margin-top: 8px;
            }

            .dropdown-content a {
                padding: 10px 12px;
                font-size: 14px;
            }
        }

        @media (max-width: 480px) {
            .user-info {
                top: 5px;
                right: 5px;
                padding: 6px 10px;
            }

            .user-avatar {
                width: 32px;
                height: 32px;
                font-size: 12px;
            }

            .dropdown-content {
                min-width: 120px;
            }

            .dropdown-content a {
                padding: 8px 10px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="header">
            <h1>🎓 闹闹疯狂学单词 v0701</h1>
            <div class="current-date">📅 当前日期: {{ current_date }}</div>
        </div>

        <!-- 第一行：用户信息 + 学习统计 -->
        <div class="top-row" data-testid="top-section">
            <!-- 左侧：用户信息 -->
            <div class="user-info-card">
                <div class="user-main-info">
                    <div class="user-welcome">欢迎回来, {{ user.username }}!</div>
                    <div class="user-stats">
                        <div class="user-level">
                            <span class="level-badge">{{ user.level }}</span>
                            <span class="points">积分: {{ user.points }}/{{ user.next_threshold }}</span>
                        </div>
                        <div class="user-currency">
                            <span class="coins">💰 点券: {{ user.vouchers }}</span>
                        </div>
                        <div class="current-mission">
                            <span class="mission-text">{{ user.mission_text }}</span>
                            <div class="progress-bar">
                                <div class="progress-fill" data-progress="{{ user.mission_progress }}"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="logout-dropdown">
                    <div class="user-avatar">{{ user.username[0].upper() }}</div>
                    <div class="dropdown-content">
                        <a href="/logout" class="logout-item">🚪 退出登录</a>
                    </div>
                </div>
            </div>

            <!-- 右侧：学习统计 -->
            <div class="combined-stats-card" data-testid="combined-stats">
                <div class="unified-stats">
                    <h3>📖 已学习单词/📚 总单词库</h3>
                    <div class="stats-formula">
                        <span class="learned-count" data-testid="learned-words-count">{{ words_actually_learned }}</span>
                        <span class="divider">/</span>
                        <span class="total-count" data-testid="total-words-count">{{ total_words_in_db }}</span>
                    </div>
                    <div class="progress-text">学习进度: {{ "%.1f"|format((words_actually_learned / total_words_in_db * 100) if total_words_in_db > 0 else 0) }}%</div>
                </div>
            </div>
        </div>

        <!-- 第二行：综合熟练度分布 + 今日学习计划（左右布局）-->
        <div class="second-row" data-testid="second-section">
            <!-- 左侧：综合熟练度分布（60%宽度）-->
            <div class="proficiency-distribution-card" data-testid="proficiency-distribution">
                <h3>📊 综合熟练度分布</h3>
                <div class="proficiency-table">
                    <div class="proficiency-header">
                        <span>等级</span>
                        <span>评分范围</span>
                        <span>单词数</span>
                        <span>占比</span>
                    </div>
                    {% set simplified_levels = {
                        'proficient': proficiency_distribution.expert.count + proficiency_distribution.proficient.count,
                        'intermediate': proficiency_distribution.intermediate.count + proficiency_distribution.basic.count,
                        'beginner': proficiency_distribution.beginner.count + proficiency_distribution.unfamiliar.count
                    } %}
                    {% set total_words = simplified_levels.proficient + simplified_levels.intermediate + simplified_levels.beginner %}

                    <div class="proficiency-row proficient-border">
                        <span class="level-info">🏆 熟练</span>
                        <span class="score-range">80-100分</span>
                        <span class="word-count">{{ simplified_levels.proficient }}个</span>
                        <span class="percentage">{{ "%.1f"|format((simplified_levels.proficient / total_words * 100) if total_words > 0 else 0) }}%</span>
                    </div>

                    <div class="proficiency-row intermediate-border">
                        <span class="level-info">📚 中等</span>
                        <span class="score-range">60-79分</span>
                        <span class="word-count">{{ simplified_levels.intermediate }}个</span>
                        <span class="percentage">{{ "%.1f"|format((simplified_levels.intermediate / total_words * 100) if total_words > 0 else 0) }}%</span>
                    </div>

                    <div class="proficiency-row beginner-border">
                        <span class="level-info">🌱 初学</span>
                        <span class="score-range">0-59分</span>
                        <span class="word-count">{{ simplified_levels.beginner }}个</span>
                        <span class="percentage">{{ "%.1f"|format((simplified_levels.beginner / total_words * 100) if total_words > 0 else 0) }}%</span>
                    </div>
                </div>
            </div>

            <!-- 右侧：今日学习计划（40%宽度）-->
            <div class="today-plan" data-testid="today-plan-section">
                <h2 data-testid="plan-title">📋 今日学习计划</h2>
                <div class="plan-details" data-testid="plan-details">
                    <div class="plan-item" data-testid="new-words-plan">
                        <div class="label">🆕 新单词</div>
                        <div class="value" data-testid="new-words-count">{{ new_words_count }}</div>
                    </div>
                    <div class="plan-item" data-testid="review-words-plan">
                        <div class="label">🔄 复习单词</div>
                        <div class="value" data-testid="review-words-count">{{ review_words_count }}</div>
                    </div>
                    <div class="plan-item" data-testid="total-words-plan">
                        <div class="label">📊 总计</div>
                        <div class="value" data-testid="total-today-count">{{ new_words_count + review_words_count }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <a href="/learning?date={{ current_date }}" class="btn btn-primary">
                🚀 开始学习
            </a>
            <a href="/vocabulary_book" class="btn btn-secondary">
                📚 生词本
            </a>
            <a href="/feature-learning/insights" class="btn btn-info">
                🧠 学习洞察
            </a>
            <a href="/daily_words?date={{ current_date }}" class="btn btn-secondary">
                📝 查看详细计划
            </a>
            <a href="/api/words/import" class="btn" style="background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);">
                📥 导入新单词
            </a>
            {% if user.username == 'admin' %}
            <a href="/admin/database" class="btn" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);">
                🛠️ 数据库管理
            </a>
            {% endif %}
        </div>

        {% if new_words_count + review_words_count == 0 %}
        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
            <h3>🎉 今日学习任务已完成！</h3>
            <p>您今天没有新的学习任务，可以：</p>
            <ul style="text-align: left; display: inline-block;">
                <li>复习之前学过的单词</li>
                <li>查看学习报告</li>
                <li>准备明天的学习计划</li>
            </ul>
        </div>
        {% endif %}
    </div>

    <!-- 自定义退出登录确认对话框 -->
    <div id="logoutModal" class="custom-modal">
        <div class="modal-content">
            <div class="modal-title">🚪 确认退出</div>
            <div class="modal-message">
                确定要退出登录吗？<br>
                您的学习进度已自动保存。
            </div>
            <div class="modal-buttons">
                <button class="modal-btn modal-btn-cancel" onclick="closeLogoutModal()">取消</button>
                <button class="modal-btn modal-btn-confirm" onclick="confirmLogout()">退出登录</button>
            </div>
        </div>
    </div>

    <script>
        // 获取今天的日期字符串
        const today = '{{ current_date }}';



        // 页面加载时初始化用户下拉菜单功能
        window.addEventListener('DOMContentLoaded', function() {
            // 初始化用户下拉菜单功能
            initUserDropdown();
        });

        // 用户下拉菜单功能
        function initUserDropdown() {
            const dropdown = document.querySelector('.logout-dropdown');
            const dropdownContent = document.querySelector('.dropdown-content');
            const userAvatar = document.querySelector('.user-avatar');

            if (!dropdown || !dropdownContent || !userAvatar) return;

            // 点击头像切换下拉菜单
            userAvatar.addEventListener('click', function(e) {
                e.stopPropagation();
                const isVisible = dropdownContent.style.display === 'block';
                dropdownContent.style.display = isVisible ? 'none' : 'block';
            });

            // 点击页面其他地方关闭下拉菜单
            document.addEventListener('click', function(e) {
                if (!dropdown.contains(e.target)) {
                    dropdownContent.style.display = 'none';
                }
            });

            // 退出登录确认
            const logoutLink = document.querySelector('.logout-item');
            if (logoutLink) {
                logoutLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    showLogoutModal();
                });
            }
        }

        // 显示退出登录确认对话框
        function showLogoutModal() {
            const modal = document.getElementById('logoutModal');
            if (modal) {
                modal.style.display = 'block';
                // 防止背景滚动
                document.body.style.overflow = 'hidden';
            }
        }

        // 关闭退出登录确认对话框
        function closeLogoutModal() {
            const modal = document.getElementById('logoutModal');
            if (modal) {
                modal.style.display = 'none';
                // 恢复背景滚动
                document.body.style.overflow = 'auto';
            }
        }

        // 确认退出登录
        function confirmLogout() {
            const confirmBtn = document.querySelector('.modal-btn-confirm');
            if (confirmBtn) {
                // 显示退出中的提示
                confirmBtn.textContent = '🔄 退出中...';
                confirmBtn.disabled = true;
                confirmBtn.style.pointerEvents = 'none';
                confirmBtn.style.opacity = '0.7';

                // 延迟跳转，给用户反馈
                setTimeout(() => {
                    window.location.href = '/logout';
                }, 800);
            }
        }

        // 点击模态框背景关闭
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('logoutModal');
            if (e.target === modal) {
                closeLogoutModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeLogoutModal();
            }
        });



        // 设置用户进度条宽度
        function setProgressBar() {
            const progressFill = document.querySelector('.progress-fill');
            if (progressFill) {
                const progress = progressFill.getAttribute('data-progress');
                if (progress) {
                    progressFill.style.width = progress + '%';
                }
            }
        }

        // 页面加载完成后设置进度条
        document.addEventListener('DOMContentLoaded', setProgressBar);

        // 移除购物券功能 - 已移至learning界面
    </script>
</body>
</html>
