<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误 - 闹闹疯狂学单词</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        .error-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        .error-title {
            color: #333;
            font-size: 24px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .error-message {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .error-details {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 30px;
            font-family: monospace;
            font-size: 12px;
            color: #dc3545;
            text-align: left;
            overflow-x: auto;
        }
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }
        .btn-refresh {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">😵</div>
        <h1 class="error-title">哎呀，出错了！</h1>
        <p class="error-message">
            {{ error_message or '页面加载时遇到了问题，请稍后重试。' }}
        </p>
        
        {% if error_details %}
        <div class="error-details">
            <strong>错误详情：</strong><br>
            {{ error_details }}
        </div>
        {% endif %}
        
        <div class="action-buttons">
            <a href="/dashboard" class="btn btn-primary">
                🏠 返回首页
            </a>
            <button onclick="window.history.back()" class="btn btn-secondary">
                ⬅️ 返回上页
            </button>
            <button onclick="window.location.reload()" class="btn btn-refresh">
                🔄 刷新页面
            </button>
        </div>
    </div>
</body>
</html>
