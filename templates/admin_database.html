<!DOCTYPE html>
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库管理 - 管理员面板</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
        }
        .back-btn {
            background: #6c757d;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            cursor: pointer;
        }
        .back-btn:hover {
            background: #5a6268;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        .table-selector {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .filters-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        .filters-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .filters-header h4 {
            margin: 0;
            color: #495057;
        }
        .filters-toggle {
            background: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .filters-toggle:hover {
            background: #5a6268;
        }
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        .filter-group label {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #495057;
        }
        .filter-group input, .filter-group select {
            padding: 6px 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .filter-group input:focus, .filter-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        .filters-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        .filter-help {
            font-size: 11px;
            color: #6c757d;
            margin-top: 2px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-success:hover {
            background: #218838;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .btn-secondary:disabled {
            background: #6c757d;
            opacity: 0.6;
            cursor: not-allowed;
        }
        .batch-actions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
            align-items: center;
            justify-content: space-between;
        }
        .batch-actions.show {
            display: flex;
        }
        .batch-info {
            font-weight: bold;
            color: #856404;
        }
        .batch-buttons {
            display: flex;
            gap: 10px;
        }
        .select-all-container {
            margin: 10px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        .checkbox-cell {
            width: 40px;
            text-align: center;
        }
        .row-checkbox {
            transform: scale(1.2);
        }
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }
        th, td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        tr:nth-child(even) {
            background: #f9f9f9;
        }
        tr:hover {
            background: #e9ecef;
        }
        .editable {
            background: #fff3cd;
            cursor: pointer;
        }
        .editable:hover {
            background: #ffeaa7;
        }
        .pagination {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin-top: 20px;
        }
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
        }
        .pagination button:hover {
            background: #e9ecef;
        }
        .pagination button.active {
            background: #007bff;
            color: white;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 400px;
            max-width: 90%;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .password-required {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .status-message {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            display: none;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
        
        /* 重复单词管理样式 */
        .duplicate-words-list {
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .duplicates-summary {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: bold;
            color: #1976d2;
        }
        
        .no-duplicates {
            text-align: center;
            padding: 40px;
            color: #4caf50;
            font-size: 18px;
        }
        
        .duplicate-group {
            border: 2px solid #ff9800;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .group-header {
            background: #fff3e0;
            padding: 12px 15px;
            border-bottom: 1px solid #ffcc02;
        }
        
        .group-header h4 {
            margin: 0;
            color: #e65100;
        }
        
        .duplicate-words {
            padding: 15px;
        }
        
        .word-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin-bottom: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        
        .word-item:last-child {
            margin-bottom: 0;
        }
        
        .word-info {
            flex: 1;
        }
        
        .word-main {
            display: flex;
            gap: 15px;
            margin-bottom: 5px;
        }
        
        .word-id {
            font-weight: bold;
            color: #1976d2;
        }
        
        .word-chinese {
            font-weight: bold;
            color: #333;
        }
        
        .word-section, .word-type {
            color: #666;
            font-size: 14px;
        }
        
        .word-stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #999;
        }
        
        .stat {
            background: #e3f2fd;
            padding: 2px 6px;
            border-radius: 3px;
        }
        
        .word-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .word-actions input[type="radio"] {
            margin: 0;
        }
        
        .word-actions label {
            margin: 0;
            font-size: 14px;
            color: #4caf50;
            font-weight: bold;
        }
        
        .group-actions {
            background: #fff3e0;
            padding: 12px 15px;
            border-top: 1px solid #ffcc02;
            text-align: center;
        }
        
        .merge-info {
            padding: 20px;
        }
        
        .merge-info h4 {
            color: #e65100;
            margin-bottom: 15px;
        }
        
        .warning-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .warning-note strong {
            color: #856404;
        }
        
        .warning-note ul {
            margin: 10px 0 0 0;
            padding-left: 20px;
        }
        
        .warning-note li {
            margin-bottom: 5px;
            color: #856404;
        }
        
        /* 新的智能合并界面样式 */
        .merge-strategy {
            background: #f0f8ff;
            border: 1px solid #b0d4f1;
            border-radius: 6px;
            padding: 12px;
            margin-top: 10px;
        }
        
        .merge-strategy ul {
            margin: 5px 0 0 0;
            padding-left: 20px;
        }
        
        .merge-strategy li {
            margin-bottom: 3px;
            color: #2c5282;
        }
        
        .auto-merge-actions {
            text-align: center;
            padding: 20px;
            background: #f0fff4;
            border: 2px dashed #68d391;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .btn-lg {
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .duplicates-preview {
            border-top: 2px solid #e2e8f0;
            padding-top: 20px;
        }
        
        .duplicate-group.preview {
            border: 1px solid #cbd5e0;
            background: #f7fafc;
        }
        
        .merge-preview {
            padding: 15px;
        }
        
        .will-keep, .will-merge {
            margin-bottom: 12px;
        }
        
        .label.keep {
            color: #38a169;
            font-weight: bold;
            display: inline-block;
            width: 100px;
        }
        
        .label.merge {
            color: #d69e2e;
            font-weight: bold;
            display: inline-block;
            width: 100px;
        }
        
        .merge-words {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 5px;
        }
        
        .merge-word {
            background: #fed7d7;
            color: #c53030;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .auto-merge-info {
            background: #ebf8ff;
            border: 1px solid #90cdf4;
            border-radius: 6px;
            padding: 12px;
            margin: 10px 0;
        }
        
        .auto-merge-info ul {
            margin: 8px 0 0 0;
            padding-left: 20px;
        }
        
        .auto-merge-info li {
            margin-bottom: 5px;
            color: #2b6cb0;
        }
        
        /* 合并结果样式 */
        .merge-results {
            background: #f0fff4;
            border: 2px solid #68d391;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .results-summary {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            justify-content: center;
        }
        
        .result-stat {
            background: #c6f6d5;
            color: #22543d;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .results-details {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result-item {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 2fr;
            gap: 10px;
            padding: 8px;
            border-bottom: 1px solid #c6f6d5;
            align-items: center;
        }
        
        .result-word {
            font-weight: bold;
            color: #2d3748;
        }
        
        .result-kept {
            color: #38a169;
            font-weight: bold;
        }
        
        .result-merged {
            color: #d69e2e;
            font-size: 12px;
        }
        
        .result-chinese {
            color: #4a5568;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container" data-testid="admin-container">
        <div class="header" data-testid="admin-header">
            <h1 data-testid="admin-title">🛠️ 数据库管理</h1>
            <a href="/dashboard" class="back-btn">返回首页</a>
        </div>

        <div class="controls" data-testid="admin-controls">
            <select id="tableSelector" class="table-selector" data-testid="table-selector" title="选择数据库表">
                <option value="user">用户表 (User)</option>
                <option value="word">单词表 (Word)</option>
                <option value="user_word">用户单词关系 (UserWord)</option>
                <option value="learning_plan">学习计划 (LearningPlan)</option>
                <option value="word_record">学习记录 (WordRecord)</option>
                <option value="import_history">导入历史 (ImportHistory)</option>
                <option value="simple_status">系统状态 (SimpleStatus)</option>
            </select>
            <button id="refreshBtn" class="btn btn-primary" data-testid="refresh-button">刷新数据</button>
            <button id="testBtn" class="btn btn-secondary" onclick="testDirectAPI()">测试API</button>
            <!-- 单词表专用：重复单词管理 -->
            <button id="manageDuplicateWords" onclick="showDuplicateWordsModal()" class="btn btn-warning" style="display: none;">重复单词管理</button>
            <span id="recordCount" class="text-muted"></span>
        </div>

        <div id="statusMessage" class="status-message"></div>

        <!-- 批量操作区域 -->
        <div id="batchActions" class="batch-actions">
            <div class="batch-info">
                已选择 <span id="selectedCount">0</span> 条记录
            </div>
            <div class="batch-buttons">
                <button onclick="clearSelection()" class="btn btn-secondary">清空选择</button>
                <button onclick="batchDelete()" class="btn btn-danger">批量删除</button>
                <!-- 单词表专用：批量设置学习类型 -->
                <button id="batchSetLearningType" onclick="showBatchLearningTypeModal()" class="btn btn-info" style="display: none;">批量设置学习类型</button>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filters-section" id="filtersSection">
            <div class="filters-header">
                <h4>🔍 数据筛选</h4>
                <button type="button" class="filters-toggle" onclick="toggleFilters()">收起筛选</button>
            </div>
            <div id="filtersContent">
                <div class="filters-grid" id="filtersGrid">
                    <!-- 筛选字段将通过JavaScript动态生成 -->
                </div>
                <div class="filters-actions">
                    <button type="button" class="btn btn-secondary" onclick="clearFilters()">清空筛选</button>
                    <button type="button" class="btn btn-primary" onclick="applyFilters()">应用筛选</button>
                </div>
            </div>
        </div>

        <!-- 全选控制 -->
        <div id="selectAllContainer" class="select-all-container" style="display: none;">
            <label>
                <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                <strong>全选/取消全选当前页面的所有记录</strong>
            </label>
        </div>

        <div class="table-container">
            <div id="loading" class="loading">正在加载数据...</div>
            <table id="dataTable" style="display: none;">
                <thead id="tableHeader"></thead>
                <tbody id="tableBody"></tbody>
            </table>
        </div>

        <div class="pagination" id="pagination"></div>
    </div>

    <!-- 编辑模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑记录</h3>
                <span class="close" onclick="closeEditModal()">&times;</span>
            </div>
            <div id="editForm"></div>
            <div class="password-required">
                <strong>⚠️ 安全验证</strong><br>
                修改数据库需要管理员密码确认
            </div>
            <div class="form-group">
                <label for="adminPassword">管理员密码:</label>
                <input type="password" id="adminPassword" placeholder="请输入admin密码">
            </div>
            <div style="text-align: right; margin-top: 20px;">
                <button onclick="closeEditModal()" class="btn" style="background: #6c757d; color: white; margin-right: 10px;">取消</button>
                <button onclick="saveChanges()" class="btn btn-success">保存更改</button>
                <button onclick="deleteRecord()" class="btn btn-danger" style="margin-left: 10px;">删除记录</button>
            </div>
        </div>
    </div>

    <script>
        console.log('Script started loading...');

        let currentTable = 'user';
        let currentPage = 1;
        let currentData = [];
        let editingRecord = null;
        let filtersVisible = true;
        let currentFilters = {};
        let selectedRecords = new Set(); // 存储选中的记录ID
        console.log('Variables initialized...');

        // 页面加载时初始化
        function initializePage() {
            console.log('Initializing page...');
            console.log('Current table:', currentTable);
            console.log('Window location:', window.location.href);
            
            // 首先测试session状态
            fetch('/admin/test_session')
                .then(response => response.json())
                .then(sessionData => {
                    console.log('Session data:', sessionData);
                    if (!sessionData.is_admin) {
                        console.error('Not logged in as admin!');
                        showMessage('请先以admin身份登录', 'error');
                        return;
                    }
                    console.log('Admin session confirmed, proceeding...');
                })
                .catch(error => {
                    console.error('Session test failed:', error);
                });
            
            try {
                generateFilters();
                console.log('Filters generated, now loading data...');
                loadTableData();

                document.getElementById('tableSelector').addEventListener('change', function() {
                    currentTable = this.value;
                    currentPage = 1;
                    currentFilters = {};
                    selectedRecords.clear(); // 清空选择
                    updateBatchActions();
                    generateFilters();
                    loadTableData();
                });

                document.getElementById('refreshBtn').addEventListener('click', function() {
                    loadTableData();
                });
                console.log('Page initialization complete');
            } catch (error) {
                console.error('Error during initialization:', error);
                alert('初始化错误: ' + error.message);
            }
        }

        // 使用多种方法确保页面初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializePage);
        } else {
            initializePage();
        }

        // 生成筛选字段
        function generateFilters() {
            const filtersGrid = document.getElementById('filtersGrid');
            filtersGrid.innerHTML = '';

            const tableFields = getTableFields(currentTable);

            tableFields.forEach(field => {
                const filterGroup = document.createElement('div');
                filterGroup.className = 'filter-group';

                const label = document.createElement('label');
                label.textContent = field.displayName;
                filterGroup.appendChild(label);

                let input;
                if (field.type === 'boolean') {
                    input = document.createElement('select');
                    input.innerHTML = `
                        <option value="">全部</option>
                        <option value="true">是</option>
                        <option value="false">否</option>
                    `;
                } else if (field.type === 'date') {
                    input = document.createElement('input');
                    input.type = 'date';
                } else {
                    input = document.createElement('input');
                    input.type = 'text';
                    if (field.type === 'number') {
                        input.placeholder = '例: >5, <10, >=3, <=8, =5';
                    } else {
                        input.placeholder = '输入关键词搜索';
                    }
                }

                input.id = `filter_${field.name}`;
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        applyFilters();
                    }
                });

                filterGroup.appendChild(input);

                if (field.type === 'number') {
                    const help = document.createElement('div');
                    help.className = 'filter-help';
                    help.textContent = '支持: >5, <10, >=3, <=8, =5';
                    filterGroup.appendChild(help);
                }

                filtersGrid.appendChild(filterGroup);
            });
        }

        // 获取表字段配置 - 基于data_model.md的实际数据库结构
        function getTableFields(tableName) {
            const fieldConfigs = {
                'user': [
                    { name: 'id', displayName: 'ID', type: 'number' },
                    { name: 'username', displayName: '用户名', type: 'text' },
                    { name: 'password', displayName: '密码', type: 'text' },
                    { name: 'registration_date', displayName: '注册日期', type: 'date' },
                    { name: 'points', displayName: '积分', type: 'number' },
                    { name: 'vouchers', displayName: '代金券', type: 'number' },
                    { name: 'created_at', displayName: '创建时间', type: 'date' },
                    { name: 'updated_at', displayName: '更新时间', type: 'date' }
                ],
                'word': [
                    { name: 'id', displayName: 'ID', type: 'number' },
                    { name: 'english_word', displayName: '英文单词', type: 'text' },
                    { name: 'chinese_meaning', displayName: '中文释义', type: 'text' },
                    { name: 'section', displayName: '章节分类', type: 'text' },
                    { name: 'learning_requirement', displayName: '学习要求', type: 'text' }
                ],
                'user_word': [
                    { name: 'id', displayName: 'ID', type: 'number' },
                    { name: 'user_id', displayName: '用户ID', type: 'number' },
                    { name: 'word_id', displayName: '单词ID', type: 'number' },
                    { name: 'learning_count', displayName: '学习次数', type: 'number' },
                    { name: 'correct_count', displayName: '正确次数', type: 'number' },
                    { name: 'last_learning_date', displayName: '最后学习日期', type: 'date' },
                    { name: 'memory_method', displayName: '记忆方法', type: 'text' },
                    { name: 'status', displayName: '状态', type: 'text' },
                    { name: 'proficiency', displayName: '熟练度', type: 'number' }
                ],
                'learning_plan': [
                    { name: 'id', displayName: 'ID', type: 'number' },
                    { name: 'user_id', displayName: '用户ID', type: 'number' },
                    { name: 'word_id', displayName: '单词ID', type: 'number' },
                    { name: 'planned_date', displayName: '计划日期', type: 'date' },
                    { name: 'item_type', displayName: '词条类型', type: 'text' },
                    { name: 'star_level', displayName: '星级', type: 'number' }
                ],
                'word_record': [
                    { name: 'id', displayName: 'ID', type: 'number' },
                    { name: 'user_id', displayName: '用户ID', type: 'number' },
                    { name: 'word_id', displayName: '单词ID', type: 'number' },
                    { name: 'date', displayName: '学习日期', type: 'date' },
                    { name: 'duration_seconds', displayName: '耗时(秒)', type: 'number' },
                    { name: 'user_input', displayName: '用户输入', type: 'text' },
                    { name: 'answer', displayName: '正确答案', type: 'text' },
                    { name: 'is_correct', displayName: '是否正确', type: 'boolean' }
                ],
                'import_history': [
                    { name: 'id', displayName: 'ID', type: 'number' },
                    { name: 'import_type', displayName: '导入类型', type: 'text' },
                    { name: 'filename', displayName: '文件名', type: 'text' },
                    { name: 'total_records', displayName: '总记录数', type: 'number' },
                    { name: 'new_words', displayName: '新增单词', type: 'number' },
                    { name: 'updated_words', displayName: '更新单词', type: 'number' },
                    { name: 'error_count', displayName: '错误数量', type: 'number' },
                    { name: 'start_time', displayName: '开始时间', type: 'date' },
                    { name: 'status', displayName: '状态', type: 'text' }
                ],
                'simple_status': [
                    { name: 'id', displayName: 'ID', type: 'number' },
                    { name: 'status', displayName: '状态信息', type: 'text' },
                    { name: 'created_at', displayName: '创建时间', type: 'date' }
                ]
            };

            return fieldConfigs[tableName] || [];
        }

        // 加载表格数据
        function loadTableData() {
            console.log('=== loadTableData called ===');
            console.log('currentTable:', currentTable);
            console.log('currentPage:', currentPage);
            console.log('currentFilters:', currentFilters);
            
            document.getElementById('loading').style.display = 'block';
            document.getElementById('dataTable').style.display = 'none';

            // 构建查询参数
            let queryParams = `table=${currentTable}&page=${currentPage}&per_page=50`;
            console.log('Base query params:', queryParams);

            // 添加筛选参数
            Object.keys(currentFilters).forEach(key => {
                if (currentFilters[key]) {
                    queryParams += `&filter_${key}=${encodeURIComponent(currentFilters[key])}`;
                }
            });

            console.log('Starting fetch request...');
            console.log('Query URL:', `/admin/database/data?table=${currentTable}&${queryParams}`);
            fetch(`/admin/database/data?table=${currentTable}&${queryParams}`)
                .then(response => {
                    console.log('Fetch response received:', response.status, response.statusText);
                    if (!response.ok) {
                        console.error('Response not ok:', response.status, response.statusText);
                        return response.text().then(text => {
                            console.error('Error response body:', text);
                            throw new Error(`HTTP ${response.status}: ${text}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Data received:', data);
                    if (data.error) {
                        console.log('Error in data:', data.error);
                        showMessage(data.error, 'error');
                        return;
                    }

                    currentData = data.data;
                    console.log('About to call renderTable...');
                    renderTable(data);
                    renderPagination(data);
                    
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('dataTable').style.display = 'table';
                    document.getElementById('selectAllContainer').style.display = 'block';
                    document.getElementById('recordCount').textContent = `共 ${data.total} 条记录`;
                    
                    // 清空之前的选择状态
                    selectedRecords.clear();
                    updateBatchActions();
                    updateSelectAllCheckbox();
                })
                .catch(error => {
                    console.error('Error loading data:', error);
                    showMessage('加载数据失败', 'error');
                    document.getElementById('loading').style.display = 'none';
                });
        }

        // 渲染表格
        function renderTable(data) {
            console.log('renderTable called with data:', data);
            const header = document.getElementById('tableHeader');
            const body = document.getElementById('tableBody');

            // 清空现有内容
            header.innerHTML = '';
            body.innerHTML = '';

            if (data.data.length === 0) {
                body.innerHTML = '<tr><td colspan="100%" style="text-align: center;">暂无数据</td></tr>';
                return;
            }
            
            // 生成表头
            const headerRow = document.createElement('tr');
            
            // 添加选择列表头
            const selectTh = document.createElement('th');
            selectTh.className = 'checkbox-cell';
            selectTh.innerHTML = '<input type="checkbox" id="headerCheckbox" onchange="toggleSelectAll()">';
            headerRow.appendChild(selectTh);
            
            const keys = Object.keys(data.data[0]).filter(key => key !== 'password');
            console.log('Table keys:', keys);
            keys.forEach(key => {
                const th = document.createElement('th');
                th.textContent = getColumnDisplayName(key);
                headerRow.appendChild(th);
            });

            // 添加操作列表头
            const actionTh = document.createElement('th');
            actionTh.textContent = '操作';
            headerRow.appendChild(actionTh);
            console.log('Added action column header');

            header.appendChild(headerRow);
            
            // 生成表格内容
            data.data.forEach((record, index) => {
                const row = document.createElement('tr');
                
                // 添加选择复选框
                const selectTd = document.createElement('td');
                selectTd.className = 'checkbox-cell';
                const isAdminUser = currentTable === 'user' && record.username === 'admin';
                if (isAdminUser) {
                    selectTd.innerHTML = '<input type="checkbox" disabled title="admin用户不可删除">';
                } else {
                    selectTd.innerHTML = `<input type="checkbox" class="row-checkbox" data-record-id="${record.id}" onchange="toggleRecordSelection(${record.id})">`;
                }
                row.appendChild(selectTd);
                
                keys.forEach(key => {
                    if (key === 'password') return; // 跳过密码字段
                    const td = document.createElement('td');

                    // 特殊处理布尔值字段
                    if (key === 'is_new' || key === 'is_correct' || key === 'shifted' || key === 'used_ai_help') {
                        if (record[key] === true) {
                            td.textContent = '是';
                        } else if (record[key] === false) {
                            td.textContent = '否';
                        } else {
                            td.textContent = '';
                        }
                    } else if (key === 'learning_requirement') {
                        // 特殊处理学习要求字段
                        if (record[key] === 'spelling') {
                            td.textContent = '拼写要求';
                            td.style.color = '#28a745';
                            td.style.fontWeight = 'bold';
                        } else if (record[key] === 'recognition') {
                            td.textContent = '认识即可';
                            td.style.color = '#ffc107';
                            td.style.fontWeight = 'bold';
                        } else {
                            td.textContent = record[key] || '';
                        }
                    } else {
                        td.textContent = record[key] || '';
                    }

                    if (isEditableField(key)) {
                        td.className = 'editable';
                        td.title = '点击编辑';
                    }
                    row.appendChild(td);
                });
                
                // 操作列
                const actionTd = document.createElement('td');
                let actionButtons = `<button onclick="editRecord(${index})" class="btn btn-primary" style="font-size: 12px; padding: 4px 8px; margin-right: 5px;">编辑</button>`;

                // 添加删除按钮，但对admin用户禁用
                if (isAdminUser) {
                    actionButtons += `<button class="btn btn-secondary" style="font-size: 12px; padding: 4px 8px;" disabled title="不能删除admin用户">删除</button>`;
                } else {
                    actionButtons += `<button onclick="confirmDelete(${index})" class="btn btn-danger" style="font-size: 12px; padding: 4px 8px;">删除</button>`;
                }

                actionTd.innerHTML = actionButtons;
                row.appendChild(actionTd);
                console.log(`Added action buttons for record ${index}:`, actionButtons);

                body.appendChild(row);
            });
        }

        // 获取列显示名称
        function getColumnDisplayName(key) {
            const displayNames = {
                'id': 'ID',
                'username': '用户名',
                'registration_date': '注册日期',
                'password': '密码哈希',
                'word': '单词',
                'answer': '答案',
                'section': '章节',
                'user_id': '用户ID',
                'word_id': '单词ID',
                'recitation_count': '学习次数',
                'correct_count': '正确次数',
                'proficiency': '熟练度',
                'last_reviewed': '最后学习',
                'interval': '间隔天数',
                'ef': 'EF值',
                'repetition': '复习次数',
                'remark': '备注',
                'planned_date': '计划日期',
                'is_new': '是否新词',
                'review_count': '复习次数',
                'shifted': '是否顺延',
                'date': '日期',
                'user_input': '用户输入',
                'is_correct': '是否正确',
                'duration_seconds': '耗时(秒)',
                'used_ai_help': '使用AI帮助',
                'learning_requirement': '学习要求'
            };
            return displayNames[key] || key;
        }

        // 判断字段是否可编辑
        function isEditableField(key) {
            const nonEditableFields = ['id', 'password'];
            return !nonEditableFields.includes(key);
        }

        // 编辑记录
        function editRecord(index) {
            editingRecord = currentData[index];
            showEditModal(editingRecord);
        }

        // 显示编辑模态框
        function showEditModal(record) {
            const modal = document.getElementById('editModal');
            const form = document.getElementById('editForm');
            
            form.innerHTML = '';
            
            Object.keys(record).forEach(key => {
                if (isEditableField(key)) {
                    const formGroup = document.createElement('div');
                    formGroup.className = 'form-group';
                    
                    const label = document.createElement('label');
                    label.textContent = getColumnDisplayName(key) + ':';
                    formGroup.appendChild(label);
                    
                    let input;
                    if (key === 'is_new' || key === 'is_correct' || key === 'shifted' || key === 'used_ai_help') {
                        input = document.createElement('select');
                        input.innerHTML = '<option value="true">是</option><option value="false">否</option>';
                        input.value = record[key] ? 'true' : 'false';
                    } else if (key === 'learning_requirement') {
                        input = document.createElement('select');
                        input.innerHTML = '<option value="spelling">拼写要求</option><option value="recognition">认识即可</option>';
                        input.value = record[key] || 'spelling';
                    } else {
                        input = document.createElement('input');
                        input.type = 'text';
                        input.value = record[key] || '';
                    }
                    
                    input.id = 'edit_' + key;
                    formGroup.appendChild(input);
                    form.appendChild(formGroup);
                }
            });
            
            modal.style.display = 'block';
        }

        // 关闭编辑模态框
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
            document.getElementById('adminPassword').value = '';
            editingRecord = null;
        }

        // 保存更改
        function saveChanges() {
            if (!editingRecord) return;
            
            const password = document.getElementById('adminPassword').value;
            if (!password) {
                showMessage('请输入管理员密码', 'error');
                return;
            }
            
            const updates = {};
            Object.keys(editingRecord).forEach(key => {
                if (isEditableField(key)) {
                    const input = document.getElementById('edit_' + key);
                    if (input) {
                        let value = input.value;
                        if (key === 'is_new' || key === 'is_correct' || key === 'shifted' || key === 'used_ai_help') {
                            value = value === 'true';
                        }
                        updates[key] = value;
                    }
                }
            });
            
            fetch('/admin/database/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    table: currentTable,
                    id: editingRecord.id,
                    updates: updates,
                    admin_password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('更新成功', 'success');
                    closeEditModal();
                    loadTableData();
                } else {
                    showMessage(data.error || '更新失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error updating record:', error);
                showMessage('更新失败', 'error');
            });
        }

        // 确认删除记录（独立删除功能）
        function confirmDelete(index) {
            const record = currentData[index];
            if (!record) return;

            // 检查是否是admin用户
                            if (currentTable === 'user' && record.username === 'admin') {
                showMessage('不能删除admin用户', 'error');
                return;
            }

            // 根据表类型显示不同的删除警告
            let confirmMessage = '确定要删除这条记录吗？此操作不可恢复！';
            let recordIdentifier = `ID: ${record.id}`;

            if (currentTable === 'user') {
                recordIdentifier = `用户: ${record.username}`;
                confirmMessage = `⚠️ 警告：删除用户 "${record.username}" 将会：\n\n` +
                    `• 删除该用户的所有学习记录\n` +
                    `• 删除该用户的所有学习计划\n` +
                    `• 删除该用户的所有单词关系\n` +
                    `• 删除用户账户本身\n\n` +
                    `这是级联删除操作，不可恢复！\n\n确定要继续吗？`;
            } else if (currentTable === 'word') {
                recordIdentifier = `单词: ${record.word}`;
                confirmMessage = `⚠️ 警告：删除单词 "${record.word}" 将会：\n\n` +
                    `• 删除所有用户对该单词的学习记录\n` +
                    `• 删除所有用户对该单词的学习计划\n` +
                    `• 删除所有用户与该单词的关系\n` +
                    `• 删除单词本身\n\n` +
                    `这是级联删除操作，不可恢复！\n\n确定要继续吗？`;
            } else if (currentTable === 'user_word') {
                recordIdentifier = `用户单词关系: ${record.username || 'ID:' + record.user_id} - ${record.word || 'ID:' + record.word_id}`;
            } else if (currentTable === 'learning_plan') {
                recordIdentifier = `学习计划: ${record.username || 'ID:' + record.user_id} - ${record.word || 'ID:' + record.word_id}`;
            } else if (currentTable === 'word_record') {
                recordIdentifier = `学习记录: ${record.username || 'ID:' + record.user_id} - ${record.word || 'ID:' + record.word_id}`;
            }

            if (!confirm(confirmMessage)) {
                return;
            }

            // 弹出密码输入框
            const password = prompt('请输入管理员密码以确认删除操作：');
            if (!password) {
                showMessage('删除操作已取消', 'error');
                return;
            }

            // 执行删除
            executeDelete(record.id, password, recordIdentifier);
        }

        // 执行删除操作
        function executeDelete(recordId, password, recordIdentifier) {
            fetch('/admin/database/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    table: currentTable,
                    id: recordId,
                    admin_password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(`删除成功: ${recordIdentifier}`, 'success');
                    loadTableData(); // 重新加载数据
                } else {
                    showMessage(data.error || '删除失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error deleting record:', error);
                showMessage('删除失败: 网络错误', 'error');
            });
        }

        // 删除记录（从编辑模态框调用）
        function deleteRecord() {
            if (!editingRecord) return;

            // 根据表类型显示不同的删除警告
            let confirmMessage = '确定要删除这条记录吗？此操作不可恢复！';

            if (currentTable === 'user') {
                if (editingRecord.username === 'admin') {
                    showMessage('不能删除admin用户', 'error');
                    return;
                }
                confirmMessage = `⚠️ 警告：删除用户 "${editingRecord.username}" 将会：\n\n` +
                    `• 删除该用户的所有学习记录\n` +
                    `• 删除该用户的所有学习计划\n` +
                    `• 删除该用户的所有单词关系\n` +
                    `• 删除用户账户本身\n\n` +
                    `这是级联删除操作，不可恢复！\n\n确定要继续吗？`;
            } else if (currentTable === 'word') {
                confirmMessage = `⚠️ 警告：删除单词 "${editingRecord.word}" 将会：\n\n` +
                    `• 删除所有用户对该单词的学习记录\n` +
                    `• 删除所有用户对该单词的学习计划\n` +
                    `• 删除所有用户与该单词的关系\n` +
                    `• 删除单词本身\n\n` +
                    `这是级联删除操作，不可恢复！\n\n确定要继续吗？`;
            }

            if (!confirm(confirmMessage)) {
                return;
            }

            const password = document.getElementById('adminPassword').value;
            if (!password) {
                showMessage('请输入管理员密码', 'error');
                return;
            }
            
            fetch('/admin/database/delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    table: currentTable,
                    id: editingRecord.id,
                    admin_password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage('删除成功', 'success');
                    closeEditModal();
                    loadTableData();
                } else {
                    showMessage(data.error || '删除失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error deleting record:', error);
                showMessage('删除失败', 'error');
            });
        }

        // 渲染分页
        function renderPagination(data) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';
            
            if (data.total_pages <= 1) return;
            
            // 上一页
            if (currentPage > 1) {
                const prevBtn = document.createElement('button');
                prevBtn.textContent = '上一页';
                prevBtn.onclick = () => {
                    currentPage--;
                    loadTableData();
                };
                pagination.appendChild(prevBtn);
            }
            
            // 页码
            for (let i = 1; i <= data.total_pages; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.className = i === currentPage ? 'active' : '';
                pageBtn.onclick = () => {
                    currentPage = i;
                    loadTableData();
                };
                pagination.appendChild(pageBtn);
            }
            
            // 下一页
            if (currentPage < data.total_pages) {
                const nextBtn = document.createElement('button');
                nextBtn.textContent = '下一页';
                nextBtn.onclick = () => {
                    currentPage++;
                    loadTableData();
                };
                pagination.appendChild(nextBtn);
            }
        }

        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.getElementById('statusMessage');
            messageDiv.textContent = message;
            messageDiv.className = `status-message status-${type}`;
            messageDiv.style.display = 'block';
            
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }

        // 筛选相关函数
        function toggleFilters() {
            const filtersContent = document.getElementById('filtersContent');
            const toggleBtn = document.querySelector('.filters-toggle');

            if (filtersVisible) {
                filtersContent.style.display = 'none';
                toggleBtn.textContent = '展开筛选';
                filtersVisible = false;
            } else {
                filtersContent.style.display = 'block';
                toggleBtn.textContent = '收起筛选';
                filtersVisible = true;
            }
        }

        function applyFilters() {
            currentFilters = {};
            const tableFields = getTableFields(currentTable);

            tableFields.forEach(field => {
                const input = document.getElementById(`filter_${field.name}`);
                if (input && input.value.trim()) {
                    currentFilters[field.name] = input.value.trim();
                }
            });

            currentPage = 1; // 重置到第一页
            loadTableData();
        }

        function clearFilters() {
            currentFilters = {};
            const tableFields = getTableFields(currentTable);

            tableFields.forEach(field => {
                const input = document.getElementById(`filter_${field.name}`);
                if (input) {
                    input.value = '';
                }
            });

            currentPage = 1;
            loadTableData();
        }

        // 多选相关函数
        function toggleRecordSelection(recordId) {
            if (selectedRecords.has(recordId)) {
                selectedRecords.delete(recordId);
            } else {
                selectedRecords.add(recordId);
            }
            updateBatchActions();
            updateSelectAllCheckbox();
        }

        function toggleSelectAll() {
            const headerCheckbox = document.getElementById('headerCheckbox');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const rowCheckboxes = document.querySelectorAll('.row-checkbox:not(:disabled)');
            
            // 同步两个全选复选框的状态
            if (headerCheckbox && selectAllCheckbox) {
                if (headerCheckbox.checked !== selectAllCheckbox.checked) {
                    selectAllCheckbox.checked = headerCheckbox.checked;
                }
            }
            
            const isChecked = headerCheckbox ? headerCheckbox.checked : selectAllCheckbox.checked;
            
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                const recordId = parseInt(checkbox.dataset.recordId);
                if (isChecked) {
                    selectedRecords.add(recordId);
                } else {
                    selectedRecords.delete(recordId);
                }
            });
            
            updateBatchActions();
        }

        function updateSelectAllCheckbox() {
            const headerCheckbox = document.getElementById('headerCheckbox');
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const rowCheckboxes = document.querySelectorAll('.row-checkbox:not(:disabled)');
            const checkedCheckboxes = document.querySelectorAll('.row-checkbox:not(:disabled):checked');
            
            const allChecked = rowCheckboxes.length > 0 && rowCheckboxes.length === checkedCheckboxes.length;
            const someChecked = checkedCheckboxes.length > 0;
            
            if (headerCheckbox) {
                headerCheckbox.checked = allChecked;
                headerCheckbox.indeterminate = someChecked && !allChecked;
            }
            
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = someChecked && !allChecked;
            }
        }

        function updateBatchActions() {
            const batchActions = document.getElementById('batchActions');
            const selectedCount = document.getElementById('selectedCount');
            
            selectedCount.textContent = selectedRecords.size;
            
            if (selectedRecords.size > 0) {
                batchActions.classList.add('show');
            } else {
                batchActions.classList.remove('show');
            }
        }

        function clearSelection() {
            selectedRecords.clear();
            
            // 取消所有复选框的选中状态
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            
            updateBatchActions();
            updateSelectAllCheckbox();
        }

        function batchDelete() {
            if (selectedRecords.size === 0) {
                showMessage('请选择要删除的记录', 'error');
                return;
            }

            // 检查是否包含admin用户
            const selectedData = currentData.filter(record => selectedRecords.has(record.id));
            const hasAdminUser = selectedData.some(record => 
                currentTable === 'user' && record.username === 'admin'
            );
            
            if (hasAdminUser) {
                showMessage('选择的记录中包含admin用户，无法批量删除', 'error');
                return;
            }

            // 构建确认消息
            let confirmMessage = `确定要删除选中的 ${selectedRecords.size} 条记录吗？此操作不可恢复！\n\n`;
            
            if (currentTable === 'user') {
                confirmMessage += `⚠️ 警告：批量删除用户将会：\n` +
                    `• 删除这些用户的所有学习记录\n` +
                    `• 删除这些用户的所有学习计划\n` +
                    `• 删除这些用户的所有单词关系\n` +
                    `• 删除用户账户本身\n\n` +
                    `这是级联删除操作，不可恢复！\n\n`;
            } else if (currentTable === 'word') {
                confirmMessage += `⚠️ 警告：批量删除单词将会：\n` +
                    `• 删除所有用户对这些单词的学习记录\n` +
                    `• 删除所有用户对这些单词的学习计划\n` +
                    `• 删除所有用户与这些单词的关系\n` +
                    `• 删除单词本身\n\n` +
                    `这是级联删除操作，不可恢复！\n\n`;
            }
            
            confirmMessage += `选中的记录：\n`;
            selectedData.slice(0, 5).forEach(record => {
                if (currentTable === 'user') {
                    confirmMessage += `• 用户: ${record.username}\n`;
                } else if (currentTable === 'word') {
                    confirmMessage += `• 单词: ${record.word}\n`;
                } else {
                    confirmMessage += `• ID: ${record.id}\n`;
                }
            });
            
            if (selectedData.length > 5) {
                confirmMessage += `• ... 还有 ${selectedData.length - 5} 条记录\n`;
            }
            
            confirmMessage += `\n确定要继续吗？`;

            if (!confirm(confirmMessage)) {
                return;
            }

            // 弹出密码输入框
            const password = prompt('请输入管理员密码以确认批量删除操作：');
            if (!password) {
                showMessage('批量删除操作已取消', 'error');
                return;
            }

            // 执行批量删除
            executeBatchDelete(Array.from(selectedRecords), password);
        }

        function executeBatchDelete(recordIds, password) {
            fetch('/admin/database/batch_delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    table: currentTable,
                    ids: recordIds,
                    admin_password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(`批量删除成功: ${data.deleted_count} 条记录已删除`, 'success');
                    selectedRecords.clear();
                    updateBatchActions();
                    loadTableData(); // 重新加载数据
                } else {
                    showMessage(data.error || '批量删除失败', 'error');
                }
            })
            .catch(error => {
                console.error('Error in batch delete:', error);
                showMessage('批量删除失败: 网络错误', 'error');
            });
        }

        // 测试API直接调用
        function testDirectAPI() {
            console.log('=== Testing Direct API Call ===');
            const testUrl = `/admin/database/data?table=user&page=1&per_page=5`;
            console.log('Test URL:', testUrl);
            
            fetch(testUrl)
                .then(response => {
                    console.log('Direct API Response:', response.status, response.statusText);
                    return response.json();
                })
                .then(data => {
                    console.log('Direct API Data:', data);
                    alert('API测试成功！数据条数: ' + (data.data ? data.data.length : '无数据'));
                })
                .catch(error => {
                    console.error('Direct API Error:', error);
                    alert('API测试失败: ' + error.message);
                });
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                closeEditModal();
            }
        }
    </script>

    <!-- 批量设置学习类型模态框 -->
    <div id="batchLearningTypeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>批量设置学习类型</h3>
                <span class="close" onclick="closeBatchLearningTypeModal()">&times;</span>
            </div>
            <div class="form-group">
                <label for="batchLearningTypeSelect">选择学习类型：</label>
                <select id="batchLearningTypeSelect" class="form-control">
                    <option value="spelling">Spelling（拼写模式）</option>
                    <option value="recognition">Recognition（认读模式）</option>
                </select>
                <div class="form-hint">
                    • Spelling模式：要求完全拼写英文单词（默认，适合核心词汇）<br>
                    • Recognition模式：中英双向选择题（适合词汇扩展、小学生）
                </div>
            </div>
            <div class="password-required">
                <strong>⚠️ 安全验证</strong><br>
                批量修改单词类型需要管理员密码确认
                <div class="form-group">
                    <label for="batchAdminPassword">管理员密码：</label>
                    <input type="password" id="batchAdminPassword" class="form-control">
                </div>
            </div>
            <div class="modal-actions">
                <button onclick="executeBatchLearningTypeUpdate()" class="btn btn-primary">确认修改</button>
                <button onclick="closeBatchLearningTypeModal()" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 重复单词管理模态框 -->
    <div id="duplicateWordsModal" class="modal" style="display: none;">
        <div class="modal-content" style="max-width: 1000px; max-height: 80vh; overflow-y: auto;">
            <div class="modal-header">
                <h3>🔄 重复单词管理 - 全库检查</h3>
                <span class="close" onclick="closeDuplicateWordsModal()">&times;</span>
            </div>
            <div id="duplicateWordsList" class="duplicate-words-list">
                <div class="loading">正在加载重复单词...</div>
            </div>
        </div>
    </div>

    <!-- 单词合并确认模态框 -->
    <div id="mergeWordsModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认合并单词</h3>
                <span class="close" onclick="closeMergeWordsModal()">&times;</span>
            </div>
            <div id="mergeWordsContent">
                <!-- 动态生成内容 -->
            </div>
            <div class="password-required">
                <strong>⚠️ 安全验证</strong><br>
                合并单词将联动更新全库所有相关表，自动同步到用户学习记录，需要管理员密码确认
                <div class="form-group">
                    <label for="mergeAdminPassword">管理员密码：</label>
                    <input type="password" id="mergeAdminPassword" class="form-control">
                </div>
            </div>
            <div class="modal-actions">
                <button onclick="executeMergeWords()" class="btn btn-danger">确认合并</button>
                <button onclick="closeMergeWordsModal()" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <script>
        // 显示批量设置学习类型模态框
        function showBatchLearningTypeModal() {
            if (selectedRecords.size === 0) {
                showMessage('请选择要修改的单词记录', 'error');
                return;
            }
            
            // 只对word表显示此功能
            if (currentTable !== 'word') {
                showMessage('批量设置学习类型仅适用于单词表', 'error');
                return;
            }
            
            document.getElementById('batchLearningTypeModal').style.display = 'block';
        }
        
        // 关闭批量设置学习类型模态框
        function closeBatchLearningTypeModal() {
            document.getElementById('batchLearningTypeModal').style.display = 'none';
            document.getElementById('batchAdminPassword').value = '';
        }
        
        // 执行批量学习类型更新
        function executeBatchLearningTypeUpdate() {
            const learningType = document.getElementById('batchLearningTypeSelect').value;
            const password = document.getElementById('batchAdminPassword').value;
            
            if (!password) {
                showMessage('请输入管理员密码', 'error');
                return;
            }
            
            const selectedIds = Array.from(selectedRecords);
            const confirmMessage = `确认将${selectedIds.length}个单词的学习类型设置为"${learningType === 'spelling' ? '拼写模式' : '认读模式'}"吗？`;
            
            if (!confirm(confirmMessage)) {
                return;
            }
            
            showMessage('正在批量更新学习类型...', 'info');
            
            fetch('/admin/batch_update_learning_type', {
                method: 'POST',  
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    word_ids: selectedIds,
                    learning_requirement: learningType,
                    admin_password: password
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(`✅ 成功更新${data.updated_count}个单词的学习类型`, 'success');
                    closeBatchLearningTypeModal();
                    clearSelection();
                    loadTableData(); // 重新加载数据
                } else {
                    showMessage(`❌ 批量更新失败: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                console.error('批量更新学习类型异常:', error);
                showMessage('❌ 网络错误，请稍后重试', 'error');
            });
        }
        
        // 扩展现有的loadTableData函数，为word表显示专用按钮
        const originalLoadTableData = loadTableData;
        loadTableData = function() {
            originalLoadTableData();
            updateWordTableButtons();
        };
        
        // 更新单词表专用按钮的显示状态
        function updateWordTableButtons() {
            const batchButton = document.getElementById('batchSetLearningType');
            const duplicateButton = document.getElementById('manageDuplicateWords');
            if (currentTable === 'word') {
                batchButton.style.display = 'inline-block';
                duplicateButton.style.display = 'inline-block';
            } else {
                batchButton.style.display = 'none';
                duplicateButton.style.display = 'none';
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateWordTableButtons();
        });
        
        // 扩展模态框关闭事件
        const originalWindowOnclick = window.onclick;
        window.onclick = function(event) {
            const editModal = document.getElementById('editModal');
            const batchModal = document.getElementById('batchLearningTypeModal');
            
            if (event.target === editModal) {
                closeEditModal();
            } else if (event.target === batchModal) {
                closeBatchLearningTypeModal();
            }
        };

        // ========== 重复单词管理功能 ==========
        
        // 存储当前选择的合并信息
        let currentMergeData = null;
        
        // 显示重复单词管理模态框
        function showDuplicateWordsModal() {
            // 检查当前是否在单词表页面
            if (currentTable !== 'word') {
                showMessage('重复单词管理仅适用于单词表', 'error');
                return;
            }
            
            document.getElementById('duplicateWordsModal').style.display = 'block';
            loadDuplicateWords();
        }
        
        // 关闭重复单词管理模态框
        function closeDuplicateWordsModal() {
            document.getElementById('duplicateWordsModal').style.display = 'none';
        }
        
        // 关闭单词合并确认模态框
        function closeMergeWordsModal() {
            document.getElementById('mergeWordsModal').style.display = 'none';
            document.getElementById('mergeAdminPassword').value = '';
            currentMergeData = null;
        }
        
        // 加载重复单词数据
        function loadDuplicateWords() {
            const listContainer = document.getElementById('duplicateWordsList');
            listContainer.innerHTML = '<div class="loading">正在加载重复单词...</div>';
            
            fetch('/api/admin/find_duplicate_words')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayDuplicateWords(data.duplicates);
                    } else {
                        listContainer.innerHTML = `<div class="error">加载失败: ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('加载重复单词失败:', error);
                    listContainer.innerHTML = '<div class="error">网络错误，请稍后重试</div>';
                });
        }
        
        // 显示重复单词列表
        function displayDuplicateWords(duplicates) {
            const listContainer = document.getElementById('duplicateWordsList');
            
            if (duplicates.length === 0) {
                listContainer.innerHTML = '<div class="no-duplicates">🎉 太好了！没有发现重复的单词</div>';
                return;
            }
            
            let html = `
                <div class="duplicates-summary">
                    📊 全库扫描结果：发现 ${duplicates.length} 组重复单词，合并后将自动同步到所有用户的学习记录中
                    <div class="merge-strategy">
                        <strong>🤖 智能合并策略：</strong>
                        <ul>
                            <li>自动保留学习记录最多的单词ID</li>
                            <li>智能合并所有中文释义</li>
                            <li>联动更新所有相关表格</li>
                        </ul>
                    </div>
                </div>
                <div class="auto-merge-actions">
                    <button onclick="showAutoMergeModal()" class="btn btn-success btn-lg">
                        🚀 一键智能合并所有重复单词
                    </button>
                    <button onclick="testMergeAPI()" class="btn btn-secondary btn-sm" style="margin-left: 10px;">
                        🧪 测试API连接
                    </button>
                </div>
                <div class="duplicates-preview">
                    <h4>📋 重复单词预览</h4>
            `;
            
            duplicates.forEach((group, index) => {
                // 智能选择最佳保留单词（与后端逻辑一致）
                const bestWord = group.words.reduce((best, current) => {
                    if (current.record_count > best.record_count) return current;
                    if (current.record_count === best.record_count && current.user_count > best.user_count) return current;
                    if (current.record_count === best.record_count && current.user_count === best.user_count && current.id < best.id) return current;
                    return best;
                });
                
                const mergeWords = group.words.filter(w => w.id !== bestWord.id);
                const allChineseMeanings = [...new Set(group.words.map(w => w.chinese_meaning).filter(m => m))];
                const mergedChinese = allChineseMeanings.join('; ');
                
                html += `
                    <div class="duplicate-group preview">
                        <div class="group-header">
                            <h5>单词: "${group.english_word}" (${group.total_count} 个重复)</h5>
                        </div>
                        <div class="merge-preview">
                            <div class="will-keep">
                                <span class="label keep">🛡️ 将保留:</span>
                                <div class="word-info">
                                    <span class="word-id">ID: ${bestWord.id}</span>
                                    <span class="word-chinese">新释义: ${mergedChinese}</span>
                                    <span class="word-stats">
                                        答题: ${bestWord.record_count} | 用户: ${bestWord.user_count} | 计划: ${bestWord.plan_count}
                                    </span>
                                </div>
                            </div>
                            <div class="will-merge">
                                <span class="label merge">🔄 将合并:</span>
                                <div class="merge-words">
                                    ${mergeWords.map(word => `
                                        <span class="merge-word">ID: ${word.id} (${word.chinese_meaning})</span>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += `</div>`;
            listContainer.innerHTML = html;
        }
        
        // 显示自动合并确认模态框
        function showAutoMergeModal() {
            const content = document.getElementById('mergeWordsContent');
            content.innerHTML = `
                <div class="merge-info">
                    <h4>🤖 智能合并所有重复单词</h4>
                    <div class="auto-merge-info">
                        <p><strong>合并策略：</strong></p>
                        <ul>
                            <li>🎯 自动保留学习记录最多的单词ID</li>
                            <li>📚 智能合并所有中文释义（用分号分隔）</li>
                            <li>🔄 联动更新用户学习记录、答题记录、学习计划</li>
                            <li>🗑️ 删除重复的单词记录</li>
                        </ul>
                    </div>
                    <div class="warning-note">
                        <strong>⚠️ 重要提醒：</strong>
                        <ul>
                            <li>此操作将处理所有发现的重复单词</li>
                            <li>自动选择最佳保留策略，无需手动选择</li>
                            <li>操作完成后将显示详细的合并报告</li>
                            <li><strong>此操作不可逆，请谨慎确认！</strong></li>
                        </ul>
                    </div>
                </div>
            `;
            
            document.getElementById('mergeWordsModal').style.display = 'block';
        }
        
        // 执行智能合并
        function executeMergeWords() {
            const password = document.getElementById('mergeAdminPassword').value;
            if (!password) {
                showMessage('请输入管理员密码', 'error');
                return;
            }
            
            const confirmMessage = '确认执行智能合并？这将处理所有重复单词并影响相关数据！';
            if (!confirm(confirmMessage)) {
                return;
            }
            
            console.log('开始执行智能合并...'); // 调试信息
            showMessage('🤖 正在执行智能合并...', 'info');
            
            fetch('/api/admin/auto_merge_duplicate_words', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    admin_password: password
                })
            })
            .then(response => {
                console.log('API响应状态:', response.status); // 调试信息
                return response.json();
            })
            .then(data => {
                console.log('API响应数据:', data); // 调试信息
                if (data.success) {
                    showMessage(`✅ ${data.message}`, 'success');
                    closeMergeWordsModal();
                    
                    // 显示合并结果
                    if (data.merged_groups && data.merged_groups.length > 0) {
                        showMergeResults(data.merged_groups);
                    }
                    
                    // 重新加载数据
                    loadDuplicateWords();
                    if (currentTable === 'word') {
                        loadTableData();
                    }
                } else {
                    showMessage(`❌ 合并失败: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                console.error('智能合并异常:', error);
                showMessage('❌ 网络错误，请稍后重试', 'error');
            });
        }
        
        // 显示合并结果
        function showMergeResults(mergedGroups) {
            let resultHtml = `
                <div class="merge-results">
                    <h4>🎉 合并完成！处理结果：</h4>
                    <div class="results-summary">
                        <span class="result-stat">处理组数: ${mergedGroups.length}</span>
                        <span class="result-stat">删除重复: ${mergedGroups.reduce((sum, g) => sum + g.merged_word_ids.length, 0)} 个</span>
                        <span class="result-stat">总记录数: ${mergedGroups.reduce((sum, g) => sum + g.total_records, 0)}</span>
                    </div>
                    <div class="results-details">
            `;
            
            mergedGroups.forEach(group => {
                resultHtml += `
                    <div class="result-item">
                        <span class="result-word">${group.english_word}</span>
                        <span class="result-kept">保留ID: ${group.kept_word_id}</span>
                        <span class="result-merged">合并: ${group.merged_word_ids.join(', ')}</span>
                        <span class="result-chinese">${group.merged_chinese}</span>
                    </div>
                `;
            });
            
            resultHtml += `
                    </div>
                </div>
            `;
            
            // 临时显示结果
            const originalHtml = document.getElementById('duplicateWordsList').innerHTML;
            document.getElementById('duplicateWordsList').innerHTML = resultHtml;
            
            // 3秒后恢复原界面
            setTimeout(() => {
                document.getElementById('duplicateWordsList').innerHTML = originalHtml;
            }, 5000);
        }
        
        // 测试API连接
        function testMergeAPI() {
            const password = prompt('请输入管理员密码进行测试:');
            if (!password) return;
            
            console.log('开始测试API连接...');
            showMessage('🧪 测试API连接中...', 'info');
            
            fetch('/api/admin/test_merge', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    admin_password: password
                })
            })
            .then(response => {
                console.log('测试API响应状态:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('测试API响应数据:', data);
                if (data.success) {
                    showMessage(`✅ API测试成功: ${data.message}`, 'success');
                } else {
                    showMessage(`❌ API测试失败: ${data.error}`, 'error');
                    console.error('API测试详情:', data.details);
                }
            })
            .catch(error => {
                console.error('API测试异常:', error);
                showMessage('❌ API测试网络错误', 'error');
            });
        }
    </script>
</body>
</html>
