<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词学习应用</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .welcome-container {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        h1 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 2rem;
        }
        
        .description {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .features {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #eee;
        }
        
        .feature {
            margin: 1rem 0;
            color: #666;
        }
        
        .system-status {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .status-ok {
            color: #28a745;
        }
        
        .status-warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="logo">📚</div>
        <h1>单词学习应用</h1>
        <p class="description">
            智能化的英语单词学习平台，提供个性化学习计划、记忆辅助和进度跟踪功能。
        </p>
        
        <div class="action-buttons">
            <a href="{{ url_for('login_page') }}" class="btn btn-primary">登录</a>
            <a href="{{ url_for('register_page') }}" class="btn btn-secondary">注册</a>
        </div>
        
        <div class="features">
            <div class="feature">✅ 智能学习计划生成</div>
            <div class="feature">🧠 AI记忆辅助</div>
            <div class="feature">📊 学习进度跟踪</div>
            <div class="feature">📱 响应式设计</div>
        </div>
        
        <div class="system-status">
            <p><strong>系统状态</strong></p>
            <p class="status-ok">✅ 应用运行正常</p>
            <p class="status-ok">✅ 数据库连接正常</p>
            <p><small>版本: v3.0 MVP | 架构: 模块化重构版</small></p>
        </div>
    </div>
</body>
</html>